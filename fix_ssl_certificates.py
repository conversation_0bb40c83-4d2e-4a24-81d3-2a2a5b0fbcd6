#!/usr/bin/env python3
"""
修复SSL证书问题的脚本
解决ElevenLabs MCP服务器启动时的SSL证书错误
"""

import os
import sys
import subprocess
import logging
from pathlib import Path

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def find_ssl_cert_file():
    """查找系统中的SSL证书文件"""
    possible_paths = [
        "/etc/ssl/certs/ca-certificates.crt",  # Ubuntu/Debian
        "/etc/pki/tls/certs/ca-bundle.crt",   # CentOS/RHEL
        "/etc/ssl/ca-bundle.pem",             # openSUSE
        "/usr/local/share/certs/ca-root-nss.crt",  # FreeBSD
        "/etc/ssl/cert.pem",                  # macOS (Homebrew)
        "/usr/local/etc/openssl/cert.pem",    # macOS (Homebrew OpenSSL)
        "/opt/homebrew/etc/openssl/cert.pem", # macOS (Apple Silicon Homebrew)
        "/System/Library/OpenSSL/certs/cert.pem",  # macOS System
    ]

    for path in possible_paths:
        if Path(path).exists():
            logger.info(f"✅ 找到SSL证书文件: {path}")
            return path

    return None

def install_certificates_macos():
    """在macOS上安装证书"""
    try:
        # 检查是否安装了Homebrew
        result = subprocess.run(['which', 'brew'], capture_output=True, text=True)
        if result.returncode == 0:
            logger.info("🍺 检测到Homebrew，尝试安装ca-certificates...")

            # 安装ca-certificates
            subprocess.run(['brew', 'install', 'ca-certificates'], check=True)
            logger.info("✅ ca-certificates安装完成")

            # 更新证书
            subprocess.run(['brew', 'install', 'openssl'], check=True)
            logger.info("✅ OpenSSL安装/更新完成")

            return True
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ Homebrew安装失败: {e}")
    except FileNotFoundError:
        logger.info("ℹ️  未检测到Homebrew")

    return False

def download_certificates():
    """下载Mozilla CA证书包"""
    try:
        import urllib.request
        import ssl

        # 创建证书目录
        cert_dir = Path.home() / '.ssl'
        cert_dir.mkdir(exist_ok=True)
        cert_file = cert_dir / 'cacert.pem'

        logger.info("📥 下载Mozilla CA证书包...")

        # 禁用SSL验证来下载证书（这是安全的，因为我们正在下载证书本身）
        ssl_context = ssl.create_default_context()
        ssl_context.check_hostname = False
        ssl_context.verify_mode = ssl.CERT_NONE

        url = "https://curl.se/ca/cacert.pem"
        with urllib.request.urlopen(url, context=ssl_context) as response:
            with open(cert_file, 'wb') as f:
                f.write(response.read())

        logger.info(f"✅ 证书下载完成: {cert_file}")
        return str(cert_file)

    except Exception as e:
        logger.error(f"❌ 下载证书失败: {e}")
        return None

def create_env_file(cert_path):
    """创建环境变量文件"""
    env_file = Path('.env')

    # 读取现有的.env文件内容
    existing_content = ""
    if env_file.exists():
        with open(env_file, 'r') as f:
            existing_content = f.read()

    # 检查是否已经有SSL_CERT_FILE设置
    lines = existing_content.split('\n')
    ssl_cert_line_found = False

    for i, line in enumerate(lines):
        if line.startswith('SSL_CERT_FILE='):
            lines[i] = f'SSL_CERT_FILE={cert_path}'
            ssl_cert_line_found = True
            break

    if not ssl_cert_line_found:
        lines.append(f'SSL_CERT_FILE={cert_path}')

    # 写入.env文件
    with open(env_file, 'w') as f:
        f.write('\n'.join(lines))

    logger.info(f"✅ 环境变量文件已更新: {env_file}")

def create_shell_script():
    """创建启动脚本"""
    script_content = '''#!/bin/bash
# ElevenLabs MCP服务器启动脚本

# 设置SSL证书路径
if [ -f ".env" ]; then
    export $(cat .env | grep -v '^#' | xargs)
fi

# 设置ElevenLabs API密钥
export ELEVENLABS_API_KEY="***************************************************"

# 启动ElevenLabs MCP服务器
echo "🚀 启动ElevenLabs MCP服务器..."
echo "📋 SSL证书路径: $SSL_CERT_FILE"
echo "🔑 API密钥: ${ELEVENLABS_API_KEY:0:20}..."

uvx elevenlabs-mcp
'''

    script_file = Path('start_elevenlabs_mcp.sh')
    with open(script_file, 'w') as f:
        f.write(script_content)

    # 设置执行权限
    os.chmod(script_file, 0o755)

    logger.info(f"✅ 启动脚本已创建: {script_file}")

def main():
    """主函数"""
    logger.info("🔧 开始修复SSL证书问题...")
    logger.info("="*60)

    # 1. 查找现有的SSL证书文件
    cert_path = find_ssl_cert_file()

    if not cert_path:
        logger.info("⚠️  未找到系统SSL证书文件")

        # 2. 尝试在macOS上安装证书
        if sys.platform == "darwin":
            logger.info("🍎 检测到macOS系统，尝试安装证书...")
            if install_certificates_macos():
                cert_path = find_ssl_cert_file()

        # 3. 如果仍然没有找到，下载证书
        if not cert_path:
            logger.info("📥 尝试下载Mozilla CA证书包...")
            cert_path = download_certificates()

    if not cert_path:
        logger.error("❌ 无法获取SSL证书文件")
        return False

    # 4. 创建环境变量文件
    create_env_file(cert_path)

    # 5. 创建启动脚本
    create_shell_script()

    logger.info("\n" + "="*60)
    logger.info("🎉 SSL证书问题修复完成！")
    logger.info("="*60)
    logger.info("📝 下一步操作:")
    logger.info("1. 运行启动脚本: ./start_elevenlabs_mcp.sh")
    logger.info("2. 或者手动设置环境变量:")
    logger.info(f"   export SSL_CERT_FILE={cert_path}")
    logger.info("   export ELEVENLABS_API_KEY=***************************************************")
    logger.info("   uvx elevenlabs-mcp")

    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
