#!/usr/bin/env python3
"""
调试脚本：检查 Vidu MCP 服务器的实际可用工具
"""
import asyncio
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.services.fastmcp_server import fastmcp_manager

async def debug_vidu_tools():
    """调试 Vidu MCP 服务器的工具"""
    print("🔍 开始调试 Vidu MCP 服务器工具...")
    
    try:
        # 初始化 MCP 管理器
        if not fastmcp_manager._initialized:
            print("📦 初始化 MCP 管理器...")
            await fastmcp_manager.initialize()
        
        # 检查可用服务器
        available_servers = fastmcp_manager.get_available_servers()
        print(f"📋 可用服务器: {available_servers}")
        
        # 检查 Vidu 服务器状态
        if 'vidu' not in available_servers:
            print("❌ Vidu 服务器不可用")
            if 'vidu' in fastmcp_manager.server_info:
                server_info = fastmcp_manager.server_info['vidu']
                print(f"   连接状态: {server_info.is_connected}")
                print(f"   最后错误: {server_info.last_error}")
            return
        
        print("✅ Vidu 服务器可用")
        
        # 获取 Vidu 服务器的工具列表
        vidu_tools = fastmcp_manager.get_server_tools('vidu')
        print(f"🔧 Vidu 服务器工具列表: {vidu_tools}")
        
        # 动态获取最新工具列表
        print("🔄 动态获取 Vidu 工具列表...")
        dynamic_tools = await fastmcp_manager.get_tools_for_server('vidu')
        print(f"🔧 动态工具列表: {dynamic_tools}")
        
        # 检查服务器能力
        vidu_capabilities = fastmcp_manager.get_server_capabilities('vidu')
        print(f"⚡ Vidu 服务器能力: {vidu_capabilities}")
        
        # 检查服务器信息
        if 'vidu' in fastmcp_manager.server_info:
            server_info = fastmcp_manager.server_info['vidu']
            print(f"📊 服务器详细信息:")
            print(f"   名称: {server_info.name}")
            print(f"   连接状态: {server_info.is_connected}")
            print(f"   可用工具: {server_info.available_tools}")
            print(f"   能力: {server_info.capabilities}")
            print(f"   最后错误: {server_info.last_error}")
        
    except Exception as e:
        print(f"❌ 调试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(debug_vidu_tools())
