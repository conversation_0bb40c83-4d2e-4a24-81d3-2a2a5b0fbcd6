#!/usr/bin/env python3
"""
DaVinci AI Co-pilot Pro 工作流程示例
展示如何在实际项目中使用AI辅助功能
"""

import sys
import asyncio
import json
from pathlib import Path
from typing import List, Dict, Any

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.davinci.resolve_api import resolve_api
from src.core import get_config

class WorkflowExamples:
    """工作流程示例类"""

    def __init__(self):
        self.resolve_api = resolve_api
        self.config = get_config()

    async def initialize(self):
        """初始化连接"""
        print("🔄 初始化DaVinci Resolve连接...")
        success = await self.resolve_api.initialize()
        if not success:
            raise RuntimeError("无法连接到DaVinci Resolve")
        print("✅ 连接成功!")
        return success

    async def example_1_basic_frame_capture(self):
        """示例1: 基础静帧捕获工作流"""
        print("\n" + "="*60)
        print("📸 示例1: 基础静帧捕获工作流")
        print("="*60)

        # 1. 检查静帧功能状态
        print("🔍 检查静帧功能状态...")
        status = await self.resolve_api.get_frame_export_status()

        if not status.get('success'):
            print(f"❌ 静帧功能不可用: {status.get('message')}")
            return False

        data = status.get('data', {})
        print(f"✅ 静帧功能可用")
        print(f"   时间线状态: {'已加载' if data.get('timeline_loaded') else '未加载'}")
        print(f"   导出格式: {data.get('export_format', 'PNG')}")

        # 2. 获取播放头信息
        print("📍 获取播放头信息...")
        playhead_info = await self.resolve_api.get_playhead_info()

        if playhead_info.get('success'):
            data = playhead_info.get('data', {})
            print(f"   当前帧: {data.get('current_frame', '-')}")
            print(f"   时间码: {data.get('current_timecode', '-')}")

        # 3. 导出当前帧
        print("📸 导出当前帧...")
        frame_result = await self.resolve_api.export_current_frame(
            output_dir="./output/frames",
            filename=f"frame_{data.get('current_frame', 'unknown')}.png"
        )

        if frame_result.get('success'):
            print(f"✅ 静帧导出成功: {frame_result.get('data', {}).get('file_path')}")
        else:
            print(f"❌ 静帧导出失败: {frame_result.get('message')}")

        # 4. 导出给AI使用
        print("🤖 导出帧给AI使用...")
        ai_frame_result = await self.resolve_api.export_frame_for_ai("image_generation")

        if ai_frame_result.get('success'):
            print(f"✅ AI静帧导出成功: {ai_frame_result.get('data', {}).get('file_path')}")
        else:
            print(f"❌ AI静帧导出失败: {ai_frame_result.get('message')}")

        return True

    async def example_2_ai_content_analysis(self):
        """示例2: AI内容分析工作流"""
        print("\n" + "="*60)
        print("🤖 示例2: AI内容分析工作流")
        print("="*60)

        # 1. 综合内容分析
        print("🔍 执行综合内容分析...")
        analysis_result = await self.resolve_api.analyze_timeline_content(
            analysis_type="comprehensive",
            confidence_threshold=0.7,
            auto_apply=False
        )

        if analysis_result.get('success'):
            data = analysis_result.get('data', {})
            print("✅ 内容分析完成")
            print(f"   分析类型: {data.get('analysis_type', '综合分析')}")
            print(f"   置信度阈值: {data.get('confidence_threshold', 0.7)}")

            # 显示分析结果摘要
            if 'analysis' in data:
                analysis = data['analysis']
                print("📊 分析结果摘要:")
                for key, value in analysis.items():
                    if isinstance(value, (str, int, float)):
                        print(f"   {key}: {value}")
        else:
            print(f"❌ 内容分析失败: {analysis_result.get('message')}")

        # 2. 场景检测
        print("\n🎬 执行场景检测...")
        scene_result = await self.resolve_api.analyze_timeline_content(
            analysis_type="scene_detection",
            confidence_threshold=0.8,
            auto_apply=True
        )

        if scene_result.get('success'):
            print("✅ 场景检测完成")
            data = scene_result.get('data', {})
            if 'markers_applied' in data:
                print(f"   自动添加标记数: {data['markers_applied']}")
        else:
            print(f"❌ 场景检测失败: {scene_result.get('message')}")

        return True

    async def example_3_subtitle_generation(self):
        """示例3: 字幕生成工作流"""
        print("\n" + "="*60)
        print("📝 示例3: 字幕生成工作流")
        print("="*60)

        # 1. 提取音频
        print("🎵 提取音频...")
        audio_result = await self.resolve_api.extract_audio_for_ai(track_index=1)

        if audio_result.get('success'):
            audio_path = audio_result.get('data', {}).get('audio_path')
            print(f"✅ 音频提取成功: {audio_path}")
        else:
            print(f"❌ 音频提取失败: {audio_result.get('message')}")
            return False

        # 2. 生成字幕
        print("📝 生成AI字幕...")
        subtitle_result = await self.resolve_api.generate_ai_subtitles(
            track_index=1,
            language="zh-CN",
            ai_service="deepseek"
        )

        if subtitle_result.get('success'):
            data = subtitle_result.get('data', {})
            subtitles = data.get('subtitles', [])
            print(f"✅ 字幕生成成功，共 {len(subtitles)} 条字幕")

            # 显示前几条字幕
            print("📋 字幕预览:")
            for i, subtitle in enumerate(subtitles[:3]):
                print(f"   {i+1}. {subtitle.get('start_time', 0)}s - {subtitle.get('end_time', 0)}s")
                print(f"      {subtitle.get('text', '')}")

            if len(subtitles) > 3:
                print(f"   ... 还有 {len(subtitles) - 3} 条字幕")

            # 保存字幕文件
            await self.save_subtitles_to_srt(subtitles, "./output/generated_subtitles.srt")

        else:
            print(f"❌ 字幕生成失败: {subtitle_result.get('message')}")

        return True

    async def example_4_smart_markers(self):
        """示例4: 智能标记工作流"""
        print("\n" + "="*60)
        print("🏷️ 示例4: 智能标记工作流")
        print("="*60)

        # 1. 添加内容标记
        print("🏷️ 添加内容标记...")
        content_markers = await self.resolve_api.analyze_timeline_content(
            analysis_type="smart_marking",
            confidence_threshold=0.8,
            auto_apply=True,
            marker_type="content"
        )

        if content_markers.get('success'):
            data = content_markers.get('data', {})
            print(f"✅ 内容标记添加成功，应用了 {data.get('markers_applied', 0)} 个标记")

        # 2. 添加场景切换标记
        print("🎬 添加场景切换标记...")
        scene_markers = await self.resolve_api.analyze_timeline_content(
            analysis_type="smart_marking",
            confidence_threshold=0.9,
            auto_apply=True,
            marker_type="scene_change"
        )

        if scene_markers.get('success'):
            data = scene_markers.get('data', {})
            print(f"✅ 场景切换标记添加成功，应用了 {data.get('markers_applied', 0)} 个标记")

        # 3. 添加质量问题标记
        print("⚠️ 添加质量问题标记...")
        quality_markers = await self.resolve_api.analyze_timeline_content(
            analysis_type="smart_marking",
            confidence_threshold=0.7,
            auto_apply=True,
            marker_type="quality_issue"
        )

        if quality_markers.get('success'):
            data = quality_markers.get('data', {})
            print(f"✅ 质量问题标记添加成功，应用了 {data.get('markers_applied', 0)} 个标记")

        return True

    async def example_5_batch_processing(self):
        """示例5: 批量处理工作流"""
        print("\n" + "="*60)
        print("⚡ 示例5: 批量处理工作流")
        print("="*60)

        # 1. 获取项目列表
        print("📁 获取项目列表...")
        projects = await self.resolve_api.get_project_list()

        if not projects:
            print("❌ 没有找到项目")
            return False

        print(f"✅ 发现 {len(projects)} 个项目")

        # 2. 批量处理前3个项目（示例）
        process_count = min(3, len(projects))
        print(f"🔄 批量处理前 {process_count} 个项目...")

        results = []
        for i, project_name in enumerate(projects[:process_count]):
            print(f"\n📋 处理项目 {i+1}/{process_count}: {project_name}")

            # 加载项目
            load_result = await self.resolve_api.load_project(project_name)
            if not load_result.get('success'):
                print(f"❌ 加载项目失败: {load_result.get('message')}")
                continue

            print(f"✅ 项目加载成功")

            # 执行基本分析
            analysis_result = await self.resolve_api.analyze_timeline_content(
                analysis_type="comprehensive",
                confidence_threshold=0.7,
                auto_apply=False
            )

            # 导出关键帧
            frame_result = await self.resolve_api.export_current_frame(
                output_dir=f"./output/batch/{project_name}",
                filename="key_frame.png"
            )

            # 保存结果
            project_result = {
                "project_name": project_name,
                "analysis_success": analysis_result.get('success', False),
                "frame_export_success": frame_result.get('success', False),
                "analysis_data": analysis_result.get('data', {}),
                "frame_path": frame_result.get('data', {}).get('file_path')
            }
            results.append(project_result)

            print(f"✅ 项目 {project_name} 处理完成")

        # 3. 生成批处理报告
        await self.generate_batch_report(results)

        return True

    async def save_subtitles_to_srt(self, subtitles: List[Dict], output_path: str):
        """保存字幕到SRT文件"""
        try:
            output_file = Path(output_path)
            output_file.parent.mkdir(parents=True, exist_ok=True)

            srt_content = []
            for i, subtitle in enumerate(subtitles, 1):
                start_time = self.format_srt_time(subtitle.get('start_time', 0))
                end_time = self.format_srt_time(subtitle.get('end_time', 0))
                text = subtitle.get('text', '')

                srt_content.append(f"{i}")
                srt_content.append(f"{start_time} --> {end_time}")
                srt_content.append(text)
                srt_content.append("")  # 空行

            with open(output_file, 'w', encoding='utf-8') as f:
                f.write('\n'.join(srt_content))

            print(f"✅ 字幕文件保存成功: {output_path}")

        except Exception as e:
            print(f"❌ 保存字幕文件失败: {e}")

    def format_srt_time(self, seconds: float) -> str:
        """格式化SRT时间格式"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        ms = int((seconds % 1) * 1000)
        return f"{hours:02d}:{minutes:02d}:{secs:02d},{ms:03d}"

    async def generate_batch_report(self, results: List[Dict]):
        """生成批处理报告"""
        try:
            report_path = Path("./output/batch_processing_report.json")
            report_path.parent.mkdir(parents=True, exist_ok=True)

            report = {
                "timestamp": str(asyncio.get_event_loop().time()),
                "total_projects": len(results),
                "successful_analyses": sum(1 for r in results if r['analysis_success']),
                "successful_exports": sum(1 for r in results if r['frame_export_success']),
                "results": results
            }

            with open(report_path, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)

            print(f"✅ 批处理报告生成成功: {report_path}")
            print(f"📊 处理统计:")
            print(f"   总项目数: {report['total_projects']}")
            print(f"   分析成功: {report['successful_analyses']}")
            print(f"   导出成功: {report['successful_exports']}")

        except Exception as e:
            print(f"❌ 生成批处理报告失败: {e}")

    async def run_all_examples(self):
        """运行所有示例"""
        print("🎬 DaVinci AI Co-pilot Pro 工作流程示例")
        print("="*60)

        try:
            # 初始化连接
            await self.initialize()

            # 运行示例
            examples = [
                ("基础静帧捕获", self.example_1_basic_frame_capture),
                ("AI内容分析", self.example_2_ai_content_analysis),
                ("字幕生成", self.example_3_subtitle_generation),
                ("智能标记", self.example_4_smart_markers),
                ("批量处理", self.example_5_batch_processing)
            ]

            success_count = 0
            for name, example_func in examples:
                try:
                    print(f"\n🚀 开始执行: {name}")
                    success = await example_func()
                    if success:
                        success_count += 1
                        print(f"✅ {name} 执行成功")
                    else:
                        print(f"❌ {name} 执行失败")
                except Exception as e:
                    print(f"💥 {name} 执行异常: {e}")

            # 总结
            print(f"\n" + "="*60)
            print(f"📊 示例执行总结")
            print(f"="*60)
            print(f"总示例数: {len(examples)}")
            print(f"成功执行: {success_count}")
            print(f"失败数量: {len(examples) - success_count}")
            print(f"成功率: {(success_count/len(examples)*100):.1f}%")

            if success_count == len(examples):
                print("🎉 所有示例执行成功!")
            else:
                print("⚠️ 部分示例执行失败，请检查日志")

        except Exception as e:
            print(f"💥 示例执行过程中发生异常: {e}")
            import traceback
            traceback.print_exc()

async def main():
    """主函数"""
    examples = WorkflowExamples()
    await examples.run_all_examples()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n⏹️ 示例执行被用户中断")
    except Exception as e:
        print(f"\n\n💥 程序执行异常: {e}")
        import traceback
        traceback.print_exc()
