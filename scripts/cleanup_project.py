#!/usr/bin/env python3
"""
项目清理脚本
自动清理过时文件、优化导入语句、移除未使用的代码
"""

import os
import sys
import ast
import re
from pathlib import Path
from typing import Set, List, Dict

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

class ImportAnalyzer(ast.NodeVisitor):
    """分析Python文件中的导入和使用情况"""

    def __init__(self):
        self.imports = set()
        self.used_names = set()
        self.import_lines = {}

    def visit_Import(self, node):
        for alias in node.names:
            name = alias.asname if alias.asname else alias.name
            self.imports.add(name)
            self.import_lines[name] = node.lineno
        self.generic_visit(node)

    def visit_ImportFrom(self, node):
        for alias in node.names:
            name = alias.asname if alias.asname else alias.name
            self.imports.add(name)
            self.import_lines[name] = node.lineno
        self.generic_visit(node)

    def visit_Name(self, node):
        self.used_names.add(node.id)
        self.generic_visit(node)

    def visit_Attribute(self, node):
        if isinstance(node.value, ast.Name):
            self.used_names.add(node.value.id)
        self.generic_visit(node)

def analyze_file_imports(file_path: Path) -> Dict[str, any]:
    """分析文件的导入使用情况"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        tree = ast.parse(content)
        analyzer = ImportAnalyzer()
        analyzer.visit(tree)

        unused_imports = analyzer.imports - analyzer.used_names

        return {
            'file': file_path,
            'imports': analyzer.imports,
            'used_names': analyzer.used_names,
            'unused_imports': unused_imports,
            'import_lines': analyzer.import_lines
        }
    except Exception as e:
        print(f"Error analyzing {file_path}: {e}")
        return None

def find_python_files(directory: Path) -> List[Path]:
    """查找所有Python文件"""
    python_files = []
    for file_path in directory.rglob("*.py"):
        # 跳过虚拟环境和缓存目录
        if any(part in str(file_path) for part in ['venv', '__pycache__', '.git']):
            continue
        python_files.append(file_path)
    return python_files

def clean_unused_imports():
    """清理未使用的导入"""
    print("🔍 分析项目中的导入使用情况...")

    src_dir = project_root / "src"
    python_files = find_python_files(src_dir)

    total_unused = 0

    for file_path in python_files:
        analysis = analyze_file_imports(file_path)
        if not analysis:
            continue

        unused = analysis['unused_imports']
        if unused:
            print(f"\n📁 {file_path.relative_to(project_root)}")
            print(f"   未使用的导入: {', '.join(unused)}")
            total_unused += len(unused)

    print(f"\n📊 总计发现 {total_unused} 个未使用的导入")

def remove_old_files():
    """移除过时的文件"""
    print("🗑️  移除过时文件...")

    # 要移除的文件模式
    patterns_to_remove = [
        "*_backup.py",
        "*_old.py",
        "test_*.py.bak",
        "*.pyc",
        "*.pyo"
    ]

    removed_count = 0

    for pattern in patterns_to_remove:
        for file_path in project_root.rglob(pattern):
            if 'venv' not in str(file_path):
                try:
                    file_path.unlink()
                    print(f"   ✅ 删除: {file_path.relative_to(project_root)}")
                    removed_count += 1
                except Exception as e:
                    print(f"   ❌ 删除失败 {file_path}: {e}")

    print(f"📊 总计删除 {removed_count} 个文件")

def clean_cache_directories():
    """清理缓存目录"""
    print("🧹 清理缓存目录...")

    cache_patterns = [
        "__pycache__",
        "*.pyc",
        ".pytest_cache"
    ]

    removed_count = 0

    for pattern in cache_patterns:
        for path in project_root.rglob(pattern):
            if 'venv' not in str(path):
                try:
                    if path.is_dir():
                        import shutil
                        shutil.rmtree(path)
                        print(f"   ✅ 删除目录: {path.relative_to(project_root)}")
                    else:
                        path.unlink()
                        print(f"   ✅ 删除文件: {path.relative_to(project_root)}")
                    removed_count += 1
                except Exception as e:
                    print(f"   ❌ 删除失败 {path}: {e}")

    print(f"📊 总计清理 {removed_count} 个缓存项")

def check_api_consistency():
    """检查API一致性"""
    print("🔍 检查API一致性...")

    # 检查DaVinci API导入
    davinci_files = list((project_root / "src").rglob("*.py"))

    old_imports = []
    new_imports = []

    for file_path in davinci_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # 检查旧版导入
            if "from src.davinci.resolve_api import" in content and "DaVinciResolveAPI" in content:
                old_imports.append(file_path)

            # 检查新版导入
            if "from src.davinci import" in content:
                new_imports.append(file_path)

        except Exception as e:
            print(f"Error reading {file_path}: {e}")

    print(f"📊 发现 {len(old_imports)} 个文件使用旧版导入")
    print(f"📊 发现 {len(new_imports)} 个文件使用新版导入")

    if old_imports:
        print("\n需要更新的文件:")
        for file_path in old_imports:
            print(f"   📁 {file_path.relative_to(project_root)}")

def optimize_imports():
    """优化导入语句"""
    print("⚡ 优化导入语句...")

    # 这里可以添加更多的导入优化逻辑
    # 例如：按字母顺序排序、分组等

    print("✅ 导入优化完成")

def generate_cleanup_report():
    """生成清理报告"""
    print("\n" + "="*50)
    print("📋 项目清理报告")
    print("="*50)

    # 统计项目文件
    src_files = len(list((project_root / "src").rglob("*.py")))
    test_files = len(list((project_root / "tests").rglob("*.py")))

    print(f"📁 源代码文件: {src_files}")
    print(f"🧪 测试文件: {test_files}")

    # 检查配置文件
    config_files = [
        "pyproject.toml",
        "requirements.txt",
        ".gitignore",
        ".pre-commit-config.yaml"
    ]

    print(f"\n📋 配置文件状态:")
    for config_file in config_files:
        file_path = project_root / config_file
        status = "✅ 存在" if file_path.exists() else "❌ 缺失"
        print(f"   {config_file}: {status}")

    print(f"\n🎉 项目清理完成!")

def main():
    """主函数"""
    print("🧹 DaVinci AI Co-pilot Pro 项目清理工具")
    print("="*50)

    # 执行清理步骤
    remove_old_files()
    clean_cache_directories()
    clean_unused_imports()
    check_api_consistency()
    optimize_imports()
    generate_cleanup_report()

if __name__ == "__main__":
    main()
