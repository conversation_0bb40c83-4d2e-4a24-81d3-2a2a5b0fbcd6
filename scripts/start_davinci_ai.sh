#!/bin/bash
# DaVinci AI Co-pilot Pro 最终启动脚本
# 集成所有最佳实践，简化启动流程

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
print_success() { echo -e "${GREEN}✅ $1${NC}"; }
print_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
print_error() { echo -e "${RED}❌ $1${NC}"; }

echo -e "${BLUE}🎬 DaVinci AI Co-pilot Pro 启动器${NC}"
echo "=================================="

# 检测操作系统
case "$(uname -s)" in
    Darwin)
        OS="macOS"
        DAVINCI_APP="/Applications/DaVinci Resolve/DaVinci Resolve.app"
        PLUGIN_DIR="$HOME/Library/Application Support/Blackmagic Design/DaVinci Resolve/Fusion/Scripts/Utility/DaVinci AI Co-pilot Pro"
        ;;
    Linux)
        OS="Linux"
        DAVINCI_APP="/opt/resolve/bin/resolve"
        PLUGIN_DIR="$HOME/.local/share/DaVinciResolve/Fusion/Scripts/Utility/DaVinci AI Co-pilot Pro"
        ;;
    *)
        print_error "不支持的操作系统: $(uname -s)"
        exit 1
        ;;
esac

print_info "操作系统: $OS"

# 检查DaVinci Resolve是否安装
if [[ ! -e "$DAVINCI_APP" ]]; then
    print_error "未找到DaVinci Resolve: $DAVINCI_APP"
    print_warning "请先安装DaVinci Resolve 18.x 或更高版本"
    exit 1
fi
print_success "发现DaVinci Resolve"

# 检查插件是否已部署
if [[ ! -d "$PLUGIN_DIR" ]]; then
    print_error "插件未部署到: $PLUGIN_DIR"
    print_warning "请先运行部署脚本: python scripts/deploy_to_davinci.py"
    exit 1
fi
print_success "插件已部署"

# 检查DaVinci Resolve是否运行
if pgrep -f "DaVinci Resolve" > /dev/null; then
    print_success "DaVinci Resolve 正在运行"
else
    print_info "启动DaVinci Resolve..."
    if [[ "$OS" == "macOS" ]]; then
        open "$DAVINCI_APP"
    elif [[ "$OS" == "Linux" ]]; then
        "$DAVINCI_APP" &
    fi
    
    print_info "等待DaVinci Resolve启动..."
    for i in {1..60}; do
        if pgrep -f "DaVinci Resolve" > /dev/null; then
            print_success "DaVinci Resolve 启动成功"
            sleep 3  # 等待完全加载
            break
        fi
        sleep 1
        echo -n "."
    done
    echo ""
    
    if ! pgrep -f "DaVinci Resolve" > /dev/null; then
        print_error "DaVinci Resolve 启动超时"
        exit 1
    fi
fi

# 设置DaVinci Resolve环境变量
print_info "设置DaVinci Resolve环境变量..."
if [ -f "$HOME/.davinci_resolve_env" ]; then
    source "$HOME/.davinci_resolve_env"
    print_success "已加载环境变量文件"
else
    if [[ "$OS" == "macOS" ]]; then
        export RESOLVE_SCRIPT_API="/Library/Application Support/Blackmagic Design/DaVinci Resolve/Developer/Scripting"
        export RESOLVE_SCRIPT_LIB="/Applications/DaVinci Resolve/DaVinci Resolve.app/Contents/Libraries/Fusion/fusionscript.so"
        export PYTHONPATH="$PYTHONPATH:/Library/Application Support/Blackmagic Design/DaVinci Resolve/Developer/Scripting/Modules/"
    elif [[ "$OS" == "Linux" ]]; then
        export RESOLVE_SCRIPT_API="/opt/resolve/Developer/Scripting"
        export RESOLVE_SCRIPT_LIB="/opt/resolve/libs/Fusion/fusionscript.so"
        export PYTHONPATH="$PYTHONPATH:/opt/resolve/Developer/Scripting/Modules/"
    fi
    print_success "已设置默认环境变量"
fi

# 切换到插件目录
cd "$PLUGIN_DIR"

# 自动清理临时文件和缓存
print_info "自动清理临时文件和缓存..."
find . -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true
find . -name "*.pyc" -delete 2>/dev/null || true
find . -name "*.pyo" -delete 2>/dev/null || true
find . -name ".DS_Store" -delete 2>/dev/null || true
find . -name "Thumbs.db" -delete 2>/dev/null || true
find . -name "*.tmp" -delete 2>/dev/null || true
rm -rf logs temp cache output 2>/dev/null || true
rm -f .service_pid 2>/dev/null || true
print_success "自动清理完成"

# 检查虚拟环境
if [[ ! -d "davinci_ai_env" ]]; then
    print_info "创建Python虚拟环境..."
    python3 -m venv davinci_ai_env
    source "davinci_ai_env/bin/activate"
    pip install -r requirements.txt
    pip install psutil
    print_success "虚拟环境创建完成"
else
    source "davinci_ai_env/bin/activate"
    print_success "虚拟环境已激活"
fi

# 安装缺失的依赖
pip install psutil > /dev/null 2>&1

# 检查端口占用并自动清理
print_info "检查端口8000可用性..."
if lsof -i :8000 > /dev/null 2>&1; then
    print_warning "端口8000被占用，自动清理..."
    # 直接杀死占用端口的进程
    lsof -ti :8000 | xargs kill -9 2>/dev/null || true
    sleep 2

    # 如果仍被占用，尝试杀死相关Python进程
    if lsof -i :8000 > /dev/null 2>&1; then
        print_warning "端口仍被占用，清理Python进程..."
        pkill -f "python.*main.py" 2>/dev/null || true
        sleep 2

        if lsof -i :8000 > /dev/null 2>&1; then
            print_error "无法清理端口8000，请重启系统"
            exit 1
        fi
    fi
    print_success "端口清理完成"
else
    print_success "端口8000可用"
fi

# 创建必要的目录
mkdir -p logs temp output cache

# 启动服务
print_info "启动AI Co-pilot Pro服务..."
python src/main.py &
SERVICE_PID=$!

# 等待服务启动
print_info "等待服务启动..."
for i in {1..30}; do
    if curl -s http://127.0.0.1:8000/health > /dev/null 2>&1; then
        print_success "服务启动成功 (PID: $SERVICE_PID)"
        break
    fi
    sleep 1
    echo -n "."
done
echo ""

if ! curl -s http://127.0.0.1:8000/health > /dev/null 2>&1; then
    print_error "服务启动失败"
    kill $SERVICE_PID 2>/dev/null || true
    exit 1
fi

# 打开Web界面
print_info "打开Web界面..."
if [[ "$OS" == "macOS" ]]; then
    open http://127.0.0.1:8000
elif [[ "$OS" == "Linux" ]]; then
    if command -v xdg-open > /dev/null; then
        xdg-open http://127.0.0.1:8000
    else
        print_warning "请手动打开浏览器访问: http://127.0.0.1:8000"
    fi
fi

# 显示使用说明
echo ""
echo -e "${GREEN}🎉 启动完成!${NC}"
echo "=================================="
echo "📋 使用说明:"
echo "1. Web界面已在浏览器中打开"
echo "2. 点击'DaVinci集成'标签"
echo "3. 点击'连接DaVinci'按钮"
echo "4. 开始使用AI功能"
echo ""
echo "🛠️  管理命令:"
echo "• 停止服务: kill $SERVICE_PID"
echo "• 查看日志: tail -f '$PLUGIN_DIR/logs/app.log'"
echo "• 连接测试: python scripts/test_connection.py"
echo ""
echo -e "${GREEN}✅ DaVinci AI Co-pilot Pro 已准备就绪!${NC}"
echo "🌐 Web界面: http://127.0.0.1:8000"

# 保持脚本运行，显示日志
echo ""
print_info "按 Ctrl+C 停止服务"
echo "=================================="

# 等待用户中断
trap 'echo ""; print_info "正在停止服务..."; kill $SERVICE_PID 2>/dev/null; print_success "服务已停止"; exit 0' INT

# 显示实时日志
tail -f logs/app.log 2>/dev/null || wait $SERVICE_PID
