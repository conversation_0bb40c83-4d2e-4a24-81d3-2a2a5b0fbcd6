#!/usr/bin/env python3
"""
DaVinci Resolve插件部署脚本
自动将AI Co-pilot Pro部署到DaVinci Resolve Scripts目录
"""

import os
import sys
import shutil
import platform
from pathlib import Path
import subprocess
import json

class DaVinciDeployer:
    """DaVinci Resolve插件部署器"""

    def __init__(self):
        self.system = platform.system()
        self.project_root = Path(__file__).parent.parent
        self.scripts_dir = self.get_scripts_directory()
        self.plugin_dir = self.scripts_dir / "Utility" / "DaVinci AI Co-pilot Pro"

    def get_scripts_directory(self):
        """获取DaVinci Resolve Scripts目录"""
        if self.system == "Darwin":  # macOS
            return Path.home() / "Library" / "Application Support" / "Blackmagic Design" / "DaVinci Resolve" / "Fusion" / "Scripts"
        elif self.system == "Windows":
            return Path(os.environ.get("APPDATA", "")) / "Blackmagic Design" / "DaVinci Resolve" / "Support" / "Fusion" / "Scripts"
        elif self.system == "Linux":
            return Path.home() / ".local" / "share" / "DaVinciResolve" / "Fusion" / "Scripts"
        else:
            raise RuntimeError(f"不支持的操作系统: {self.system}")

    def print_step(self, step, message):
        """打印部署步骤"""
        print(f"📋 步骤 {step}: {message}")

    def print_success(self, message):
        """打印成功信息"""
        print(f"✅ {message}")

    def print_error(self, message):
        """打印错误信息"""
        print(f"❌ {message}")

    def print_warning(self, message):
        """打印警告信息"""
        print(f"⚠️  {message}")

    def check_davinci_resolve(self):
        """检查DaVinci Resolve是否安装"""
        self.print_step(1, "检查DaVinci Resolve安装")

        if self.system == "Darwin":
            resolve_path = Path("/Applications/DaVinci Resolve/DaVinci Resolve.app")
        elif self.system == "Windows":
            resolve_path = Path("C:/Program Files/Blackmagic Design/DaVinci Resolve/Resolve.exe")
        elif self.system == "Linux":
            resolve_path = Path("/opt/resolve/bin/resolve")

        if resolve_path.exists():
            self.print_success(f"发现DaVinci Resolve: {resolve_path}")
            return True
        else:
            self.print_error(f"未找到DaVinci Resolve: {resolve_path}")
            self.print_warning("请确保DaVinci Resolve已正确安装")
            return False

    def create_directories(self):
        """创建必要的目录结构"""
        self.print_step(2, "创建目录结构")

        try:
            # 创建Scripts目录（如果不存在）
            self.scripts_dir.mkdir(parents=True, exist_ok=True)
            self.print_success(f"Scripts目录: {self.scripts_dir}")

            # 创建Utility目录
            utility_dir = self.scripts_dir / "Utility"
            utility_dir.mkdir(exist_ok=True)
            self.print_success(f"Utility目录: {utility_dir}")

            # 创建插件目录
            self.plugin_dir.mkdir(exist_ok=True)
            self.print_success(f"插件目录: {self.plugin_dir}")

            return True
        except Exception as e:
            self.print_error(f"创建目录失败: {e}")
            return False

    def copy_project_files(self):
        """复制项目文件"""
        self.print_step(3, "复制项目文件")

        try:
            # 要复制的文件和目录
            items_to_copy = [
                "src",
                "web",
                "config",
                "requirements.txt",
                "pyproject.toml",
                "README.md",
                "run.sh",
                "setup.sh"
            ]

            # 要排除的目录
            exclude_dirs = {
                "__pycache__",
                ".pytest_cache",
                "venv",
                "davinci_ai_env",
                "logs",
                "temp",
                "output",
                "cache"
            }

            copied_count = 0
            for item_name in items_to_copy:
                source_path = self.project_root / item_name
                dest_path = self.plugin_dir / item_name

                if source_path.exists():
                    if source_path.is_file():
                        shutil.copy2(source_path, dest_path)
                        copied_count += 1
                        print(f"   📄 复制文件: {item_name}")
                    elif source_path.is_dir():
                        if dest_path.exists():
                            shutil.rmtree(dest_path)
                        shutil.copytree(
                            source_path,
                            dest_path,
                            ignore=shutil.ignore_patterns(*exclude_dirs)
                        )
                        copied_count += 1
                        print(f"   📁 复制目录: {item_name}")
                else:
                    self.print_warning(f"源文件不存在: {item_name}")

            self.print_success(f"复制完成，共 {copied_count} 个项目")
            return True

        except Exception as e:
            self.print_error(f"复制文件失败: {e}")
            return False

    def setup_virtual_environment(self):
        """设置虚拟环境"""
        self.print_step(4, "设置Python虚拟环境")

        try:
            venv_path = self.plugin_dir / "davinci_ai_env"

            # 创建虚拟环境
            if venv_path.exists():
                self.print_warning("虚拟环境已存在，删除旧环境")
                shutil.rmtree(venv_path)

            print("   🔄 创建虚拟环境...")
            subprocess.run([
                sys.executable, "-m", "venv", str(venv_path)
            ], check=True, capture_output=True)

            # 确定pip路径
            if self.system == "Windows":
                pip_path = venv_path / "Scripts" / "pip"
                python_path = venv_path / "Scripts" / "python"
            else:
                pip_path = venv_path / "bin" / "pip"
                python_path = venv_path / "bin" / "python"

            # 升级pip
            print("   🔄 升级pip...")
            subprocess.run([
                str(python_path), "-m", "pip", "install", "--upgrade", "pip"
            ], check=True, capture_output=True)

            # 安装依赖
            requirements_file = self.plugin_dir / "requirements.txt"
            if requirements_file.exists():
                print("   🔄 安装依赖包...")
                subprocess.run([
                    str(pip_path), "install", "-r", str(requirements_file)
                ], check=True, capture_output=True)

            self.print_success("虚拟环境设置完成")
            return True

        except subprocess.CalledProcessError as e:
            self.print_error(f"虚拟环境设置失败: {e}")
            return False
        except Exception as e:
            self.print_error(f"虚拟环境设置异常: {e}")
            return False

    def create_startup_scripts(self):
        """创建启动脚本"""
        self.print_step(5, "创建启动脚本")

        try:
            if self.system in ["Darwin", "Linux"]:
                # macOS/Linux启动脚本
                startup_script = self.plugin_dir / "start_davinci_ai.sh"
                script_content = f"""#!/bin/bash
# DaVinci AI Co-pilot Pro 启动脚本

# 设置工作目录 (使用引号处理空格)
cd "{self.plugin_dir}"

# 激活虚拟环境
source "davinci_ai_env/bin/activate"

# 启动服务
echo "🎬 启动DaVinci AI Co-pilot Pro..."
python src/main.py

echo "✅ 服务已启动在 http://127.0.0.1:8000"
"""
                startup_script.write_text(script_content)
                startup_script.chmod(0o755)

            elif self.system == "Windows":
                # Windows启动脚本
                startup_script = self.plugin_dir / "start_davinci_ai.bat"
                script_content = f"""@echo off
REM DaVinci AI Co-pilot Pro 启动脚本

REM 设置工作目录
cd /d "{self.plugin_dir}"

REM 激活虚拟环境
call davinci_ai_env\\Scripts\\activate

REM 启动服务
echo 🎬 启动DaVinci AI Co-pilot Pro...
python src\\main.py

echo ✅ 服务已启动在 http://127.0.0.1:8000
pause
"""
                startup_script.write_text(script_content, encoding='utf-8')

            self.print_success(f"启动脚本创建完成: {startup_script.name}")
            return True

        except Exception as e:
            self.print_error(f"创建启动脚本失败: {e}")
            return False

    def update_config(self):
        """更新配置文件"""
        self.print_step(6, "更新配置文件")

        try:
            config_file = self.plugin_dir / "config" / "config.json"

            if config_file.exists():
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)

                # 更新DaVinci Resolve路径
                if self.system == "Darwin":
                    resolve_path = "/Applications/DaVinci Resolve/DaVinci Resolve.app"
                elif self.system == "Windows":
                    resolve_path = "C:\\Program Files\\Blackmagic Design\\DaVinci Resolve\\Resolve.exe"
                elif self.system == "Linux":
                    resolve_path = "/opt/resolve/bin/resolve"

                config["davinci"]["resolve_path"] = resolve_path

                # 设置生产环境配置
                config["app"]["debug"] = False
                config["logging"]["level"] = "INFO"

                # 保存配置
                with open(config_file, 'w', encoding='utf-8') as f:
                    json.dump(config, f, indent=2, ensure_ascii=False)

                self.print_success("配置文件更新完成")
                return True
            else:
                self.print_warning("配置文件不存在，跳过更新")
                return True

        except Exception as e:
            self.print_error(f"更新配置文件失败: {e}")
            return False

    def create_uninstall_script(self):
        """创建卸载脚本"""
        try:
            if self.system in ["Darwin", "Linux"]:
                uninstall_script = self.plugin_dir / "uninstall.sh"
                script_content = f"""#!/bin/bash
# DaVinci AI Co-pilot Pro 卸载脚本

echo "🗑️  卸载DaVinci AI Co-pilot Pro..."

# 停止服务
pkill -f "python src/main.py" 2>/dev/null || true

# 删除插件目录
rm -rf "{self.plugin_dir}"

echo "✅ 卸载完成!"
"""
                uninstall_script.write_text(script_content)
                uninstall_script.chmod(0o755)

            elif self.system == "Windows":
                uninstall_script = self.plugin_dir / "uninstall.bat"
                script_content = f"""@echo off
REM DaVinci AI Co-pilot Pro 卸载脚本

echo 🗑️  卸载DaVinci AI Co-pilot Pro...

REM 停止服务
taskkill /f /im python.exe 2>nul

REM 删除插件目录
rmdir /s /q "{self.plugin_dir}"

echo ✅ 卸载完成!
pause
"""
                uninstall_script.write_text(script_content, encoding='utf-8')

            self.print_success(f"卸载脚本创建完成: {uninstall_script.name}")
            return True

        except Exception as e:
            self.print_error(f"创建卸载脚本失败: {e}")
            return False

    def print_deployment_summary(self):
        """打印部署总结"""
        print(f"\n{'='*60}")
        print("🎉 DaVinci AI Co-pilot Pro 部署完成!")
        print(f"{'='*60}")
        print(f"📁 插件位置: {self.plugin_dir}")
        print(f"🚀 启动脚本: {self.plugin_dir / ('start_davinci_ai.sh' if self.system != 'Windows' else 'start_davinci_ai.bat')}")
        print(f"🗑️  卸载脚本: {self.plugin_dir / ('uninstall.sh' if self.system != 'Windows' else 'uninstall.bat')}")
        print(f"\n📋 使用说明:")
        print(f"1. 启动DaVinci Resolve")
        print(f"2. 运行启动脚本启动AI Co-pilot Pro服务")
        print(f"3. 在浏览器中访问 http://127.0.0.1:8000")
        print(f"4. 在Web界面中点击'连接DaVinci'建立连接")
        print(f"\n💡 提示:")
        print(f"- 确保DaVinci Resolve在启动服务前已经运行")
        print(f"- 首次使用时可能需要配置AI服务API密钥")
        print(f"- 查看日志文件了解详细运行状态")
        print(f"{'='*60}")

    def deploy(self):
        """执行部署"""
        print("🎬 DaVinci AI Co-pilot Pro 部署开始")
        print(f"操作系统: {self.system}")
        print(f"项目根目录: {self.project_root}")
        print(f"目标目录: {self.plugin_dir}")

        steps = [
            self.check_davinci_resolve,
            self.create_directories,
            self.copy_project_files,
            self.setup_virtual_environment,
            self.create_startup_scripts,
            self.update_config,
            self.create_uninstall_script
        ]

        for i, step_func in enumerate(steps, 1):
            try:
                if not step_func():
                    self.print_error(f"部署在步骤 {i} 失败")
                    return False
            except Exception as e:
                self.print_error(f"步骤 {i} 发生异常: {e}")
                return False

        self.print_deployment_summary()
        return True

def main():
    """主函数"""
    try:
        deployer = DaVinciDeployer()
        success = deployer.deploy()

        if success:
            print("\n🎉 部署成功完成!")
            sys.exit(0)
        else:
            print("\n💥 部署失败!")
            sys.exit(1)

    except KeyboardInterrupt:
        print("\n\n⏹️  部署被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n💥 部署过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
