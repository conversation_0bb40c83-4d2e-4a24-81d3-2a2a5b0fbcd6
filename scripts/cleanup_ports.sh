#!/bin/bash
# 端口清理脚本 - 清理DaVinci AI Co-pilot Pro相关进程

echo "🧹 DaVinci AI Co-pilot Pro 端口清理工具"
echo "========================================"

# 要检查的端口列表
PORTS=(8000 8001 8080 3000)

# 检查并清理每个端口
for PORT in "${PORTS[@]}"; do
    echo ""
    echo "🔍 检查端口 $PORT..."
    
    # 查找占用该端口的进程
    PIDS=$(lsof -ti :$PORT 2>/dev/null)
    
    if [ -z "$PIDS" ]; then
        echo "✅ 端口 $PORT 未被占用"
    else
        echo "⚠️  端口 $PORT 被以下进程占用:"
        
        # 显示进程详细信息
        for PID in $PIDS; do
            PROCESS_INFO=$(ps -p $PID -o pid,ppid,user,command 2>/dev/null)
            if [ $? -eq 0 ]; then
                echo "$PROCESS_INFO"
            fi
        done
        
        # 询问是否要杀死进程
        echo ""
        read -p "是否要杀死占用端口 $PORT 的进程? (y/N): " -n 1 -r
        echo ""
        
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            echo "🔄 正在杀死进程..."
            for PID in $PIDS; do
                if kill -9 $PID 2>/dev/null; then
                    echo "✅ 已杀死进程 $PID"
                else
                    echo "❌ 无法杀死进程 $PID (可能需要sudo权限)"
                fi
            done
            
            # 再次检查
            sleep 1
            NEW_PIDS=$(lsof -ti :$PORT 2>/dev/null)
            if [ -z "$NEW_PIDS" ]; then
                echo "✅ 端口 $PORT 现在可用"
            else
                echo "⚠️  端口 $PORT 仍被占用，尝试使用sudo..."
                sudo lsof -ti :$PORT | xargs sudo kill -9 2>/dev/null
                sleep 1
                FINAL_PIDS=$(lsof -ti :$PORT 2>/dev/null)
                if [ -z "$FINAL_PIDS" ]; then
                    echo "✅ 端口 $PORT 现在可用"
                else
                    echo "❌ 无法清理端口 $PORT"
                fi
            fi
        else
            echo "⏭️  跳过端口 $PORT"
        fi
    fi
done

echo ""
echo "🔍 查找所有Python相关进程..."
PYTHON_PIDS=$(pgrep -f "python.*main.py" 2>/dev/null)

if [ -z "$PYTHON_PIDS" ]; then
    echo "✅ 没有发现相关的Python进程"
else
    echo "⚠️  发现以下Python进程:"
    for PID in $PYTHON_PIDS; do
        PROCESS_INFO=$(ps -p $PID -o pid,ppid,user,command 2>/dev/null)
        if [ $? -eq 0 ]; then
            echo "$PROCESS_INFO"
        fi
    done
    
    echo ""
    read -p "是否要杀死这些Python进程? (y/N): " -n 1 -r
    echo ""
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        for PID in $PYTHON_PIDS; do
            if kill -9 $PID 2>/dev/null; then
                echo "✅ 已杀死Python进程 $PID"
            else
                echo "❌ 无法杀死Python进程 $PID"
            fi
        done
    fi
fi

echo ""
echo "🧹 清理完成!"
echo "========================================"
echo "📋 端口状态总结:"

for PORT in "${PORTS[@]}"; do
    if lsof -i :$PORT >/dev/null 2>&1; then
        echo "❌ 端口 $PORT: 仍被占用"
    else
        echo "✅ 端口 $PORT: 可用"
    fi
done

echo ""
echo "💡 提示:"
echo "- 如果端口仍被占用，可能需要重启系统"
echo "- 可以使用 'lsof -i :8000' 查看详细信息"
echo "- 可以使用 'netstat -an | grep 8000' 检查网络状态"
