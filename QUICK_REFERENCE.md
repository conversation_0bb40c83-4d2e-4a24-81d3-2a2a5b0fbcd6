# 🎬 DaVinci AI Co-pilot Pro - 快速参考

## ⚡ 一键启动

```bash
# 自动部署
python scripts/deploy_to_davinci.py

# 一键启动 (包含自动清理)
./scripts/start_davinci_ai.sh

# 连接测试
python scripts/test_connection.py
```

## 🧹 自动清理功能

启动脚本现在自动执行以下清理操作：
- 🗑️ Python缓存 (`__pycache__`, `*.pyc`)
- 🗑️ 系统文件 (`.DS_Store`, `Thumbs.db`)
- 🗑️ 临时文件 (`*.tmp`)
- 🔄 重建运行目录 (`logs`, `temp`, `cache`, `output`)
- ⚡ 自动清理端口占用

## 🔧 关键路径

### macOS
```bash
Scripts目录: ~/Library/Application Support/Blackmagic Design/DaVinci Resolve/Fusion/Scripts
插件目录: $SCRIPTS_DIR/Utility/DaVinci AI Co-pilot Pro
DaVinci路径: /Applications/DaVinci Resolve/DaVinci Resolve.app
```

### Windows
```cmd
Scripts目录: %APPDATA%\Blackmagic Design\DaVinci Resolve\Support\Fusion\Scripts
插件目录: %SCRIPTS_DIR%\Utility\DaVinci AI Co-pilot Pro
DaVinci路径: C:\Program Files\Blackmagic Design\DaVinci Resolve\Resolve.exe
```

### Linux
```bash
Scripts目录: ~/.local/share/DaVinciResolve/Fusion/Scripts
插件目录: $SCRIPTS_DIR/Utility/DaVinci AI Co-pilot Pro
DaVinci路径: /opt/resolve/bin/resolve
```

## 🌐 Web界面

- **地址**: http://127.0.0.1:8000
- **DaVinci集成**: 点击"DaVinci集成"标签
- **连接**: 点击"连接DaVinci"按钮

## 📸 静帧捕获

```javascript
// Web界面操作
1. 选择导出格式: PNG (推荐) / JPEG
2. 选择导出质量: 高质量 / 中等质量 / 低质量
3. 设置输出目录 (可选)
4. 点击"捕获当前帧"或"导出给AI使用"
```

## 🤖 AI深度集成

### 内容分析
- **分析类型**: 综合分析、场景检测、物体识别、情感分析、质量检查
- **置信度阈值**: 0.1-1.0 (推荐0.7-0.8)
- **自动应用**: 可选择是否自动应用分析结果

### 字幕生成
- **音轨选择**: 音轨1-4
- **目标语言**: 中文(简体)、英语、日语、韩语
- **AI服务**: DeepSeek、OpenAI、Azure

### 智能标记
- **标记类型**: 内容标记、场景切换、质量问题、人物识别、物体识别
- **置信度**: 0.5-1.0 (推荐0.8)

## 🔧 常用命令

### 服务管理
```bash
# 启动服务
python src/main.py

# 停止服务
kill $(cat .service_pid)        # macOS/Linux
taskkill /F /IM python.exe      # Windows

# 查看状态
ps aux | grep "python src/main.py"
```

### 日志查看
```bash
# 实时日志
tail -f logs/app.log

# 启动日志
cat logs/startup.log

# 错误日志
grep ERROR logs/app.log
```

### 环境检查
```bash
# Python版本
python3 --version

# DaVinci进程
ps aux | grep -i resolve        # macOS/Linux
tasklist | find "Resolve"       # Windows

# 端口占用
lsof -i :8000                   # macOS/Linux
netstat -an | find "8000"       # Windows
```

## 🛠️ 故障排除

### API连接问题
```bash
# 检查环境变量 (macOS)
echo $RESOLVE_SCRIPT_API
echo $RESOLVE_SCRIPT_LIB

# 重新设置环境变量
export RESOLVE_SCRIPT_API="/Applications/DaVinci Resolve/DaVinci Resolve.app/Contents/Libraries/Fusion/fusionscript.so"
export RESOLVE_SCRIPT_LIB="/Applications/DaVinci Resolve/DaVinci Resolve.app/Contents/Libraries/Fusion/python"
```

### 权限问题 (macOS)
```bash
# 修复权限
sudo chmod -R 755 "$HOME/Library/Application Support/Blackmagic Design"
sudo chown -R $(whoami) "$HOME/Library/Application Support/Blackmagic Design"

# 系统偏好设置 > 安全性与隐私 > 完全磁盘访问权限
# 添加: DaVinci Resolve.app
```

### 端口冲突
```bash
# 查找占用进程
lsof -i :8000                   # macOS/Linux
netstat -ano | find "8000"      # Windows

# 停止进程
kill -9 <PID>                   # macOS/Linux
taskkill /PID <PID> /F          # Windows

# 修改端口 (config/config.json)
{
  "app": {
    "port": 8001
  }
}
```

## 📊 性能监控

### 系统资源
```bash
# CPU和内存使用
top | grep python               # macOS/Linux
tasklist | find "python"       # Windows

# 磁盘空间
df -h                          # macOS/Linux
dir                            # Windows
```

### 服务状态
```bash
# 健康检查
curl http://127.0.0.1:8000/health

# API状态
curl http://127.0.0.1:8000/api/davinci/status
```

## 🔑 配置要点

### 必需配置
```json
{
  "ai_services": {
    "deepseek": {
      "api_key": "your-api-key"
    }
  },
  "davinci": {
    "resolve_path": "/path/to/davinci/resolve"
  }
}
```

### 推荐配置
```json
{
  "app": {
    "debug": false,
    "port": 8000
  },
  "features": {
    "frame_capture": {
      "default_format": "PNG",
      "default_quality": "high"
    },
    "ai_integration": {
      "default_confidence": 0.7
    }
  }
}
```

## 📚 快速链接

- **完整部署指南**: [docs/DAVINCI_DEPLOYMENT_GUIDE.md](docs/DAVINCI_DEPLOYMENT_GUIDE.md)
- **生产环境总结**: [docs/PRODUCTION_DEPLOYMENT_SUMMARY.md](docs/PRODUCTION_DEPLOYMENT_SUMMARY.md)
- **API文档**: [docs/FINAL_API_COMPLIANCE_REPORT.md](docs/FINAL_API_COMPLIANCE_REPORT.md)
- **工作流程示例**: [examples/workflow_examples.py](examples/workflow_examples.py)

## 🆘 紧急联系

### 快速诊断
```bash
python scripts/test_connection.py
```

### 重置环境
```bash
# 停止所有服务
pkill -f "python src/main.py"

# 清理临时文件
rm -rf temp/* cache/* logs/*

# 重新启动
./scripts/quick_start.sh
```

### 完全重装
```bash
# 卸载
./uninstall.sh                 # macOS/Linux
uninstall.bat                  # Windows

# 重新部署
python scripts/deploy_to_davinci.py
```

---

## 🎯 成功指标

✅ **连接测试通过**: `python scripts/test_connection.py` 显示所有绿色
✅ **Web界面可访问**: http://127.0.0.1:8000 正常加载
✅ **DaVinci连接成功**: 连接状态显示"已连接"
✅ **静帧功能可用**: 静帧捕获状态显示"功能可用"
✅ **AI服务响应**: 各AI功能正常响应

**🎬 准备就绪，开始您的AI辅助视频创作之旅！**
