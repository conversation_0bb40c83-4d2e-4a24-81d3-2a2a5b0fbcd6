#!/usr/bin/env python3
"""
通过HTTP API测试MiniMax语音合成功能
"""

import asyncio
import json
import aiohttp
from pathlib import Path

async def test_minimax_via_http():
    """通过HTTP API测试MiniMax语音合成"""
    print("🎯 通过HTTP API测试MiniMax语音合成...")
    
    # API端点
    base_url = "http://127.0.0.1:8000"
    
    # 语音合成请求数据 - 使用统一AI路由格式
    request_data = {
        "input": "你好，这是MiniMax语音合成测试。",
        "capability": "speech_synthesis",
        "provider": "minimax",
        "parameters": {
            "voice_id": "female-tianmei",
            "output_format": "mp3"
        }
    }
    
    try:
        async with aiohttp.ClientSession() as session:
            print("🚀 发送语音合成请求...")
            print(f"  文本: {request_data['input']}")
            print(f"  声音: {request_data['parameters']['voice_id']}")
            print(f"  格式: {request_data['parameters']['output_format']}")
            
            # 发送POST请求
            async with session.post(
                f"{base_url}/api/ai/process",
                json=request_data,
                timeout=aiohttp.ClientTimeout(total=60)
            ) as response:
                
                if response.status == 200:
                    result = await response.json()

                    print(f"🔍 完整响应: {json.dumps(result, indent=2, ensure_ascii=False)}")

                    if result.get('success'):
                        print("✅ 语音合成成功！")
                        # 检查不同可能的数据字段
                        data = result.get('data', {})
                        result_field = result.get('result', '')

                        print(f"  输出文件: {data.get('file_path', result_field if 'output' in result_field else 'N/A')}")
                        print(f"  文件大小: {data.get('file_size', 'N/A')} bytes")
                        print(f"  持续时间: {data.get('duration', 'N/A')} seconds")

                        # 检查文件是否存在
                        file_path = data.get('file_path') or (result_field if result_field.startswith('/') else None)
                        if file_path and Path(file_path).exists():
                            file_size = Path(file_path).stat().st_size
                            print(f"  ✅ 文件确实存在，大小: {file_size} bytes")
                        else:
                            print(f"  ❌ 文件不存在: {file_path}")

                    else:
                        print("❌ 语音合成失败")
                        print(f"  错误信息: {result.get('error', 'Unknown error')}")
                        
                else:
                    print(f"❌ HTTP请求失败，状态码: {response.status}")
                    error_text = await response.text()
                    print(f"  错误信息: {error_text}")
                    
    except Exception as e:
        print(f"❌ 测试失败: {e}")

async def test_service_status():
    """测试服务状态"""
    print("\n🔍 检查服务状态...")
    
    base_url = "http://127.0.0.1:8000"
    
    try:
        async with aiohttp.ClientSession() as session:
            # 检查服务统计
            async with session.get(f"{base_url}/api/config/services") as response:
                if response.status == 200:
                    stats = await response.json()
                    print("📊 服务统计:")
                    print(f"  总服务数: {stats.get('total_services', 'N/A')}")
                    print(f"  活跃服务数: {stats.get('active_services', 'N/A')}")
                    print(f"  提供商: {', '.join(stats.get('providers', []))}")
                else:
                    print(f"❌ 无法获取服务统计，状态码: {response.status}")
            
            # 检查服务能力
            async with session.get(f"{base_url}/api/config/capabilities") as response:
                if response.status == 200:
                    capabilities = await response.json()
                    print("🛠️  服务能力:")
                    for capability, providers in capabilities.items():
                        print(f"  {capability}: {', '.join(providers)}")
                else:
                    print(f"❌ 无法获取服务能力，状态码: {response.status}")
                    
    except Exception as e:
        print(f"❌ 状态检查失败: {e}")

async def main():
    """主函数"""
    print("🚀 DaVinci AI Co-pilot Pro - MiniMax HTTP API测试")
    print("=" * 60)
    
    await test_service_status()
    await test_minimax_via_http()
    
    print("\n" + "=" * 60)
    print("🏁 测试完成")

if __name__ == "__main__":
    asyncio.run(main())
