"""
统一的路径管理器
负责管理所有文件路径和URL转换，确保路径处理的一致性
"""

import os
from pathlib import Path
from typing import Optional
import logging

logger = logging.getLogger(__name__)

class PathManager:
    """统一的路径管理器"""

    def __init__(self):
        # 项目根目录（从 src/core 回到根目录）
        self.project_root = Path(__file__).parent.parent.parent

        # 主要目录
        self.output_dir = self.project_root / "output"
        self.media_library_dir = self.output_dir / "media_library"
        self.temp_dir = self.project_root / "temp"
        self.cache_dir = self.project_root / "cache"

        # 确保所有目录存在
        self._ensure_directories()

    def _ensure_directories(self):
        """确保所有必要的目录存在"""
        directories = [
            self.output_dir,
            self.media_library_dir,
            self.temp_dir,
            self.cache_dir
        ]

        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
            logger.debug(f"✅ Ensured directory exists: {directory}")

    def get_output_directory(self) -> str:
        """获取输出目录的绝对路径"""
        self.output_dir.mkdir(parents=True, exist_ok=True)
        return str(self.output_dir)

    def get_media_library_directory(self) -> str:
        """获取媒体库目录的绝对路径"""
        self.media_library_dir.mkdir(parents=True, exist_ok=True)
        return str(self.media_library_dir)

    def get_temp_directory(self) -> str:
        """获取临时目录的绝对路径"""
        self.temp_dir.mkdir(parents=True, exist_ok=True)
        return str(self.temp_dir)

    def file_to_url(self, file_path: str) -> Optional[str]:
        """
        将文件路径转换为 URL

        Args:
            file_path: 文件的绝对路径或相对路径

        Returns:
            标准化的 URL 路径，如 "/output/media_library/file.mp3"
            如果文件不在输出目录中，返回 None
        """
        try:
            file_path = Path(file_path)

            # 如果是相对路径，转换为绝对路径
            if not file_path.is_absolute():
                file_path = self.project_root / file_path

            # 检查文件是否在输出目录中
            try:
                relative_path = file_path.relative_to(self.project_root)
                # 确保路径以 output/ 开头
                if relative_path.parts[0] == "output":
                    url = f"/{relative_path.as_posix()}"
                    logger.debug(f"🔗 Converted path to URL: {file_path} -> {url}")
                    return url
            except ValueError:
                # 文件不在项目根目录中
                pass

            logger.warning(f"⚠️ File not in output directory: {file_path}")
            return None

        except Exception as e:
            logger.error(f"❌ Failed to convert path to URL: {file_path}, error: {e}")
            return None

    def url_to_file_path(self, url: str) -> Optional[str]:
        """
        将 URL 转换为文件路径

        Args:
            url: URL 路径，如 "/output/media_library/file.mp3"

        Returns:
            文件的绝对路径
        """
        try:
            # 移除开头的斜杠
            if url.startswith('/'):
                url = url[1:]

            file_path = self.project_root / url

            # 检查文件是否存在
            if file_path.exists():
                logger.debug(f"🔗 Converted URL to path: {url} -> {file_path}")
                return str(file_path)
            else:
                logger.warning(f"⚠️ File does not exist: {file_path}")
                return None

        except Exception as e:
            logger.error(f"❌ Failed to convert URL to path: {url}, error: {e}")
            return None

    def extract_file_path_from_mcp_result(self, mcp_result_text: str, provider: str = "elevenlabs") -> Optional[str]:
        """
        从 MCP 结果中提取文件路径，支持不同提供商的格式

        Args:
            mcp_result_text: MCP 返回的文本
            provider: 提供商名称 ("elevenlabs" 或 "minimax")

        Returns:
            提取的文件路径
        """
        import re

        try:
            if provider == "minimax":
                return self._extract_minimax_file_path(mcp_result_text)
            else:
                return self._extract_elevenlabs_file_path(mcp_result_text)

        except Exception as e:
            logger.error(f"❌ Failed to extract file path from MCP result: {e}")
            return None

    def _extract_elevenlabs_file_path(self, mcp_result_text: str) -> Optional[str]:
        """从 ElevenLabs MCP 结果中提取文件路径"""
        import re

        # 匹配 "File saved as: /path/to/file.mp3" 格式
        path_match = re.search(r'File saved as:\s*([^.]+\.mp3)', mcp_result_text)
        if path_match:
            file_path = path_match.group(1).strip()
            logger.debug(f"🔍 Extracted ElevenLabs file path: {file_path}")
            return file_path

        # 如果没有匹配到，尝试其他格式
        # 移除 "Voice used:" 等额外信息
        clean_text = re.sub(r'\s*\.\s*Voice\s+used:.*$', '', mcp_result_text)
        clean_text = re.sub(r'^Success\.\s*File\s+saved\s+as:\s*', '', clean_text)
        clean_text = clean_text.strip()

        if clean_text and clean_text.endswith('.mp3'):
            logger.debug(f"🔍 Extracted ElevenLabs file path (fallback): {clean_text}")
            return clean_text

        logger.warning(f"⚠️ Could not extract ElevenLabs file path from: {mcp_result_text}")
        return None

    def _extract_minimax_file_path(self, mcp_result_text: str) -> Optional[str]:
        """从 MiniMax MCP 结果中提取文件路径或URL"""
        import re

        # MiniMax 返回的格式：
        # "Success. Audio URL: https://minimax-algeng-chat-tts.oss-cn-wulanchabu.aliyuncs.com/audio%2Ftts-xxx.mp3?..."

        # 首先尝试提取URL
        url_pattern = r'Audio URL:\s*(https?://[^\s]+\.(?:mp3|wav|flac)[^\s]*)'
        url_match = re.search(url_pattern, mcp_result_text)

        if url_match:
            audio_url = url_match.group(1).strip()
            logger.info(f"🔍 Extracted MiniMax audio URL: {audio_url}")

            # 下载URL到本地文件
            local_file_path = self._download_minimax_audio(audio_url)
            if local_file_path:
                logger.info(f"✅ Downloaded MiniMax audio to: {local_file_path}")
                return local_file_path
            else:
                logger.error(f"❌ Failed to download MiniMax audio from: {audio_url}")
                return None

        # 如果没有找到URL，尝试传统的文件路径格式
        patterns = [
            r'Audio file saved to:\s*([^\s]+\.(?:mp3|wav|flac))',
            r'Audio generated successfully:\s*([^\s]+\.(?:mp3|wav|flac))',
            r'Generated audio file:\s*([^\s]+\.(?:mp3|wav|flac))',
            r'File saved as:\s*([^\s]+\.(?:mp3|wav|flac))',
            r'Output file:\s*([^\s]+\.(?:mp3|wav|flac))',
            r'Saved to:\s*([^\s]+\.(?:mp3|wav|flac))',
            # 匹配绝对路径
            r'(/[^\s]*\.(?:mp3|wav|flac))',
            # 匹配相对路径
            r'([^\s]*\.(?:mp3|wav|flac))'
        ]

        for pattern in patterns:
            match = re.search(pattern, mcp_result_text)
            if match:
                file_path = match.group(1).strip()
                # 验证路径是否合理
                if file_path and len(file_path) > 4:  # 至少要有 "x.mp3"
                    logger.debug(f"🔍 Extracted MiniMax file path: {file_path}")
                    return file_path

        logger.warning(f"⚠️ Could not extract MiniMax file path or URL from: {mcp_result_text}")
        return None

    def _download_minimax_audio(self, audio_url: str) -> Optional[str]:
        """下载MiniMax音频URL到本地文件"""
        import requests
        import os
        from urllib.parse import urlparse, unquote
        import time

        try:
            # 生成本地文件名
            timestamp = int(time.time())
            filename = f"minimax_audio_{timestamp}.mp3"
            local_file_path = os.path.join(self.get_media_library_directory(), filename)

            logger.info(f"🔄 Downloading MiniMax audio from: {audio_url}")
            logger.info(f"📁 Saving to: {local_file_path}")

            # 下载文件
            response = requests.get(audio_url, timeout=30)
            response.raise_for_status()

            # 保存到本地
            with open(local_file_path, 'wb') as f:
                f.write(response.content)

            # 验证文件是否成功保存
            if os.path.exists(local_file_path) and os.path.getsize(local_file_path) > 0:
                logger.info(f"✅ Successfully downloaded MiniMax audio: {local_file_path}")
                return local_file_path
            else:
                logger.error(f"❌ Downloaded file is empty or doesn't exist: {local_file_path}")
                return None

        except Exception as e:
            logger.error(f"❌ Failed to download MiniMax audio: {e}")
            return None

    def generate_media_file_path(self, prefix: str, extension: str = "mp3") -> str:
        """
        生成媒体文件的完整路径

        Args:
            prefix: 文件名前缀
            extension: 文件扩展名

        Returns:
            完整的文件路径
        """
        from datetime import datetime
        import uuid

        # 生成唯一的文件名
        timestamp = int(datetime.now().timestamp() * 1000)
        unique_id = str(uuid.uuid4())[:8]
        filename = f"{prefix}_{timestamp}_{unique_id}.{extension}"

        file_path = self.media_library_dir / filename
        logger.debug(f"📁 Generated media file path: {file_path}")

        return str(file_path)

    def is_valid_output_file(self, file_path: str) -> bool:
        """
        检查文件是否是有效的输出文件

        Args:
            file_path: 文件路径

        Returns:
            是否是有效的输出文件
        """
        try:
            file_path = Path(file_path)

            # 检查文件是否存在
            if not file_path.exists():
                return False

            # 检查文件是否在输出目录中
            try:
                file_path.relative_to(self.output_dir)
                return True
            except ValueError:
                return False

        except Exception:
            return False

# 全局路径管理器实例
path_manager = PathManager()
