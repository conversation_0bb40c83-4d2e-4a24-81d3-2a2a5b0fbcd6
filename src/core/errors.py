"""
统一错误处理模块
定义所有插件相关的异常类和错误处理机制
"""

import logging
from typing import Dict, Any, Optional, Type
from enum import Enum
from functools import wraps
from dataclasses import dataclass
import asyncio
import time

logger = logging.getLogger(__name__)

class ErrorCode(Enum):
    """错误代码枚举"""
    # 通用错误
    UNKNOWN_ERROR = "UNKNOWN_ERROR"
    INVALID_REQUEST = "INVALID_REQUEST"
    INVALID_PARAMETER = "INVALID_PARAMETER"

    # 配置错误
    CONFIG_NOT_FOUND = "CONFIG_NOT_FOUND"
    CONFIG_INVALID = "CONFIG_INVALID"
    CONFIG_MISSING_KEY = "CONFIG_MISSING_KEY"

    # API错误
    API_CONNECTION_ERROR = "API_CONNECTION_ERROR"
    API_TIMEOUT = "API_TIMEOUT"
    API_AUTH_ERROR = "API_AUTH_ERROR"
    API_AUTHENTICATION_ERROR = "API_AUTHENTICATION_ERROR"
    API_RATE_LIMIT = "API_RATE_LIMIT"
    API_QUOTA_EXCEEDED = "API_QUOTA_EXCEEDED"
    API_SERVER_ERROR = "API_SERVER_ERROR"
    API_INVALID_RESPONSE = "API_INVALID_RESPONSE"

    # DaVinci错误
    DAVINCI_NOT_FOUND = "DAVINCI_NOT_FOUND"
    DAVINCI_CONNECTION_ERROR = "DAVINCI_CONNECTION_ERROR"
    DAVINCI_PROJECT_ERROR = "DAVINCI_PROJECT_ERROR"
    DAVINCI_MEDIA_ERROR = "DAVINCI_MEDIA_ERROR"
    DAVINCI_TIMELINE_ERROR = "DAVINCI_TIMELINE_ERROR"

    # 文件错误
    FILE_NOT_FOUND = "FILE_NOT_FOUND"
    FILE_PERMISSION_ERROR = "FILE_PERMISSION_ERROR"
    FILE_FORMAT_ERROR = "FILE_FORMAT_ERROR"
    FILE_SIZE_ERROR = "FILE_SIZE_ERROR"

    # 服务错误
    SERVICE_UNAVAILABLE = "SERVICE_UNAVAILABLE"
    SERVICE_TIMEOUT = "SERVICE_TIMEOUT"
    SERVICE_OVERLOAD = "SERVICE_OVERLOAD"

@dataclass
class ErrorContext:
    """错误上下文信息"""
    timestamp: float
    function_name: str
    module_name: str
    user_id: Optional[str] = None
    request_id: Optional[str] = None
    additional_data: Optional[Dict[str, Any]] = None

class BasePluginError(Exception):
    """插件基础错误类"""

    def __init__(
        self,
        message: str,
        error_code: ErrorCode = ErrorCode.UNKNOWN_ERROR,
        details: Optional[Dict[str, Any]] = None,
        context: Optional[ErrorContext] = None,
        cause: Optional[Exception] = None
    ):
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        self.context = context
        self.cause = cause
        super().__init__(message)

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'error_code': self.error_code.value,
            'message': self.message,
            'details': self.details,
            'timestamp': self.context.timestamp if self.context else time.time(),
            'function_name': self.context.function_name if self.context else None,
            'module_name': self.context.module_name if self.context else None,
        }

    def __str__(self) -> str:
        return f"[{self.error_code.value}] {self.message}"

class ConfigError(BasePluginError):
    """配置相关错误"""
    pass

class APIError(BasePluginError):
    """API调用相关错误"""

    def __init__(self, message: str, error_code: ErrorCode, status_code: Optional[int] = None, **kwargs):
        super().__init__(message, error_code, **kwargs)
        self.status_code = status_code

class DaVinciError(BasePluginError):
    """DaVinci Resolve相关错误"""
    pass

class FileError(BasePluginError):
    """文件操作相关错误"""
    pass

class ServiceError(BasePluginError):
    """服务相关错误"""
    pass

class RetryConfig:
    """重试配置"""

    def __init__(
        self,
        max_retries: int = 3,
        backoff_factor: float = 1.0,
        backoff_type: str = "exponential",  # linear, exponential, fixed
        retry_on: Optional[list] = None
    ):
        self.max_retries = max_retries
        self.backoff_factor = backoff_factor
        self.backoff_type = backoff_type
        self.retry_on = retry_on or [
            ErrorCode.API_CONNECTION_ERROR,
            ErrorCode.API_TIMEOUT,
            ErrorCode.API_SERVER_ERROR,
            ErrorCode.SERVICE_TIMEOUT
        ]

    def should_retry(self, error: BasePluginError, attempt: int) -> bool:
        """判断是否应该重试"""
        if attempt >= self.max_retries:
            return False
        return error.error_code in self.retry_on

    def get_delay(self, attempt: int) -> float:
        """获取重试延迟时间"""
        if self.backoff_type == "linear":
            return self.backoff_factor * attempt
        elif self.backoff_type == "exponential":
            return self.backoff_factor * (2 ** attempt)
        else:  # fixed
            return self.backoff_factor

def create_error_context(func_name: str, module_name: str, **kwargs) -> ErrorContext:
    """创建错误上下文"""
    return ErrorContext(
        timestamp=time.time(),
        function_name=func_name,
        module_name=module_name,
        **kwargs
    )

def handle_errors(
    error_type: Type[BasePluginError] = BasePluginError,
    retry_config: Optional[RetryConfig] = None,
    log_errors: bool = True
):
    """错误处理装饰器"""

    def decorator(func):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            attempt = 0
            last_error = None

            while True:
                try:
                    return await func(*args, **kwargs)
                except error_type as e:
                    last_error = e

                    # 创建错误上下文
                    if not e.context:
                        e.context = create_error_context(
                            func.__name__,
                            func.__module__
                        )

                    # 记录错误日志
                    if log_errors:
                        logger.error(f"Error in {func.__name__}: {e}", exc_info=True)

                    # 检查是否需要重试
                    if retry_config and retry_config.should_retry(e, attempt):
                        attempt += 1
                        delay = retry_config.get_delay(attempt)
                        logger.info(f"Retrying {func.__name__} in {delay}s (attempt {attempt})")
                        await asyncio.sleep(delay)
                        continue

                    raise e
                except Exception as e:
                    # 包装未知异常
                    context = create_error_context(func.__name__, func.__module__)
                    wrapped_error = BasePluginError(
                        message=f"Unexpected error: {str(e)}",
                        error_code=ErrorCode.UNKNOWN_ERROR,
                        context=context,
                        cause=e
                    )

                    if log_errors:
                        logger.exception(f"Unexpected error in {func.__name__}")

                    raise wrapped_error

        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except error_type as e:
                if not e.context:
                    e.context = create_error_context(func.__name__, func.__module__)

                if log_errors:
                    logger.error(f"Error in {func.__name__}: {e}", exc_info=True)

                raise e
            except Exception as e:
                context = create_error_context(func.__name__, func.__module__)
                wrapped_error = BasePluginError(
                    message=f"Unexpected error: {str(e)}",
                    error_code=ErrorCode.UNKNOWN_ERROR,
                    context=context,
                    cause=e
                )

                if log_errors:
                    logger.exception(f"Unexpected error in {func.__name__}")

                raise wrapped_error

        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper

    return decorator

class ErrorHandler:
    """全局错误处理器"""

    def __init__(self):
        self.error_stats = {}
        self.error_callbacks = []

    def add_callback(self, callback):
        """添加错误回调"""
        self.error_callbacks.append(callback)

    def handle_error(self, error: BasePluginError):
        """处理错误"""
        # 更新错误统计
        error_code = error.error_code.value
        self.error_stats[error_code] = self.error_stats.get(error_code, 0) + 1

        # 调用回调函数
        for callback in self.error_callbacks:
            try:
                callback(error)
            except Exception as e:
                logger.error(f"Error in error callback: {e}")

    def get_error_stats(self) -> Dict[str, int]:
        """获取错误统计"""
        return self.error_stats.copy()

    def reset_stats(self):
        """重置错误统计"""
        self.error_stats.clear()

# 全局错误处理器实例
error_handler = ErrorHandler()

def log_error_callback(error: BasePluginError):
    """默认错误日志回调"""
    logger.error(f"Error handled: {error}")

# 注册默认回调
error_handler.add_callback(log_error_callback)

# 常用的重试配置
DEFAULT_RETRY_CONFIG = RetryConfig(max_retries=3, backoff_factor=1.0)
API_RETRY_CONFIG = RetryConfig(max_retries=5, backoff_factor=2.0, backoff_type="exponential")
NETWORK_RETRY_CONFIG = RetryConfig(max_retries=3, backoff_factor=1.5, backoff_type="exponential")
