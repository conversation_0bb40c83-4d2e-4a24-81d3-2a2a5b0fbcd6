"""
工具函数模块
提供通用的工具函数和辅助类
"""

import os
import json
import hashlib
import uuid
import asyncio
import aiofiles
import logging
from typing import Any, Dict, List, Optional, Union, Callable
from pathlib import Path
from datetime import datetime, timezone
import time
import re
from functools import wraps
import mimetypes
from urllib.parse import urlparse

from .errors import FileError, ErrorCode

logger = logging.getLogger(__name__)

class FileUtils:
    """文件操作工具类"""

    @staticmethod
    def ensure_dir(path: Union[str, Path]) -> Path:
        """确保目录存在"""
        path = Path(path)
        path.mkdir(parents=True, exist_ok=True)
        return path

    @staticmethod
    def get_file_size(file_path: Union[str, Path]) -> int:
        """获取文件大小（字节）"""
        try:
            return Path(file_path).stat().st_size
        except FileNotFoundError:
            raise FileError(
                f"File not found: {file_path}",
                ErrorCode.FILE_NOT_FOUND
            )

    @staticmethod
    def get_file_hash(file_path: Union[str, Path], algorithm: str = 'md5') -> str:
        """计算文件哈希值"""
        hash_func = hashlib.new(algorithm)
        try:
            with open(file_path, 'rb') as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_func.update(chunk)
            return hash_func.hexdigest()
        except FileNotFoundError:
            raise FileError(
                f"File not found: {file_path}",
                ErrorCode.FILE_NOT_FOUND
            )

    @staticmethod
    def is_valid_file_type(file_path: Union[str, Path], allowed_types: List[str]) -> bool:
        """检查文件类型是否允许"""
        file_ext = Path(file_path).suffix.lower()
        return file_ext in [ext.lower() for ext in allowed_types]

    @staticmethod
    def get_mime_type(file_path: Union[str, Path]) -> str:
        """获取文件MIME类型"""
        mime_type, _ = mimetypes.guess_type(str(file_path))
        return mime_type or 'application/octet-stream'

    @staticmethod
    async def read_file_async(file_path: Union[str, Path], encoding: str = 'utf-8') -> str:
        """异步读取文件"""
        try:
            async with aiofiles.open(file_path, 'r', encoding=encoding) as f:
                return await f.read()
        except FileNotFoundError:
            raise FileError(
                f"File not found: {file_path}",
                ErrorCode.FILE_NOT_FOUND
            )

    @staticmethod
    async def write_file_async(
        file_path: Union[str, Path],
        content: str,
        encoding: str = 'utf-8',
        create_dirs: bool = True
    ):
        """异步写入文件"""
        file_path = Path(file_path)

        if create_dirs:
            FileUtils.ensure_dir(file_path.parent)

        try:
            async with aiofiles.open(file_path, 'w', encoding=encoding) as f:
                await f.write(content)
        except PermissionError:
            raise FileError(
                f"Permission denied: {file_path}",
                ErrorCode.FILE_PERMISSION_ERROR
            )

    @staticmethod
    def cleanup_temp_files(temp_dir: Union[str, Path], max_age_hours: int = 24):
        """清理临时文件"""
        temp_dir = Path(temp_dir)
        if not temp_dir.exists():
            return

        current_time = time.time()
        max_age_seconds = max_age_hours * 3600

        for file_path in temp_dir.rglob('*'):
            if file_path.is_file():
                file_age = current_time - file_path.stat().st_mtime
                if file_age > max_age_seconds:
                    try:
                        file_path.unlink()
                        logger.info(f"Cleaned up temp file: {file_path}")
                    except Exception as e:
                        logger.error(f"Failed to cleanup temp file {file_path}: {e}")

class StringUtils:
    """字符串处理工具类"""

    @staticmethod
    def generate_uuid() -> str:
        """生成UUID"""
        return str(uuid.uuid4())

    @staticmethod
    def generate_short_id(length: int = 8) -> str:
        """生成短ID"""
        return str(uuid.uuid4()).replace('-', '')[:length]

    @staticmethod
    def sanitize_filename(filename: str) -> str:
        """清理文件名，移除非法字符"""
        # 移除或替换非法字符
        illegal_chars = r'[<>:"/\\|?*]'
        sanitized = re.sub(illegal_chars, '_', filename)

        # 移除前后空格和点号
        sanitized = sanitized.strip('. ')

        # 限制长度
        if len(sanitized) > 255:
            name, ext = os.path.splitext(sanitized)
            max_name_length = 255 - len(ext)
            sanitized = name[:max_name_length] + ext

        return sanitized or 'unnamed'

    @staticmethod
    def truncate_text(text: str, max_length: int, suffix: str = '...') -> str:
        """截断文本"""
        if len(text) <= max_length:
            return text
        return text[:max_length - len(suffix)] + suffix

    @staticmethod
    def extract_keywords(text: str, max_keywords: int = 10) -> List[str]:
        """提取关键词（简单实现）"""
        # 移除标点符号，转换为小写
        cleaned_text = re.sub(r'[^\w\s]', ' ', text.lower())
        words = cleaned_text.split()

        # 过滤停用词（简单版本）
        stop_words = {'的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', '自己', '这'}
        keywords = [word for word in words if len(word) > 1 and word not in stop_words]

        # 统计词频并返回最常见的词
        from collections import Counter
        word_counts = Counter(keywords)
        return [word for word, count in word_counts.most_common(max_keywords)]

class TimeUtils:
    """时间处理工具类"""

    @staticmethod
    def get_timestamp() -> float:
        """获取当前时间戳"""
        return time.time()

    @staticmethod
    def get_iso_timestamp() -> str:
        """获取ISO格式时间戳"""
        return datetime.now(timezone.utc).isoformat()

    @staticmethod
    def format_duration(seconds: float) -> str:
        """格式化持续时间"""
        if seconds < 60:
            return f"{seconds:.1f}s"
        elif seconds < 3600:
            minutes = seconds / 60
            return f"{minutes:.1f}m"
        else:
            hours = seconds / 3600
            return f"{hours:.1f}h"

    @staticmethod
    def parse_duration(duration_str: str) -> float:
        """解析持续时间字符串为秒数"""
        duration_str = duration_str.lower().strip()

        if duration_str.endswith('s'):
            return float(duration_str[:-1])
        elif duration_str.endswith('m'):
            return float(duration_str[:-1]) * 60
        elif duration_str.endswith('h'):
            return float(duration_str[:-1]) * 3600
        else:
            return float(duration_str)  # 默认为秒

class ValidationUtils:
    """验证工具类"""

    @staticmethod
    def is_valid_url(url: str) -> bool:
        """验证URL格式"""
        try:
            result = urlparse(url)
            return all([result.scheme, result.netloc])
        except Exception:
            return False

    @staticmethod
    def is_valid_email(email: str) -> bool:
        """验证邮箱格式"""
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email) is not None

    @staticmethod
    def is_valid_api_key(api_key: str, prefix: str = None) -> bool:
        """验证API密钥格式"""
        if not api_key or len(api_key) < 10:
            return False

        if prefix and not api_key.startswith(prefix):
            return False

        return True

class AsyncUtils:
    """异步工具类"""

    @staticmethod
    async def run_with_timeout(coro, timeout: float):
        """运行协程并设置超时"""
        try:
            return await asyncio.wait_for(coro, timeout=timeout)
        except asyncio.TimeoutError:
            raise TimeoutError(f"Operation timed out after {timeout} seconds")

    @staticmethod
    async def gather_with_concurrency(tasks: List, max_concurrency: int = 10):
        """限制并发数量的gather"""
        semaphore = asyncio.Semaphore(max_concurrency)

        async def run_task(task):
            async with semaphore:
                return await task

        return await asyncio.gather(*[run_task(task) for task in tasks])

    @staticmethod
    def create_task_with_error_handling(coro, error_callback: Optional[Callable] = None):
        """创建带错误处理的任务"""
        async def wrapped_coro():
            try:
                return await coro
            except Exception as e:
                if error_callback:
                    error_callback(e)
                else:
                    logger.exception("Error in background task")
                raise

        return asyncio.create_task(wrapped_coro())

class DataUtils:
    """数据处理工具类"""

    @staticmethod
    def deep_merge_dict(dict1: Dict, dict2: Dict) -> Dict:
        """深度合并字典"""
        result = dict1.copy()

        for key, value in dict2.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = DataUtils.deep_merge_dict(result[key], value)
            else:
                result[key] = value

        return result

    @staticmethod
    def flatten_dict(d: Dict, parent_key: str = '', sep: str = '.') -> Dict:
        """扁平化字典"""
        items = []
        for k, v in d.items():
            new_key = f"{parent_key}{sep}{k}" if parent_key else k
            if isinstance(v, dict):
                items.extend(DataUtils.flatten_dict(v, new_key, sep=sep).items())
            else:
                items.append((new_key, v))
        return dict(items)

    @staticmethod
    def safe_json_loads(json_str: str, default: Any = None) -> Any:
        """安全的JSON解析"""
        try:
            return json.loads(json_str)
        except (json.JSONDecodeError, TypeError):
            return default

    @staticmethod
    def safe_json_dumps(obj: Any, default: str = "null") -> str:
        """安全的JSON序列化"""
        try:
            return json.dumps(obj, ensure_ascii=False, indent=2)
        except (TypeError, ValueError):
            return default

# 性能监控装饰器
def monitor_performance(func_name: Optional[str] = None):
    """性能监控装饰器"""
    def decorator(func):
        name = func_name or f"{func.__module__}.{func.__name__}"

        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = await func(*args, **kwargs)
                duration = time.time() - start_time
                logger.info(f"Performance: {name} took {duration:.3f}s")
                return result
            except Exception as e:
                duration = time.time() - start_time
                logger.error(f"Performance: {name} failed after {duration:.3f}s: {e}")
                raise

        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                duration = time.time() - start_time
                logger.info(f"Performance: {name} took {duration:.3f}s")
                return result
            except Exception as e:
                duration = time.time() - start_time
                logger.error(f"Performance: {name} failed after {duration:.3f}s: {e}")
                raise

        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper

    return decorator
