"""
统一API路由
简化的API端点，支持自然语言处理和意图识别
"""

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel, Field
from typing import Dict, Any, Optional, List
import logging
from datetime import datetime

from ..services.direct_service_adapter import DirectServiceAdapter
from ..services.simplified_types import ServiceCapability, ServiceProvider, ServiceRequest, ServiceResponse
from ..core.unified_config import get_config

logger = logging.getLogger(__name__)

# 创建路由器 - 不需要prefix，因为主路由器已经有/api前缀
router = APIRouter(prefix="/ai", tags=["AI Services"])

# 请求模型
class AIProcessRequest(BaseModel):
    """AI处理请求"""
    input: str = Field(..., description="输入内容（支持自然语言）")
    capability: Optional[str] = Field(None, description="指定能力类型")
    provider: Optional[str] = Field(None, description="指定服务提供商")
    parameters: Optional[Dict[str, Any]] = Field(default_factory=dict, description="额外参数")

class AIProcessResponse(BaseModel):
    """AI处理响应"""
    success: bool = Field(..., description="处理是否成功")
    result: Optional[str] = Field(None, description="处理结果")
    capability: str = Field(..., description="使用的能力类型")
    provider: str = Field(..., description="使用的服务提供商")
    processing_time: float = Field(..., description="处理时间（秒）")
    error: Optional[str] = Field(None, description="错误信息")

class CapabilitiesResponse(BaseModel):
    """能力列表响应"""
    capabilities: List[Dict[str, Any]] = Field(..., description="可用能力列表")
    providers: List[Dict[str, Any]] = Field(..., description="可用提供商列表")

class StatusResponse(BaseModel):
    """状态响应"""
    status: str = Field(..., description="服务状态")
    version: str = Field(..., description="版本信息")
    uptime: str = Field(..., description="运行时间")
    active_providers: List[str] = Field(..., description="活跃的服务提供商")

# 依赖注入
def get_service_adapter() -> DirectServiceAdapter:
    """获取服务适配器"""
    return DirectServiceAdapter()

def detect_intent(input_text: str) -> Dict[str, Any]:
    """简单的意图识别"""
    input_lower = input_text.lower()

    # 文本生成意图
    if any(keyword in input_lower for keyword in ['生成', '写', '创作', '编写', 'generate', 'write', 'create']):
        if any(keyword in input_lower for keyword in ['文章', '故事', '内容', '文本', 'article', 'story', 'content', 'text']):
            return {
                'capability': ServiceCapability.TEXT_GENERATION,
                'confidence': 0.9,
                'parameters': {'max_length': 1000}
            }

    # 翻译意图
    if any(keyword in input_lower for keyword in ['翻译', '转换', 'translate', 'convert']):
        return {
            'capability': ServiceCapability.TRANSLATION,
            'confidence': 0.9,
            'parameters': {'target_language': 'auto'}
        }

    # 语音合成意图
    if any(keyword in input_lower for keyword in ['语音', '朗读', '播放', 'voice', 'speech', 'audio']):
        return {
            'capability': ServiceCapability.SPEECH_SYNTHESIS,
            'confidence': 0.8,
            'parameters': {'voice_id': 'default'}
        }

    # 图像生成意图
    if any(keyword in input_lower for keyword in ['图片', '图像', '画', 'image', 'picture', 'draw']):
        return {
            'capability': ServiceCapability.IMAGE_GENERATION,
            'confidence': 0.8,
            'parameters': {'size': '1024x1024'}
        }

    # 视频生成意图
    if any(keyword in input_lower for keyword in ['视频', '动画', 'video', 'animation']):
        return {
            'capability': ServiceCapability.VIDEO_GENERATION,
            'confidence': 0.7,
            'parameters': {'duration': 10}
        }

    # 默认为文本分析
    return {
        'capability': ServiceCapability.TEXT_ANALYSIS,
        'confidence': 0.5,
        'parameters': {}
    }

# API端点
@router.post("/process", response_model=AIProcessResponse)
async def process_ai_request(
    request: AIProcessRequest,
    adapter: DirectServiceAdapter = Depends(get_service_adapter)
):
    """
    统一AI处理端点
    支持自然语言输入和自动意图识别
    """
    start_time = datetime.now()

    try:
        # 意图识别
        if not request.capability:
            intent = detect_intent(request.input)
            capability = intent['capability']
            parameters = {**intent['parameters'], **request.parameters}
        else:
            try:
                capability = ServiceCapability(request.capability)
                parameters = request.parameters
            except ValueError:
                raise HTTPException(status_code=400, detail=f"不支持的能力类型: {request.capability}")

        # 选择提供商
        if request.provider:
            try:
                provider = ServiceProvider(request.provider)
            except ValueError:
                raise HTTPException(status_code=400, detail=f"不支持的服务提供商: {request.provider}")
        else:
            # 自动选择提供商
            provider_mapping = {
                ServiceCapability.TEXT_GENERATION: ServiceProvider.DEEPSEEK,
                ServiceCapability.TRANSLATION: ServiceProvider.DEEPSEEK,
                ServiceCapability.TEXT_ANALYSIS: ServiceProvider.DEEPSEEK,
                ServiceCapability.SPEECH_SYNTHESIS: ServiceProvider.MINIMAX,
                ServiceCapability.IMAGE_GENERATION: ServiceProvider.DOUBAO,
                ServiceCapability.VIDEO_GENERATION: ServiceProvider.VIDU
            }
            provider = provider_mapping.get(capability, ServiceProvider.DEEPSEEK)

        # 创建服务请求
        service_request = ServiceRequest(
            capability=capability,
            provider=provider,
            content=request.input,
            parameters=parameters
        )

        # 处理请求
        response = await adapter.execute_request(service_request)

        # 计算处理时间
        processing_time = (datetime.now() - start_time).total_seconds()

        if response.success:
            # 从 ServiceResponse.data 中提取结果
            result_data = response.data
            if isinstance(result_data, dict):
                # 对于语音合成，优先返回音频URL
                if capability == ServiceCapability.SPEECH_SYNTHESIS:
                    result = result_data.get('audio_url') or result_data.get('result') or str(result_data)
                else:
                    # 尝试提取主要结果字段
                    result = result_data.get('result') or result_data.get('content') or result_data.get('text') or str(result_data)
            else:
                result = str(result_data)

            return AIProcessResponse(
                success=True,
                result=result,
                capability=capability.value,
                provider=provider.value,
                processing_time=processing_time
            )
        else:
            return AIProcessResponse(
                success=False,
                result=None,
                capability=capability.value,
                provider=provider.value,
                processing_time=processing_time,
                error=response.error
            )

    except Exception as e:
        processing_time = (datetime.now() - start_time).total_seconds()
        logger.error(f"AI处理请求失败: {e}")

        return AIProcessResponse(
            success=False,
            result=None,
            capability=request.capability or "unknown",
            provider=request.provider or "unknown",
            processing_time=processing_time,
            error=str(e)
        )

@router.get("/capabilities", response_model=CapabilitiesResponse)
async def get_capabilities():
    """获取可用的AI能力和服务提供商"""
    capabilities = [
        {
            "name": cap.value,
            "description": cap.name,
            "supported_providers": []
        }
        for cap in ServiceCapability
    ]

    providers = [
        {
            "name": provider.value,
            "description": provider.name,
            "enabled": get_config(f'providers.{provider.value}.enabled', False)
        }
        for provider in ServiceProvider
    ]

    return CapabilitiesResponse(
        capabilities=capabilities,
        providers=providers
    )

@router.get("/status", response_model=StatusResponse)
async def get_status():
    """获取服务状态"""
    active_providers = []

    for provider in ServiceProvider:
        if get_config(f'providers.{provider.value}.enabled', False):
            active_providers.append(provider.value)

    return StatusResponse(
        status="running",
        version=get_config('core.version', '2.0.0'),
        uptime="unknown",  # 这里可以添加实际的运行时间计算
        active_providers=active_providers
    )

@router.post("/batch")
async def batch_process(
    requests: List[AIProcessRequest],
    adapter: DirectServiceAdapter = Depends(get_service_adapter)
):
    """批量处理AI请求"""
    results = []

    for req in requests:
        try:
            # 重用单个请求的处理逻辑
            result = await process_ai_request(req, adapter)
            results.append(result)
        except Exception as e:
            results.append(AIProcessResponse(
                success=False,
                result=None,
                capability=req.capability or "unknown",
                provider=req.provider or "unknown",
                processing_time=0.0,
                error=str(e)
            ))

    return {"results": results, "total": len(requests), "success_count": sum(1 for r in results if r.success)}
