"""
简化配置API路由
为前端提供简化的服务配置信息，替代复杂的动态配置系统
"""

import logging
from fastapi import APIRouter
from typing import Dict, List, Any

from ..services.simplified_types import ServiceCapability, ServiceProvider
from ..core.unified_config import get_config

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/config", tags=["config"])

@router.get("/capabilities")
async def get_capabilities():
    """获取所有可用的能力"""
    return [capability.value for capability in ServiceCapability]

@router.get("/providers")
async def get_providers():
    """获取所有可用的提供商"""
    return [provider.value for provider in ServiceProvider]

@router.get("/services")
async def get_services_config():
    """获取所有可用的服务配置"""
    try:
        # 从统一配置文件读取服务配置
        services_config = get_config("services.capabilities", {})

        return {
            "success": True,
            "data": services_config
        }

    except Exception as e:
        logger.error(f"Error getting services config: {e}")
        return {
            "success": False,
            "error": "Failed to get services configuration",
            "metadata": {"error_code": "CONFIG_ERROR"}
        }

@router.get("/providers")
async def get_providers_config():
    """获取所有可用的服务提供商配置"""
    try:
        providers_config = {
            "deepseek": {
                "name": "DeepSeek",
                "description": "DeepSeek AI服务",
                "capabilities": ["text_generation", "translation", "text_analysis"],
                "status": "active"
            },
            "elevenlabs": {
                "name": "ElevenLabs",
                "description": "高质量语音合成服务",
                "capabilities": ["speech_synthesis", "voice_cloning"],
                "status": "active"
            },
            "minimax": {
                "name": "MiniMax",
                "description": "MiniMax AI服务",
                "capabilities": ["speech_synthesis", "image_generation", "video_generation"],
                "status": "active"
            },
            "doubao": {
                "name": "豆包",
                "description": "字节跳动豆包AI服务",
                "capabilities": ["image_generation", "video_generation"],
                "status": "active"
            }
        }

        return {
            "success": True,
            "data": providers_config
        }

    except Exception as e:
        logger.error(f"Error getting providers config: {e}")
        return {
            "success": False,
            "error": "Failed to get providers configuration",
            "metadata": {"error_code": "CONFIG_ERROR"}
        }

@router.get("/capabilities")
async def get_capabilities_config():
    """获取所有可用的服务能力配置"""
    try:
        capabilities_config = {}

        for capability in ServiceCapability:
            capabilities_config[capability.value] = {
                "name": capability.value.replace("_", " ").title(),
                "value": capability.value,
                "providers": []
            }

        # 添加提供商信息
        provider_capabilities = {
            ServiceProvider.DEEPSEEK: [ServiceCapability.TEXT_GENERATION, ServiceCapability.TRANSLATION, ServiceCapability.TEXT_ANALYSIS],
            ServiceProvider.ELEVENLABS: [ServiceCapability.SPEECH_SYNTHESIS],
            ServiceProvider.MINIMAX: [ServiceCapability.SPEECH_SYNTHESIS, ServiceCapability.IMAGE_GENERATION, ServiceCapability.VIDEO_GENERATION],
            ServiceProvider.DOUBAO: [ServiceCapability.IMAGE_GENERATION, ServiceCapability.VIDEO_GENERATION]
            # VIDU已并入MiniMax，不再单独列出
        }

        for provider, capabilities in provider_capabilities.items():
            for capability in capabilities:
                capabilities_config[capability.value]["providers"].append(provider.value)

        return {
            "success": True,
            "data": capabilities_config
        }

    except Exception as e:
        logger.error(f"Error getting capabilities config: {e}")
        return {
            "success": False,
            "error": "Failed to get capabilities configuration",
            "metadata": {"error_code": "CONFIG_ERROR"}
        }

@router.get("/unified_services.json")
async def get_unified_services_json():
    """兼容性端点：返回统一服务配置JSON格式"""
    try:
        # 为了兼容前端的配置加载，提供简化的配置格式
        unified_config = {
            "services": {
                "text_generation": {
                    "providers": ["deepseek"],
                    "default_provider": "deepseek"
                },
                "translation": {
                    "providers": ["deepseek"],
                    "default_provider": "deepseek"
                },
                "text_analysis": {
                    "providers": ["deepseek"],
                    "default_provider": "deepseek"
                },
                "speech_synthesis": {
                    "providers": ["elevenlabs", "minimax"],
                    "default_provider": "elevenlabs"
                },
                "image_generation": {
                    "providers": ["minimax", "doubao"],
                    "default_provider": "minimax"
                },
                "video_generation": {
                    "providers": ["minimax", "doubao"],
                    "default_provider": "minimax"
                }
            },
            "providers": {
                "deepseek": {"name": "DeepSeek", "status": "active"},
                "elevenlabs": {"name": "ElevenLabs", "status": "active"},
                "minimax": {"name": "MiniMax", "status": "active"},
                "doubao": {"name": "豆包", "status": "active"}
                # VIDU已并入MiniMax，不再单独列出
            }
        }

        return unified_config

    except Exception as e:
        logger.error(f"Error getting unified services config: {e}")
        return {"services": {}, "providers": {}}
