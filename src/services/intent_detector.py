"""
意图识别服务
基于关键词和模式匹配的简单意图识别系统
"""

import re
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from enum import Enum

from .simplified_types import ServiceCapability

@dataclass
class IntentResult:
    """意图识别结果"""
    capability: ServiceCapability
    confidence: float
    parameters: Dict[str, Any]
    matched_patterns: List[str]

class IntentDetector:
    """意图识别器"""

    def __init__(self):
        self.patterns = self._load_patterns()

    def _load_patterns(self) -> Dict[ServiceCapability, List[Dict[str, Any]]]:
        """加载意图识别模式"""
        return {
            ServiceCapability.TEXT_GENERATION: [
                {
                    'keywords': ['生成', '写', '创作', '编写', 'generate', 'write', 'create'],
                    'context': ['文章', '故事', '内容', '文本', 'article', 'story', 'content'],
                    'confidence': 0.9,
                    'parameters': {'max_length': 1000}
                },
                {
                    'keywords': ['帮我写', '请写', '创建'],
                    'context': [],
                    'confidence': 0.8,
                    'parameters': {'max_length': 500}
                }
            ],

            ServiceCapability.TRANSLATION: [
                {
                    'keywords': ['翻译', '转换', 'translate', 'convert'],
                    'context': ['英文', '中文', '日文', 'english', 'chinese', 'japanese'],
                    'confidence': 0.9,
                    'parameters': {'target_language': 'auto'}
                },
                {
                    'keywords': ['翻译成', '转为'],
                    'context': [],
                    'confidence': 0.8,
                    'parameters': {}
                }
            ],

            ServiceCapability.SPEECH_SYNTHESIS: [
                {
                    'keywords': ['语音', '朗读', '播放', 'voice', 'speech', 'audio'],
                    'context': ['合成', '生成', '制作'],
                    'confidence': 0.9,
                    'parameters': {'voice_id': 'default'}
                },
                {
                    'keywords': ['读出来', '念出来'],
                    'context': [],
                    'confidence': 0.8,
                    'parameters': {}
                }
            ],

            ServiceCapability.IMAGE_GENERATION: [
                {
                    'keywords': ['图片', '图像', '画', 'image', 'picture', 'draw'],
                    'context': ['生成', '创建', '制作', 'generate', 'create'],
                    'confidence': 0.9,
                    'parameters': {'size': '1024x1024'}
                },
                {
                    'keywords': ['画一个', '生成图片'],
                    'context': [],
                    'confidence': 0.8,
                    'parameters': {}
                }
            ],

            ServiceCapability.VIDEO_GENERATION: [
                {
                    'keywords': ['视频', '动画', 'video', 'animation'],
                    'context': ['生成', '创建', '制作', 'generate', 'create'],
                    'confidence': 0.8,
                    'parameters': {'duration': 10}
                }
            ],

            ServiceCapability.TEXT_ANALYSIS: [
                {
                    'keywords': ['分析', '总结', '提取', 'analyze', 'summarize', 'extract'],
                    'context': ['关键词', '主题', '情感', 'keywords', 'topic', 'sentiment'],
                    'confidence': 0.8,
                    'parameters': {}
                }
            ]
        }

    def detect_intent(self, text: str) -> IntentResult:
        """检测用户意图"""
        text_lower = text.lower()
        best_match = None
        best_score = 0.0

        for capability, patterns in self.patterns.items():
            for pattern in patterns:
                score = self._calculate_match_score(text_lower, pattern)
                if score > best_score:
                    best_score = score
                    best_match = {
                        'capability': capability,
                        'confidence': score,
                        'parameters': pattern['parameters'],
                        'matched_patterns': pattern['keywords']
                    }

        if best_match and best_score > 0.3:  # 最低置信度阈值
            return IntentResult(
                capability=best_match['capability'],
                confidence=best_match['confidence'],
                parameters=best_match['parameters'],
                matched_patterns=best_match['matched_patterns']
            )
        else:
            # 默认为文本分析
            return IntentResult(
                capability=ServiceCapability.TEXT_ANALYSIS,
                confidence=0.3,
                parameters={},
                matched_patterns=[]
            )

    def _calculate_match_score(self, text: str, pattern: Dict[str, Any]) -> float:
        """计算匹配分数"""
        keyword_score = 0.0
        context_score = 0.0

        # 关键词匹配
        keywords = pattern['keywords']
        matched_keywords = sum(1 for keyword in keywords if keyword in text)
        if keywords:
            keyword_score = matched_keywords / len(keywords)

        # 上下文匹配
        context = pattern.get('context', [])
        if context:
            matched_context = sum(1 for ctx in context if ctx in text)
            context_score = matched_context / len(context)
        else:
            context_score = 0.5  # 无上下文要求时给予中等分数

        # 综合分数 - 如果有关键词匹配就给予基础分数
        base_confidence = pattern.get('confidence', 0.5)
        if keyword_score > 0:
            final_score = base_confidence * (0.7 * keyword_score + 0.3 * context_score)
        else:
            final_score = 0.0  # 没有关键词匹配则分数为0

        return final_score

    def get_supported_capabilities(self) -> List[ServiceCapability]:
        """获取支持的能力列表"""
        return list(self.patterns.keys())
