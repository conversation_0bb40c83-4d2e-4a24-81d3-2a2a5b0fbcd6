"""
批量语音合成服务
处理大量文本的批量语音合成任务，支持任务队列管理和进度跟踪
"""

import asyncio
import logging
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any
from enum import Enum
from dataclasses import dataclass, field

from ..core import get_config
from .direct_service_adapter import direct_service_adapter as simplified_ai_service_manager
from ..services.simplified_types import ServiceRequest, ServiceCapability, ServiceProvider

logger = logging.getLogger(__name__)

class BatchJobStatus(Enum):
    """批量任务状态"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

@dataclass
class BatchTextItem:
    """批量文本项"""
    index: int
    text: str
    voice_config: Dict[str, Any] = field(default_factory=dict)
    status: str = "pending"
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None

@dataclass
class BatchSpeechJob:
    """批量语音合成任务"""
    job_id: str
    name: str
    status: BatchJobStatus = BatchJobStatus.PENDING
    total_items: int = 0
    completed_items: int = 0
    failed_items: int = 0
    items: List[BatchTextItem] = field(default_factory=list)
    created_at: datetime = field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    error: Optional[str] = None

    @property
    def progress_percentage(self) -> float:
        """计算进度百分比"""
        if self.total_items == 0:
            return 0.0
        return (self.completed_items + self.failed_items) / self.total_items * 100

    @property
    def success_rate(self) -> float:
        """计算成功率"""
        processed = self.completed_items + self.failed_items
        if processed == 0:
            return 0.0
        return self.completed_items / processed * 100

class BatchSpeechService:
    """批量语音合成服务"""

    def __init__(self):
        self.jobs: Dict[str, BatchSpeechJob] = {}
        self.max_concurrent_jobs = get_config('features.batch_processing.max_concurrent_tasks', 3)
        self.processing_jobs: Dict[str, asyncio.Task] = {}

        # 配置参数
        self.default_delay_between_requests = 1.0  # 请求间隔，避免API限制
        self.max_retries = 3
        self.retry_delay = 5.0

    async def create_batch_job(
        self,
        name: str,
        texts: List[str],
        voice_config: Dict[str, Any],
        auto_start: bool = True
    ) -> str:
        """创建批量语音合成任务"""
        try:
            job_id = str(uuid.uuid4())

            # 创建文本项列表
            items = []
            for i, text in enumerate(texts):
                items.append(BatchTextItem(
                    index=i,
                    text=text.strip(),
                    voice_config=voice_config.copy()
                ))

            # 创建任务
            job = BatchSpeechJob(
                job_id=job_id,
                name=name,
                total_items=len(items),
                items=items
            )

            self.jobs[job_id] = job

            logger.info(f"Created batch job {job_id} with {len(items)} items")

            # 自动开始处理
            if auto_start:
                await self.start_batch_job(job_id)

            return job_id

        except Exception as e:
            logger.error(f"Failed to create batch job: {e}")
            raise

    async def start_batch_job(self, job_id: str) -> bool:
        """开始批量任务"""
        try:
            job = self.jobs.get(job_id)
            if not job:
                raise ValueError(f"Job {job_id} not found")

            if job.status != BatchJobStatus.PENDING:
                raise ValueError(f"Job {job_id} is not in pending status")

            # 检查并发限制
            if len(self.processing_jobs) >= self.max_concurrent_jobs:
                raise ValueError("Maximum concurrent jobs limit reached")

            # 更新状态
            job.status = BatchJobStatus.PROCESSING
            job.started_at = datetime.now()

            # 创建异步任务
            task = asyncio.create_task(self._process_batch_job(job_id))
            self.processing_jobs[job_id] = task

            logger.info(f"Started batch job {job_id}")
            return True

        except Exception as e:
            logger.error(f"Failed to start batch job {job_id}: {e}")
            if job_id in self.jobs:
                self.jobs[job_id].status = BatchJobStatus.FAILED
                self.jobs[job_id].error = str(e)
            return False

    async def _process_batch_job(self, job_id: str):
        """处理批量任务的核心逻辑"""
        job = self.jobs[job_id]

        try:
            logger.info(f"Processing batch job {job_id} with {job.total_items} items")

            for item in job.items:
                try:
                    # 检查任务是否被取消
                    if job.status == BatchJobStatus.CANCELLED:
                        break

                    # 处理单个文本项
                    await self._process_text_item(job, item)

                    # 更新进度
                    if item.status == "completed":
                        job.completed_items += 1
                    elif item.status == "failed":
                        job.failed_items += 1

                    # 添加延迟避免API限制
                    if item.index < job.total_items - 1:  # 不是最后一项
                        await asyncio.sleep(self.default_delay_between_requests)

                except Exception as e:
                    logger.error(f"Failed to process item {item.index} in job {job_id}: {e}")
                    item.status = "failed"
                    item.error = str(e)
                    item.end_time = datetime.now()
                    job.failed_items += 1

            # 完成任务
            if job.status != BatchJobStatus.CANCELLED:
                job.status = BatchJobStatus.COMPLETED
                job.completed_at = datetime.now()

                logger.info(
                    f"Batch job {job_id} completed: "
                    f"{job.completed_items} succeeded, {job.failed_items} failed"
                )

        except Exception as e:
            logger.error(f"Batch job {job_id} failed: {e}")
            job.status = BatchJobStatus.FAILED
            job.error = str(e)
            job.completed_at = datetime.now()

        finally:
            # 清理处理中的任务
            if job_id in self.processing_jobs:
                del self.processing_jobs[job_id]

    async def _process_text_item(self, job: BatchSpeechJob, item: BatchTextItem):
        """处理单个文本项"""
        item.start_time = datetime.now()
        item.status = "processing"

        # 准备语音合成请求
        provider = ServiceProvider.ELEVENLABS if item.voice_config.get('provider') == 'elevenlabs' else ServiceProvider.MINIMAX

        service_request = ServiceRequest(
            capability=ServiceCapability.SPEECH_SYNTHESIS,
            provider=provider,
            content=item.text,
            parameters=item.voice_config,
            metadata={}
        )

        # 重试机制
        for attempt in range(self.max_retries):
            try:
                response = await simplified_ai_service_manager.process_request(service_request)

                if response.success:
                    item.status = "completed"
                    item.result = {
                        'audio_url': response.data.get('audio_url'),
                        'audio_path': response.data.get('audio_path'),
                        'metadata': response.metadata
                    }
                    item.end_time = datetime.now()

                    logger.debug(f"Successfully processed item {item.index} in job {job.job_id}")
                    return
                else:
                    error_msg = response.error or "Unknown error"
                    if attempt < self.max_retries - 1:
                        logger.warning(
                            f"Attempt {attempt + 1} failed for item {item.index} in job {job.job_id}: {error_msg}. Retrying..."
                        )
                        await asyncio.sleep(self.retry_delay)
                    else:
                        item.status = "failed"
                        item.error = error_msg
                        item.end_time = datetime.now()

            except Exception as e:
                error_msg = str(e)
                if attempt < self.max_retries - 1:
                    logger.warning(
                        f"Attempt {attempt + 1} failed for item {item.index} in job {job.job_id}: {error_msg}. Retrying..."
                    )
                    await asyncio.sleep(self.retry_delay)
                else:
                    item.status = "failed"
                    item.error = error_msg
                    item.end_time = datetime.now()

    async def cancel_batch_job(self, job_id: str) -> bool:
        """取消批量任务"""
        try:
            job = self.jobs.get(job_id)
            if not job:
                return False

            if job.status == BatchJobStatus.PROCESSING:
                job.status = BatchJobStatus.CANCELLED
                job.completed_at = datetime.now()

                # 取消异步任务
                if job_id in self.processing_jobs:
                    task = self.processing_jobs[job_id]
                    task.cancel()
                    del self.processing_jobs[job_id]

                logger.info(f"Cancelled batch job {job_id}")

            return True

        except Exception as e:
            logger.error(f"Failed to cancel batch job {job_id}: {e}")
            return False

    def get_batch_job_status(self, job_id: str) -> Optional[Dict[str, Any]]:
        """获取批量任务状态"""
        job = self.jobs.get(job_id)
        if not job:
            return None

        return {
            'job_id': job.job_id,
            'name': job.name,
            'status': job.status.value,
            'total_items': job.total_items,
            'completed_items': job.completed_items,
            'failed_items': job.failed_items,
            'progress_percentage': job.progress_percentage,
            'success_rate': job.success_rate,
            'created_at': job.created_at.isoformat(),
            'started_at': job.started_at.isoformat() if job.started_at else None,
            'completed_at': job.completed_at.isoformat() if job.completed_at else None,
            'error': job.error,
            'items': [
                {
                    'index': item.index,
                    'text': item.text[:100] + '...' if len(item.text) > 100 else item.text,
                    'status': item.status,
                    'result': item.result,
                    'error': item.error,
                    'start_time': item.start_time.isoformat() if item.start_time else None,
                    'end_time': item.end_time.isoformat() if item.end_time else None
                }
                for item in job.items
            ]
        }

    def list_batch_jobs(self) -> List[Dict[str, Any]]:
        """列出所有批量任务"""
        return [
            {
                'job_id': job.job_id,
                'name': job.name,
                'status': job.status.value,
                'total_items': job.total_items,
                'completed_items': job.completed_items,
                'failed_items': job.failed_items,
                'progress_percentage': job.progress_percentage,
                'success_rate': job.success_rate,
                'created_at': job.created_at.isoformat(),
                'started_at': job.started_at.isoformat() if job.started_at else None,
                'completed_at': job.completed_at.isoformat() if job.completed_at else None
            }
            for job in self.jobs.values()
        ]

    def cleanup_completed_jobs(self, max_age_hours: int = 24):
        """清理已完成的旧任务"""
        current_time = datetime.now()
        jobs_to_remove = []

        for job_id, job in self.jobs.items():
            if job.status in [BatchJobStatus.COMPLETED, BatchJobStatus.FAILED, BatchJobStatus.CANCELLED]:
                if job.completed_at:
                    age_hours = (current_time - job.completed_at).total_seconds() / 3600
                    if age_hours > max_age_hours:
                        jobs_to_remove.append(job_id)

        for job_id in jobs_to_remove:
            del self.jobs[job_id]
            logger.info(f"Cleaned up old batch job {job_id}")

        return len(jobs_to_remove)

# 创建全局批量语音合成服务实例
batch_speech_service = BatchSpeechService()
