"""
简化的服务类型定义
合并原有的 AIRequest/UnifiedRequest，使用强类型枚举，减少转换层次
"""
from abc import ABC, abstractmethod
from enum import Enum
from typing import Dict, Any, Optional, Union
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)

class ServiceCapability(Enum):
    """强类型服务能力枚举"""
    TEXT_GENERATION = "text_generation"
    TEXT_ANALYSIS = "text_analysis"
    TRANSLATION = "translation"
    SPEECH_SYNTHESIS = "speech_synthesis"
    IMAGE_GENERATION = "image_generation"
    VIDEO_GENERATION = "video_generation"
    CHAT = "chat"
    VOICE_CLONING = "voice_cloning"
    UTILITY = "utility"

    def __str__(self):
        return self.value

class ServiceProvider(Enum):
    """强类型服务提供商枚举"""
    DEEPSEEK = "deepseek"
    MINIMAX = "minimax"
    ELEVENLABS = "elevenlabs"
    DOUBAO = "doubao"
    VIDU = "vidu"

    def __str__(self):
        return self.value

@dataclass
class ServiceRequest:
    """统一的服务请求模型 - 合并了AIRequest和UnifiedRequest"""
    capability: ServiceCapability
    provider: Optional[ServiceProvider]  # 允许None值
    content: str
    parameters: Dict[str, Any]
    metadata: Optional[Dict[str, Any]] = None

    def __post_init__(self):
        """确保类型正确"""
        if isinstance(self.capability, str):
            self.capability = ServiceCapability(self.capability)
        if isinstance(self.provider, str):
            self.provider = ServiceProvider(self.provider)
        elif self.provider is None:
            # 保持None值，表示自动选择提供商
            pass
        if self.metadata is None:
            self.metadata = {}

    @property
    def capability_str(self) -> str:
        """获取能力的字符串值"""
        return self.capability.value

    @property
    def provider_str(self) -> str:
        """获取提供商的字符串值"""
        return self.provider.value

@dataclass
class ServiceResponse:
    """统一的服务响应模型"""
    success: bool
    data: Dict[str, Any]
    provider: ServiceProvider
    capability: ServiceCapability
    processing_time: float
    error: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None

    def __post_init__(self):
        """确保类型正确"""
        if isinstance(self.provider, str):
            self.provider = ServiceProvider(self.provider)
        if isinstance(self.capability, str):
            self.capability = ServiceCapability(self.capability)
        if self.metadata is None:
            self.metadata = {}

# 向后兼容类型已删除 - 简化架构统一使用ServiceRequest/ServiceResponse

    @abstractmethod
    def get_supported_capabilities(self) -> list:
        """获取支持的能力列表"""
        pass

    @classmethod
    def success_response(cls, data: Dict[str, Any], provider: Union[ServiceProvider, str],
                        capability: Union[ServiceCapability, str], processing_time: float,
                        metadata: Optional[Dict[str, Any]] = None) -> 'ServiceResponse':
        """创建成功响应"""
        return cls(
            success=True,
            data=data,
            provider=provider,
            capability=capability,
            processing_time=processing_time,
            metadata=metadata or {}
        )

    @classmethod
    def error_response(cls, error: str, provider: Union[ServiceProvider, str],
                      capability: Union[ServiceCapability, str], processing_time: float = 0.0,
                      metadata: Optional[Dict[str, Any]] = None) -> 'ServiceResponse':
        """创建错误响应"""
        return cls(
            success=False,
            data={},
            provider=provider,
            capability=capability,
            processing_time=processing_time,
            error=error,
            metadata=metadata or {}
        )

# 服务商能力映射 - 简化的硬编码映射，替代复杂的配置文件
SERVICE_CAPABILITIES = {
    ServiceProvider.DEEPSEEK: [
        ServiceCapability.TEXT_GENERATION,
        ServiceCapability.TEXT_ANALYSIS,
        ServiceCapability.TRANSLATION,
        ServiceCapability.CHAT
    ],
    ServiceProvider.MINIMAX: [
        ServiceCapability.TEXT_GENERATION,
        ServiceCapability.SPEECH_SYNTHESIS,
        ServiceCapability.IMAGE_GENERATION,
        ServiceCapability.VIDEO_GENERATION
    ],
    ServiceProvider.ELEVENLABS: [
        ServiceCapability.SPEECH_SYNTHESIS,
        ServiceCapability.VOICE_CLONING
    ],
    ServiceProvider.DOUBAO: [
        ServiceCapability.IMAGE_GENERATION,
        ServiceCapability.VIDEO_GENERATION
    ],
    ServiceProvider.VIDU: [
        ServiceCapability.VIDEO_GENERATION
    ]
}

# MCP工具映射 - 简化的硬编码映射
MCP_TOOL_MAPPINGS = {
    (ServiceProvider.DEEPSEEK, ServiceCapability.TEXT_GENERATION): "chat_completion",
    (ServiceProvider.DEEPSEEK, ServiceCapability.TEXT_ANALYSIS): "chat_completion",
    (ServiceProvider.DEEPSEEK, ServiceCapability.TRANSLATION): "chat_completion",
    (ServiceProvider.DEEPSEEK, ServiceCapability.CHAT): "chat_completion",

    (ServiceProvider.MINIMAX, ServiceCapability.TEXT_GENERATION): "text_generation",
    (ServiceProvider.MINIMAX, ServiceCapability.SPEECH_SYNTHESIS): "text_to_audio",
    (ServiceProvider.MINIMAX, ServiceCapability.IMAGE_GENERATION): "text_to_image",
    (ServiceProvider.MINIMAX, ServiceCapability.VIDEO_GENERATION): "generate_video",

    (ServiceProvider.ELEVENLABS, ServiceCapability.SPEECH_SYNTHESIS): "text_to_speech",
    (ServiceProvider.ELEVENLABS, ServiceCapability.VOICE_CLONING): "voice_cloning",

    (ServiceProvider.DOUBAO, ServiceCapability.IMAGE_GENERATION): "text_to_image",
    (ServiceProvider.DOUBAO, ServiceCapability.VIDEO_GENERATION): "image_to_video",

    (ServiceProvider.VIDU, ServiceCapability.VIDEO_GENERATION): "generate_text_to_video"
}

def get_mcp_tool_name(provider: ServiceProvider, capability: ServiceCapability) -> Optional[str]:
    """获取MCP工具名称"""
    return MCP_TOOL_MAPPINGS.get((provider, capability))

def is_capability_supported(provider: ServiceProvider, capability: ServiceCapability) -> bool:
    """检查服务商是否支持指定能力"""
    return capability in SERVICE_CAPABILITIES.get(provider, [])

def get_supported_capabilities(provider: ServiceProvider) -> list[ServiceCapability]:
    """获取服务商支持的所有能力"""
    return SERVICE_CAPABILITIES.get(provider, [])

# 兼容性转换函数已删除 - 简化架构不需要复杂的类型转换
