"""
动态服务提供商管理器
支持运行时动态注册和管理MCP服务提供商
"""

import logging
from typing import Dict, List, Optional, Any, Set
from dataclasses import dataclass, field
from pathlib import Path
import json
import time

logger = logging.getLogger(__name__)

@dataclass
class ServiceProviderMetadata:
    """服务提供商元数据"""
    name: str
    display_name: str
    description: str
    version: str = "1.0.0"
    homepage: Optional[str] = None
    supported_capabilities: List[str] = field(default_factory=list)
    supported_tools: Dict[str, str] = field(default_factory=dict)
    default_config: Dict[str, Any] = field(default_factory=dict)
    dependencies: List[str] = field(default_factory=list)
    conflicts: List[str] = field(default_factory=list)
    tags: List[str] = field(default_factory=list)
    created_at: float = field(default_factory=time.time)
    updated_at: float = field(default_factory=time.time)

@dataclass
class ServiceProviderInstance:
    """服务提供商实例"""
    metadata: ServiceProviderMetadata
    server_name: str
    config: Dict[str, Any]
    is_active: bool = False
    last_health_check: float = 0
    error_count: int = 0
    total_requests: int = 0
    successful_requests: int = 0

class DynamicServiceProviderManager:
    """动态服务提供商管理器"""

    def __init__(self, config_dir: Path = None):
        self.providers: Dict[str, ServiceProviderMetadata] = {}
        self.instances: Dict[str, ServiceProviderInstance] = {}
        self.config_dir = config_dir or Path("config")
        self.providers_config_path = self.config_dir / "service_providers.json"
        self._load_builtin_providers()

    def _load_builtin_providers(self):
        """加载内置服务提供商"""
        builtin_providers = [
            ServiceProviderMetadata(
                name="minimax",
                display_name="MiniMax",
                description="MiniMax AI服务，支持语音合成、图像生成、视频生成",
                supported_capabilities=["speech_synthesis", "image_generation", "video_generation"],
                supported_tools={
                    "text_to_audio": "speech_synthesis",
                    "text_to_image": "image_generation",
                    "generate_video": "video_generation",
                    "list_voices": "utility",
                    "voice_clone": "speech_synthesis"
                },
                tags=["ai", "multimodal", "chinese"]
            ),
            ServiceProviderMetadata(
                name="elevenlabs",
                display_name="ElevenLabs",
                description="ElevenLabs语音合成服务，支持高质量语音生成和克隆",
                supported_capabilities=["speech_synthesis", "voice_cloning"],
                supported_tools={
                    "text_to_speech": "speech_synthesis",
                    "voice_clone": "speech_synthesis",
                    "list_voices": "utility",
                    "search_voices": "utility"
                },
                tags=["voice", "tts", "cloning"]
            ),
            ServiceProviderMetadata(
                name="doubao",
                display_name="豆包",
                description="字节跳动豆包AI多模态生成服务，支持文生图、文生视频、图生视频",
                supported_capabilities=["image_generation", "video_generation", "multimodal"],
                supported_tools={
                    "text_to_image": "image_generation",
                    "image_to_video": "video_generation",
                    "text_to_video": "video_generation",
                    "encode_image_to_base64": "image_processing"
                },
                tags=["image", "video", "multimodal", "generation"]
            ),
            ServiceProviderMetadata(
                name="deepseek",
                display_name="DeepSeek",
                description="DeepSeek AI文本生成和对话服务，默认使用V3模型",
                supported_capabilities=["text_generation", "chat", "text_analysis", "translation"],
                supported_tools={
                    "chat_completion": "text_generation",
                    "multi_turn_chat": "chat"
                },
                tags=["text", "chat", "reasoning", "v3"]
            ),
            ServiceProviderMetadata(
                name="vidu",
                display_name="Vidu",
                description="Vidu视频生成服务，支持高质量视频创作",
                supported_capabilities=["video_generation"],
                supported_tools={
                    "generate_video": "video_generation",
                    "text_to_video": "video_generation",
                    "image_to_video": "video_generation"
                },
                tags=["video", "generation", "creative"]
            )
        ]

        for provider in builtin_providers:
            self.providers[provider.name] = provider

    def register_provider(self, metadata: ServiceProviderMetadata) -> bool:
        """注册新的服务提供商"""
        try:
            # 检查冲突
            if metadata.name in self.providers:
                existing = self.providers[metadata.name]
                if existing.version >= metadata.version:
                    logger.warning(f"Provider {metadata.name} already exists with same or newer version")
                    return False

            # 检查依赖
            for dep in metadata.dependencies:
                if dep not in self.providers:
                    logger.error(f"Provider {metadata.name} requires dependency {dep} which is not available")
                    return False

            # 检查冲突
            for conflict in metadata.conflicts:
                if conflict in self.providers and conflict in self.instances:
                    logger.error(f"Provider {metadata.name} conflicts with active provider {conflict}")
                    return False

            self.providers[metadata.name] = metadata
            logger.info(f"✅ Registered service provider: {metadata.display_name}")

            # 保存到配置文件
            self._save_providers_config()
            return True

        except Exception as e:
            logger.error(f"Failed to register provider {metadata.name}: {e}")
            return False

    def create_instance(self, provider_name: str, server_name: str, config: Dict[str, Any]) -> Optional[ServiceProviderInstance]:
        """创建服务提供商实例"""
        if provider_name not in self.providers:
            logger.error(f"Unknown provider: {provider_name}")
            return None

        metadata = self.providers[provider_name]
        instance = ServiceProviderInstance(
            metadata=metadata,
            server_name=server_name,
            config=config
        )

        instance_key = f"{provider_name}_{server_name}"
        self.instances[instance_key] = instance

        logger.info(f"✅ Created instance: {instance_key}")
        return instance

    def get_provider(self, name: str) -> Optional[ServiceProviderMetadata]:
        """获取服务提供商元数据"""
        return self.providers.get(name)

    def get_instance(self, provider_name: str, server_name: str) -> Optional[ServiceProviderInstance]:
        """获取服务提供商实例"""
        instance_key = f"{provider_name}_{server_name}"
        return self.instances.get(instance_key)

    def list_providers(self, capability: str = None, tag: str = None) -> List[ServiceProviderMetadata]:
        """列出服务提供商"""
        providers = list(self.providers.values())

        if capability:
            providers = [p for p in providers if capability in p.supported_capabilities]

        if tag:
            providers = [p for p in providers if tag in p.tags]

        return providers

    def get_providers_for_capability(self, capability: str) -> List[str]:
        """获取支持特定能力的提供商名称列表"""
        return [
            name for name, provider in self.providers.items()
            if capability in provider.supported_capabilities
        ]

    def _save_providers_config(self):
        """保存提供商配置到文件"""
        try:
            self.config_dir.mkdir(parents=True, exist_ok=True)

            config_data = {
                "providers": {
                    name: {
                        "display_name": provider.display_name,
                        "description": provider.description,
                        "version": provider.version,
                        "homepage": provider.homepage,
                        "supported_capabilities": provider.supported_capabilities,
                        "supported_tools": provider.supported_tools,
                        "default_config": provider.default_config,
                        "dependencies": provider.dependencies,
                        "conflicts": provider.conflicts,
                        "tags": provider.tags,
                        "updated_at": provider.updated_at
                    }
                    for name, provider in self.providers.items()
                }
            }

            with open(self.providers_config_path, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)

        except Exception as e:
            logger.error(f"Failed to save providers config: {e}")

# 全局实例
dynamic_provider_manager = DynamicServiceProviderManager()
