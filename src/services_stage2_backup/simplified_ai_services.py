"""
简化的AI服务管理器
使用新的3层架构：Frontend → SimplifiedAIService → DirectServiceAdapter → MCP
"""
import logging
from typing import Dict, Any, Optional

from .simplified_types import (
    ServiceRequest, ServiceResponse, ServiceCapability, ServiceProvider
)
from .direct_service_adapter import direct_service_adapter

logger = logging.getLogger(__name__)

class SimplifiedAIServiceManager:
    """简化的AI服务管理器"""

    def __init__(self):
        self.adapter = direct_service_adapter

    async def initialize(self):
        """初始化服务管理器"""
        logger.info("🚀 [SIMPLIFIED_MANAGER] Initializing...")
        try:
            # 确保DirectServiceAdapter的MCP管理器已初始化
            await self.adapter._ensure_initialized()
            logger.info("✅ [SIMPLIFIED_MANAGER] Initialization complete")
        except Exception as e:
            logger.error(f"❌ [SIMPLIFIED_MANAGER] Initialization failed: {e}")
            raise

    async def shutdown(self):
        """关闭服务管理器"""
        logger.info("🛑 [SIMPLIFIED_MANAGER] Shutting down...")
        try:
            # 关闭MCP连接
            if hasattr(self.adapter, 'mcp_manager'):
                await self.adapter.mcp_manager.shutdown()
            logger.info("✅ [SIMPLIFIED_MANAGER] Shutdown complete")
        except Exception as e:
            logger.error(f"❌ [SIMPLIFIED_MANAGER] Shutdown error: {e}")

    def get_service_stats(self):
        """获取服务统计信息"""
        return {
            "total_services": 5,
            "active_services": 5,
            "providers": ["deepseek", "minimax", "elevenlabs", "doubao", "vidu"],
            "capabilities": ["text_generation", "translation", "text_analysis", "speech_synthesis", "image_generation", "video_generation"]
        }

    def get_available_capabilities(self):
        """获取可用能力"""
        return [
            "text_generation",
            "translation",
            "text_analysis",
            "speech_synthesis",
            "image_generation",
            "video_generation"
        ]

    async def process_request(self, request: ServiceRequest) -> ServiceResponse:
        """处理AI请求 - 简化的统一处理流程"""
        provider_name = request.provider.value if request.provider else "auto-select"
        logger.info(f"🔄 [SIMPLIFIED_SERVICE] Processing {request.capability.value} request for {provider_name}")
        return await self.adapter.execute_request(request)

    def get_supported_capabilities(self, provider: str) -> list[str]:
        """获取服务商支持的能力列表"""
        try:
            provider_enum = ServiceProvider(provider)
            from .simplified_types import get_supported_capabilities
            capabilities = get_supported_capabilities(provider_enum)
            return [cap.value for cap in capabilities]
        except ValueError:
            logger.warning(f"Unknown provider: {provider}")
            return []

    def is_capability_supported(self, provider: str, capability: str) -> bool:
        """检查服务商是否支持指定能力"""
        try:
            provider_enum = ServiceProvider(provider)
            capability_enum = ServiceCapability(capability)
            from .simplified_types import is_capability_supported
            return is_capability_supported(provider_enum, capability_enum)
        except ValueError:
            return False

    def get_all_providers(self) -> list[str]:
        """获取所有可用的服务商"""
        return [provider.value for provider in ServiceProvider]

    def get_all_capabilities(self) -> list[str]:
        """获取所有可用的能力"""
        return [capability.value for capability in ServiceCapability]

# 全局实例
simplified_ai_service_manager = SimplifiedAIServiceManager()

# 兼容性包装器已删除 - 简化架构统一使用ServiceRequest/ServiceResponse

# 便捷函数
async def create_and_execute_request(
    capability: str,
    provider: str,
    content: str,
    parameters: Optional[Dict[str, Any]] = None,
    metadata: Optional[Dict[str, Any]] = None
) -> ServiceResponse:
    """便捷函数：创建并执行服务请求"""
    request = ServiceRequest(
        capability=ServiceCapability(capability),
        provider=ServiceProvider(provider),
        content=content,
        parameters=parameters or {},
        metadata=metadata or {}
    )
    return await simplified_ai_service_manager.process_request(request)

# 常用能力的便捷函数
async def generate_text(provider: str, prompt: str, **kwargs) -> ServiceResponse:
    """文本生成便捷函数"""
    return await create_and_execute_request(
        capability="text_generation",
        provider=provider,
        content=prompt,
        parameters=kwargs
    )

async def translate_text(provider: str, text: str, target_language: str = "en",
                        source_language: str = "auto", **kwargs) -> ServiceResponse:
    """翻译便捷函数"""
    parameters = {
        "target_language": target_language,
        "source_language": source_language,
        **kwargs
    }
    return await create_and_execute_request(
        capability="translation",
        provider=provider,
        content=text,
        parameters=parameters
    )

async def enhance_prompt(provider: str, prompt: str, target_type: str = "general", **kwargs) -> ServiceResponse:
    """提示词优化便捷函数"""
    parameters = {
        "task": "prompt_enhancement",
        "target_type": target_type,
        **kwargs
    }
    return await create_and_execute_request(
        capability="text_analysis",
        provider=provider,
        content=prompt,
        parameters=parameters
    )

async def synthesize_speech(provider: str, text: str, voice_id: str = "default", **kwargs) -> ServiceResponse:
    """语音合成便捷函数"""
    parameters = {
        "voice_id": voice_id,
        **kwargs
    }
    return await create_and_execute_request(
        capability="speech_synthesis",
        provider=provider,
        content=text,
        parameters=parameters
    )

async def generate_image(provider: str, prompt: str, **kwargs) -> ServiceResponse:
    """图像生成便捷函数"""
    return await create_and_execute_request(
        capability="image_generation",
        provider=provider,
        content=prompt,
        parameters=kwargs
    )

async def generate_video(provider: str, prompt: str, **kwargs) -> ServiceResponse:
    """视频生成便捷函数"""
    return await create_and_execute_request(
        capability="video_generation",
        provider=provider,
        content=prompt,
        parameters=kwargs
    )
