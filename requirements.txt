# ===== MVP版本核心依赖 =====

# MCP (Model Context Protocol) - 核心依赖
fastmcp>=2.10.0

# Web框架和API
fastapi>=0.104.1
uvicorn[standard]>=0.24.0
websockets>=12.0
python-multipart>=0.0.6
jinja2>=3.1.2

# HTTP客户端和异步支持
aiohttp>=3.9.1

# 数据处理和验证
pydantic>=2.5.0
python-dotenv>=1.0.0

# JSON和配置处理
PyYAML>=6.0.1

# 日志和监控
loguru>=0.7.2

# 文件处理和监控
watchdog>=6.0.0
aiofiles>=24.1.0

# ===== 开发工具（可选） =====

# 测试框架
pytest>=7.4.3
pytest-asyncio>=0.21.1
pytest-cov>=4.1.0

# 代码格式化和检查
black>=23.11.0
flake8>=6.1.0
mypy>=1.7.1

# ===== 扩展功能依赖（按需安装） =====

# 音视频处理（视频生成功能需要）
# moviepy>=1.0.3
# pydub>=0.25.1
# Pillow>=10.1.0

# 图像处理（图像生成功能需要）
# opencv-python>=********

# 数据分析（统计功能需要）
# pandas>=2.1.4
# numpy>=1.25.2

# 中文文本处理（中文分词需要）
# jieba>=0.42.1

# 系统监控（性能监控需要）
# psutil>=5.9.6

# AI服务SDK（如果有官方SDK）
# openai>=1.3.7

# 部署相关（生产环境需要）
# gunicorn>=21.2.0
