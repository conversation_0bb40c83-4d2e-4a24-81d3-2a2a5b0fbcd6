#!/usr/bin/env python3
"""
检查克隆声音状态的脚本
"""
import asyncio
import aiohttp
import json

async def check_clone_voices():
    """检查克隆声音列表"""
    print("🔍 检查克隆声音列表...")

    async with aiohttp.ClientSession() as session:
        try:
            response = await session.get("http://localhost:8000/api/speech/voices/clones")
            if response.status == 200:
                data = await response.json()
                print("✅ 克隆声音列表获取成功")

                if data.get('success'):
                    voices = data.get('data', {}).get('voices', [])
                    print(f"🎤 找到 {len(voices)} 个克隆声音:")
                    for voice in voices:
                        print(f"  - {voice.get('id', 'N/A')}: {voice.get('name', 'N/A')} ({voice.get('provider', 'N/A')})")
                    return voices
                else:
                    print(f"❌ API返回错误: {data.get('error', 'Unknown error')}")
                    return []
            else:
                error_text = await response.text()
                print(f"❌ 请求失败: {response.status}")
                print(f"📄 错误响应: {error_text}")
                return []
        except Exception as e:
            print(f"❌ 请求异常: {e}")
            return []

async def check_minimax_voices():
    """检查MiniMax语音列表中的克隆声音"""
    print("\n🔍 检查MiniMax语音列表...")

    async with aiohttp.ClientSession() as session:
        try:
            response = await session.get("http://localhost:8000/api/speech/voices?provider=minimax")
            if response.status == 200:
                data = await response.json()
                print("✅ MiniMax语音列表获取成功")

                if data.get('success'):
                    voices = data.get('data', {}).get('voices', [])
                    print(f"📊 MiniMax语音总数: {len(voices)}")

                    # 查找可能的克隆声音
                    clone_voices = []
                    target_keywords = ['realvoicetest', 'zy1234567890', 'clone', 'custom']

                    for voice in voices:
                        voice_id = voice.get('id', '').lower()
                        voice_name = voice.get('name', '').lower()

                        # 检查是否包含我们的关键词
                        if any(keyword in voice_id or keyword in voice_name for keyword in target_keywords):
                            clone_voices.append({
                                'id': voice.get('id', ''),
                                'name': voice.get('name', ''),
                                'category': voice.get('category', 'unknown'),
                                'provider': voice.get('provider', 'minimax')
                            })

                    if clone_voices:
                        print(f"🎯 找到可能的克隆声音 ({len(clone_voices)} 个):")
                        for voice in clone_voices:
                            print(f"  - {voice['id']}: {voice['name']} ({voice['category']})")
                        return clone_voices
                    else:
                        print("⚠️ 在MiniMax语音列表中没有找到克隆声音")
                        return []

                else:
                    print(f"❌ API返回错误: {data.get('error', 'Unknown error')}")
                    return []
            else:
                error_text = await response.text()
                print(f"❌ 请求失败: {response.status}")
                print(f"📄 错误响应: {error_text}")
                return []
        except Exception as e:
            print(f"❌ 请求异常: {e}")
            return []

async def main():
    """主函数"""
    print("🚀 检查克隆声音状态")
    print("=" * 40)

    # 检查克隆声音列表
    clone_voices = await check_clone_voices()

    # 检查MiniMax语音列表
    minimax_clones = await check_minimax_voices()

    # 总结
    print("\n📋 检查结果:")
    print(f"  - 克隆声音列表: {len(clone_voices)} 个")
    print(f"  - MiniMax中的克隆: {len(minimax_clones)} 个")

    if clone_voices or minimax_clones:
        print("🎉 找到克隆声音！")

        # 显示所有找到的克隆声音
        all_clones = {}
        for voice in clone_voices:
            all_clones[voice.get('id', 'unknown')] = voice
        for voice in minimax_clones:
            all_clones[voice.get('id', 'unknown')] = voice

        print("\n🎤 所有克隆声音:")
        for voice_id, voice in all_clones.items():
            print(f"  - {voice_id}: {voice.get('name', 'N/A')} ({voice.get('provider', 'N/A')})")
    else:
        print("⚠️ 暂时没有找到克隆声音，可能需要更多时间处理")
        print("💡 建议：等待几分钟后再次检查，或者刷新浏览器页面")

if __name__ == "__main__":
    asyncio.run(main())
