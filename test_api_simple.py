#!/usr/bin/env python3
import requests
import json

# 测试API调用
url = "http://127.0.0.1:8000/api/ai/process"
payload = {
    "input": "测试",
    "capability": "speech_synthesis",
    "provider": "minimax",
    "parameters": {
        "voice_id": "female-tianmei"
    }
}

try:
    print("发送请求...")
    response = requests.post(url, json=payload, timeout=30)
    print(f"状态码: {response.status_code}")
    print(f"响应: {response.text}")
    
    if response.status_code == 422:
        print("参数验证失败，检查API文档...")
        
except Exception as e:
    print(f"请求异常: {e}")
