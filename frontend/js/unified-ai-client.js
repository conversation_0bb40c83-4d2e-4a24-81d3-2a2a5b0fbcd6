/**
 * 统一AI服务客户端
 * 简化的前端API调用接口
 */

class UnifiedAIClient {
    constructor(baseUrl = 'http://127.0.0.1:8000') {
        this.baseUrl = baseUrl;
        this.apiPrefix = '/api/ai';
    }

    /**
     * 统一AI处理请求
     * @param {string} input - 输入内容（支持自然语言）
     * @param {Object} options - 可选参数
     * @returns {Promise<Object>} 处理结果
     */
    async process(input, options = {}) {
        const requestData = {
            input: input,
            capability: options.capability || null,
            provider: options.provider || null,
            parameters: options.parameters || {}
        };

        try {
            const response = await this._makeRequest('POST', '/process', requestData);
            return this._handleResponse(response);
        } catch (error) {
            console.error('AI处理请求失败:', error);
            throw error;
        }
    }

    /**
     * 批量处理请求
     * @param {Array} requests - 请求数组
     * @returns {Promise<Object>} 批量处理结果
     */
    async batchProcess(requests) {
        try {
            const response = await this._makeRequest('POST', '/batch', requests);
            return this._handleResponse(response);
        } catch (error) {
            console.error('批量处理请求失败:', error);
            throw error;
        }
    }

    /**
     * 获取可用能力列表
     * @returns {Promise<Object>} 能力列表
     */
    async getCapabilities() {
        try {
            const response = await this._makeRequest('GET', '/capabilities');
            return this._handleResponse(response);
        } catch (error) {
            console.error('获取能力列表失败:', error);
            throw error;
        }
    }

    /**
     * 获取服务状态
     * @returns {Promise<Object>} 服务状态
     */
    async getStatus() {
        try {
            const response = await this._makeRequest('GET', '/status');
            return this._handleResponse(response);
        } catch (error) {
            console.error('获取服务状态失败:', error);
            throw error;
        }
    }

    /**
     * 便捷方法：文本生成
     * @param {string} prompt - 提示词
     * @param {Object} options - 可选参数
     * @returns {Promise<string>} 生成的文本
     */
    async generateText(prompt, options = {}) {
        const result = await this.process(prompt, {
            capability: 'text_generation',
            ...options
        });
        return result.success ? result.result : null;
    }

    /**
     * 便捷方法：文本翻译
     * @param {string} text - 待翻译文本
     * @param {string} targetLanguage - 目标语言
     * @returns {Promise<string>} 翻译结果
     */
    async translateText(text, targetLanguage = 'auto') {
        const result = await this.process(text, {
            capability: 'translation',
            parameters: { target_language: targetLanguage }
        });
        return result.success ? result.result : null;
    }

    /**
     * 便捷方法：语音合成
     * @param {string} text - 待合成文本
     * @param {Object} options - 语音选项
     * @returns {Promise<string>} 音频URL或数据
     */
    async synthesizeSpeech(text, options = {}) {
        const result = await this.process(text, {
            capability: 'speech_synthesis',
            parameters: { voice_id: options.voiceId || 'default', ...options }
        });
        return result.success ? result.result : null;
    }

    /**
     * 便捷方法：图像生成
     * @param {string} prompt - 图像描述
     * @param {Object} options - 图像选项
     * @returns {Promise<string>} 图像URL或数据
     */
    async generateImage(prompt, options = {}) {
        const result = await this.process(prompt, {
            capability: 'image_generation',
            parameters: { size: options.size || '1024x1024', ...options }
        });
        return result.success ? result.result : null;
    }

    /**
     * 便捷方法：视频生成
     * @param {string} prompt - 视频描述
     * @param {Object} options - 视频选项
     * @returns {Promise<string>} 视频URL或数据
     */
    async generateVideo(prompt, options = {}) {
        const result = await this.process(prompt, {
            capability: 'video_generation',
            parameters: { duration: options.duration || 10, ...options }
        });
        return result.success ? result.result : null;
    }

    /**
     * 内部方法：发送HTTP请求
     * @private
     */
    async _makeRequest(method, endpoint, data = null) {
        const url = `${this.baseUrl}${this.apiPrefix}${endpoint}`;
        const options = {
            method: method,
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }
        };

        if (data && (method === 'POST' || method === 'PUT')) {
            options.body = JSON.stringify(data);
        }

        const response = await fetch(url, options);

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        return await response.json();
    }

    /**
     * 内部方法：处理响应
     * @private
     */
    _handleResponse(response) {
        if (response.success === false && response.error) {
            throw new Error(response.error);
        }
        return response;
    }
}

// 创建全局实例
window.aiClient = new UnifiedAIClient();

// 导出类（用于模块化环境）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = UnifiedAIClient;
}
