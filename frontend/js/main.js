/**
 * 主应用入口
 * 初始化应用和绑定事件
 */

class DaVinciAIApp {
    constructor() {
        this.aiClient = window.aiClient;
        this.uiComponents = window.uiComponents;
        this.init();
    }

    async init() {
        console.log('🚀 DaVinci AI Co-pilot Pro 启动中...');

        // 检查服务状态
        await this.checkServiceStatus();

        // 初始化UI
        this.initializeUI();

        console.log('✅ 应用初始化完成');
    }

    async checkServiceStatus() {
        const statusIndicator = document.getElementById('status-indicator');
        const statusDot = statusIndicator.querySelector('.status-dot');
        const statusText = statusIndicator.querySelector('.status-text');

        try {
            const status = await this.aiClient.getStatus();

            statusDot.className = 'status-dot status-online';
            statusText.textContent = `在线 - ${status.active_providers.length} 个服务可用`;

            console.log('📊 服务状态:', status);
        } catch (error) {
            statusDot.className = 'status-dot status-offline';
            statusText.textContent = '离线';

            console.error('❌ 服务连接失败:', error);
            this.uiComponents.showError('无法连接到AI服务，请检查服务是否启动');
        }
    }

    initializeUI() {
        const aiInterface = document.getElementById('ai-interface');

        // 创建AI处理表单
        const aiForm = this.uiComponents.createAIForm({
            onSuccess: (result) => {
                console.log('✅ AI处理成功:', result);
            },
            onError: (error) => {
                console.error('❌ AI处理失败:', error);
            }
        });

        aiInterface.appendChild(aiForm);
    }
}

// 等待DOM加载完成后启动应用
document.addEventListener('DOMContentLoaded', () => {
    window.davinciApp = new DaVinciAIApp();
});
