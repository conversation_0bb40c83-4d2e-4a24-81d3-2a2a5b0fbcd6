/**
 * 通用UI组件
 * 简化的用户界面组件库
 */

class UIComponents {
    constructor() {
        this.loadingElements = new Set();
    }

    /**
     * 显示加载状态
     * @param {string|HTMLElement} element - 元素选择器或元素
     * @param {string} message - 加载消息
     */
    showLoading(element, message = '处理中...') {
        const el = typeof element === 'string' ? document.querySelector(element) : element;
        if (!el) return;

        el.disabled = true;
        el.classList.add('loading');

        const originalText = el.textContent;
        el.setAttribute('data-original-text', originalText);
        el.textContent = message;

        this.loadingElements.add(el);
    }

    /**
     * 隐藏加载状态
     * @param {string|HTMLElement} element - 元素选择器或元素
     */
    hideLoading(element) {
        const el = typeof element === 'string' ? document.querySelector(element) : element;
        if (!el) return;

        el.disabled = false;
        el.classList.remove('loading');

        const originalText = el.getAttribute('data-original-text');
        if (originalText) {
            el.textContent = originalText;
            el.removeAttribute('data-original-text');
        }

        this.loadingElements.delete(el);
    }

    /**
     * 显示成功消息
     * @param {string} message - 成功消息
     * @param {number} duration - 显示时长（毫秒）
     */
    showSuccess(message, duration = 3000) {
        this._showNotification(message, 'success', duration);
    }

    /**
     * 显示错误消息
     * @param {string} message - 错误消息
     * @param {number} duration - 显示时长（毫秒）
     */
    showError(message, duration = 5000) {
        this._showNotification(message, 'error', duration);
    }

    /**
     * 显示信息消息
     * @param {string} message - 信息消息
     * @param {number} duration - 显示时长（毫秒）
     */
    showInfo(message, duration = 3000) {
        this._showNotification(message, 'info', duration);
    }

    /**
     * 创建AI处理表单
     * @param {Object} options - 表单选项
     * @returns {HTMLElement} 表单元素
     */
    createAIForm(options = {}) {
        const form = document.createElement('div');
        form.className = 'ai-form';

        form.innerHTML = `
            <div class="form-group">
                <label for="ai-input">输入内容：</label>
                <textarea id="ai-input" placeholder="请输入您的需求..." rows="4"></textarea>
            </div>

            <div class="form-group">
                <label for="ai-capability">AI能力：</label>
                <select id="ai-capability">
                    <option value="">自动识别</option>
                    <option value="text_generation">文本生成</option>
                    <option value="translation">翻译</option>
                    <option value="speech_synthesis">语音合成</option>
                    <option value="image_generation">图像生成</option>
                    <option value="video_generation">视频生成</option>
                    <option value="text_analysis">文本分析</option>
                </select>
            </div>

            <div class="form-group">
                <label for="ai-provider">服务提供商：</label>
                <select id="ai-provider">
                    <option value="">自动选择</option>
                    <option value="deepseek">DeepSeek</option>
                    <option value="minimax">MiniMax</option>
                    <option value="elevenlabs">ElevenLabs</option>
                    <option value="doubao">Doubao</option>
                    <option value="vidu">Vidu</option>
                </select>
            </div>

            <div class="form-actions">
                <button type="button" id="ai-submit" class="btn-primary">处理</button>
                <button type="button" id="ai-clear" class="btn-secondary">清空</button>
            </div>

            <div id="ai-result" class="result-area" style="display: none;">
                <h4>处理结果：</h4>
                <div class="result-content"></div>
            </div>
        `;

        // 绑定事件
        this._bindFormEvents(form, options);

        return form;
    }

    /**
     * 更新结果显示
     * @param {HTMLElement} form - 表单元素
     * @param {Object} result - 处理结果
     */
    updateResult(form, result) {
        const resultArea = form.querySelector('#ai-result');
        const resultContent = form.querySelector('.result-content');

        if (result.success) {
            resultContent.innerHTML = `
                <div class="success-result">
                    <p><strong>结果：</strong></p>
                    <div class="result-text">${this._formatResult(result.result)}</div>
                    <div class="result-meta">
                        <small>
                            能力：${result.capability} |
                            提供商：${result.provider} |
                            耗时：${result.processing_time?.toFixed(2)}秒
                        </small>
                    </div>
                </div>
            `;
        } else {
            resultContent.innerHTML = `
                <div class="error-result">
                    <p><strong>处理失败：</strong></p>
                    <div class="error-text">${result.error || '未知错误'}</div>
                </div>
            `;
        }

        resultArea.style.display = 'block';
    }

    /**
     * 内部方法：显示通知
     * @private
     */
    _showNotification(message, type, duration) {
        // 创建通知容器（如果不存在）
        let container = document.querySelector('.notification-container');
        if (!container) {
            container = document.createElement('div');
            container.className = 'notification-container';
            document.body.appendChild(container);
        }

        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;

        container.appendChild(notification);

        // 自动移除
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, duration);
    }

    /**
     * 内部方法：绑定表单事件
     * @private
     */
    _bindFormEvents(form, options) {
        const submitBtn = form.querySelector('#ai-submit');
        const clearBtn = form.querySelector('#ai-clear');
        const inputArea = form.querySelector('#ai-input');
        const capabilitySelect = form.querySelector('#ai-capability');
        const providerSelect = form.querySelector('#ai-provider');

        submitBtn.addEventListener('click', async () => {
            const input = inputArea.value.trim();
            if (!input) {
                this.showError('请输入内容');
                return;
            }

            this.showLoading(submitBtn, '处理中...');

            try {
                const result = await window.aiClient.process(input, {
                    capability: capabilitySelect.value || null,
                    provider: providerSelect.value || null
                });

                this.updateResult(form, result);
                this.showSuccess('处理完成');

                if (options.onSuccess) {
                    options.onSuccess(result);
                }
            } catch (error) {
                this.showError(`处理失败: ${error.message}`);

                if (options.onError) {
                    options.onError(error);
                }
            } finally {
                this.hideLoading(submitBtn);
            }
        });

        clearBtn.addEventListener('click', () => {
            inputArea.value = '';
            capabilitySelect.value = '';
            providerSelect.value = '';
            form.querySelector('#ai-result').style.display = 'none';
        });
    }

    /**
     * 内部方法：格式化结果
     * @private
     */
    _formatResult(result) {
        if (typeof result === 'string') {
            // 处理换行符
            return result.replace(/\n/g, '<br>');
        } else if (typeof result === 'object') {
            return `<pre>${JSON.stringify(result, null, 2)}</pre>`;
        }
        return String(result);
    }
}

// 创建全局实例
window.uiComponents = new UIComponents();

// 导出类（用于模块化环境）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = UIComponents;
}
