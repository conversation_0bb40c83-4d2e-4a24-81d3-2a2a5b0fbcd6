/* DaVinci AI Co-pilot Pro 主样式 */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.app-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 头部样式 */
.app-header {
    background: rgba(255, 255, 255, 0.95);
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.app-header h1 {
    color: #2c3e50;
    font-size: 1.8em;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
}

.status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #95a5a6;
    animation: pulse 2s infinite;
}

.status-dot.status-online {
    background: #27ae60;
}

.status-dot.status-offline {
    background: #e74c3c;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* 主内容样式 */
.app-main {
    flex: 1;
    background: rgba(255, 255, 255, 0.95);
    padding: 30px;
    border-radius: 10px;
    margin-bottom: 20px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* AI表单样式 */
.ai-form {
    max-width: 800px;
    margin: 0 auto;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: #2c3e50;
}

.form-group textarea,
.form-group select {
    width: 100%;
    padding: 12px;
    border: 2px solid #e0e0e0;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.3s;
}

.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: #667eea;
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

/* 按钮样式 */
.form-actions {
    display: flex;
    gap: 10px;
    justify-content: center;
    margin-top: 30px;
}

.btn-primary,
.btn-secondary {
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s;
}

.btn-primary {
    background: #667eea;
    color: white;
}

.btn-primary:hover {
    background: #5a6fd8;
    transform: translateY(-2px);
}

.btn-primary.loading {
    background: #95a5a6;
    cursor: not-allowed;
    transform: none;
}

.btn-secondary {
    background: #95a5a6;
    color: white;
}

.btn-secondary:hover {
    background: #7f8c8d;
}

/* 结果区域样式 */
.result-area {
    margin-top: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 6px;
    border-left: 4px solid #667eea;
}

.result-content {
    margin-top: 10px;
}

.success-result {
    color: #27ae60;
}

.error-result {
    color: #e74c3c;
}

.result-text {
    background: white;
    padding: 15px;
    border-radius: 4px;
    margin: 10px 0;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.result-meta {
    margin-top: 10px;
    opacity: 0.7;
}

/* 通知样式 */
.notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
}

.notification {
    padding: 12px 20px;
    margin-bottom: 10px;
    border-radius: 6px;
    color: white;
    font-weight: 500;
    animation: slideIn 0.3s ease-out;
}

.notification-success {
    background: #27ae60;
}

.notification-error {
    background: #e74c3c;
}

.notification-info {
    background: #3498db;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 底部样式 */
.app-footer {
    text-align: center;
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .app-container {
        padding: 10px;
    }
    
    .app-header {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }
    
    .app-main {
        padding: 20px;
    }
    
    .form-actions {
        flex-direction: column;
    }
}
