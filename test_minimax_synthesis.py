#!/usr/bin/env python3
"""
MiniMax语音合成功能测试脚本
"""

import asyncio
import json
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

async def test_minimax_synthesis():
    """测试MiniMax语音合成功能"""
    print("🎯 测试MiniMax语音合成功能...")
    
    try:
        from src.services.simplified_ai_services import SimplifiedAIServiceManager
        from src.services.simplified_types import ServiceRequest, ServiceCapability, ServiceProvider
        
        # 创建AI服务管理器
        ai_manager = SimplifiedAIServiceManager()
        
        # 初始化服务管理器
        print("🚀 初始化AI服务管理器...")
        success = await ai_manager.initialize()
        
        if not success:
            print("❌ AI服务管理器初始化失败")
            return
            
        print("✅ AI服务管理器初始化成功")
        
        # 创建语音合成请求
        request = ServiceRequest(
            capability=ServiceCapability.SPEECH_SYNTHESIS,
            provider=ServiceProvider.MINIMAX,
            parameters={
                "text": "你好，这是MiniMax语音合成测试。",
                "voice_id": "female-tianmei",
                "output_format": "mp3"
            }
        )
        
        print("🎤 发送语音合成请求...")
        print(f"  文本: {request.parameters['text']}")
        print(f"  声音: {request.parameters['voice_id']}")
        print(f"  格式: {request.parameters['output_format']}")
        
        # 执行语音合成
        result = await ai_manager.process_request(request)
        
        if result.success:
            print("✅ 语音合成成功！")
            print(f"  输出文件: {result.data.get('file_path', 'N/A')}")
            print(f"  文件大小: {result.data.get('file_size', 'N/A')} bytes")
            print(f"  持续时间: {result.data.get('duration', 'N/A')} seconds")
            
            # 检查文件是否存在
            file_path = result.data.get('file_path')
            if file_path and Path(file_path).exists():
                file_size = Path(file_path).stat().st_size
                print(f"  ✅ 文件确实存在，大小: {file_size} bytes")
            else:
                print(f"  ❌ 文件不存在: {file_path}")
                
        else:
            print("❌ 语音合成失败")
            print(f"  错误信息: {result.error}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

async def test_service_availability():
    """测试服务可用性"""
    print("\n🔍 测试服务可用性...")
    
    try:
        from src.services.simplified_ai_services import SimplifiedAIServiceManager
        
        # 创建AI服务管理器
        ai_manager = SimplifiedAIServiceManager()
        
        # 初始化服务管理器
        await ai_manager.initialize()
        
        # 检查MCP管理器状态
        if hasattr(ai_manager, 'mcp_manager') and ai_manager.mcp_manager:
            clients = ai_manager.mcp_manager.clients
            print(f"📡 MCP客户端状态:")
            for name, client in clients.items():
                status = "已连接" if client else "未连接"
                print(f"  - {name}: {status}")
                
            # 特别检查MiniMax
            if 'minimax' in clients and clients['minimax']:
                print("✅ MiniMax MCP客户端已连接")
            else:
                print("❌ MiniMax MCP客户端未连接")
        else:
            print("❌ MCP管理器未初始化")
            
    except Exception as e:
        print(f"❌ 服务可用性测试失败: {e}")

async def main():
    """主函数"""
    print("🚀 DaVinci AI Co-pilot Pro - MiniMax语音合成测试")
    print("=" * 60)
    
    await test_service_availability()
    await test_minimax_synthesis()
    
    print("\n" + "=" * 60)
    print("🏁 测试完成")

if __name__ == "__main__":
    asyncio.run(main())
