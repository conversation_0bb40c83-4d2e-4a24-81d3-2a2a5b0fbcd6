# 📖 DaVinci AI Co-pilot Pro 使用指南

## 🚀 快速开始

### 1. 安装和配置

#### 环境要求
- Python 3.8+
- DaVinci Resolve 17+
- 现代浏览器（Chrome, Firefox, Safari, Edge）

#### 安装步骤
1. **下载项目**
   ```bash
   git clone <repository-url>
   cd "DaVinci AI Co-pilot Pro"
   ```

2. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

3. **配置API密钥**
   ```bash
   cp .env.example .env
   # 编辑 .env 文件，添加你的API密钥
   ```

4. **启动服务**
   ```bash
   python src/main.py
   ```

### 2. 基本使用

#### 通过Web界面使用
1. 打开浏览器，访问 `frontend/index.html`
2. 在输入框中输入你的需求（支持自然语言）
3. 选择AI能力和服务提供商（可选，系统会自动识别）
4. 点击"处理"按钮
5. 查看处理结果

#### 通过API使用
```javascript
// 基本调用
const response = await fetch('http://127.0.0.1:8000/api/ai/process', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
        input: "帮我生成一篇关于AI的文章"
    })
});
const result = await response.json();
```

## 🎯 功能详解

### 1. 文本生成
**用途**：创建文章、故事、脚本等文本内容

**使用方法**：
- 自然语言：`"帮我写一篇关于人工智能的文章"`
- 指定能力：`capability: "text_generation"`

**示例**：
```javascript
const article = await aiClient.generateText(
    "写一篇500字的科技文章，主题是人工智能的未来发展"
);
```

### 2. 文本翻译
**用途**：多语言翻译

**使用方法**：
- 自然语言：`"把这段话翻译成英文"`
- 指定能力：`capability: "translation"`

**示例**：
```javascript
const translation = await aiClient.translateText(
    "你好，世界！", 
    "english"
);
```

### 3. 语音合成
**用途**：将文本转换为语音

**使用方法**：
- 自然语言：`"请朗读这段文字"`
- 指定能力：`capability: "speech_synthesis"`

**示例**：
```javascript
const audioUrl = await aiClient.synthesizeSpeech(
    "欢迎使用DaVinci AI Co-pilot Pro",
    { voiceId: "female_voice" }
);
```

### 4. 图像生成
**用途**：根据描述生成图像

**使用方法**：
- 自然语言：`"生成一张日落海滩的图片"`
- 指定能力：`capability: "image_generation"`

**示例**：
```javascript
const imageUrl = await aiClient.generateImage(
    "一只可爱的小猫在花园里玩耍",
    { size: "1024x1024" }
);
```

### 5. 视频生成
**用途**：创建短视频内容

**使用方法**：
- 自然语言：`"制作一个10秒的动画视频"`
- 指定能力：`capability: "video_generation"`

**示例**：
```javascript
const videoUrl = await aiClient.generateVideo(
    "一朵花慢慢绽放的过程",
    { duration: 15 }
);
```

## 🔧 高级功能

### 1. 批量处理
```javascript
const requests = [
    { input: "生成标题", capability: "text_generation" },
    { input: "翻译内容", capability: "translation" },
    { input: "合成语音", capability: "speech_synthesis" }
];

const results = await aiClient.batchProcess(requests);
```

### 2. 自定义参数
```javascript
const result = await aiClient.process("生成文章", {
    capability: "text_generation",
    provider: "deepseek",
    parameters: {
        max_length: 1000,
        temperature: 0.7,
        style: "formal"
    }
});
```

### 3. 状态监控
```javascript
const status = await aiClient.getStatus();
console.log(`服务状态: ${status.status}`);
console.log(`可用服务: ${status.active_providers.join(', ')}`);
```

## 🎬 DaVinci Resolve 集成

### 1. 脚本安装
1. 将项目文件夹复制到DaVinci Resolve脚本目录
2. 在DaVinci Resolve中打开"Workspace" → "Scripts"
3. 选择"DaVinci AI Co-pilot Pro"

### 2. 工作流程
1. **项目分析**：自动分析当前项目结构
2. **内容生成**：根据需求生成文本、音频、视频素材
3. **自动导入**：将生成的素材自动导入到媒体池
4. **时间线集成**：直接将素材添加到时间线

### 3. 常用场景
- **字幕生成**：自动生成视频字幕
- **配音制作**：为视频生成配音
- **素材创建**：生成背景音乐、转场效果
- **内容优化**：分析和优化视频内容

## 🛠️ 故障排除

### 常见问题

#### 1. 服务无法启动
**症状**：运行 `python src/main.py` 时出错

**解决方案**：
- 检查Python版本（需要3.8+）
- 安装缺失的依赖：`pip install -r requirements.txt`
- 检查端口8000是否被占用

#### 2. API调用失败
**症状**：前端显示"连接失败"

**解决方案**：
- 确认后端服务已启动
- 检查API密钥配置
- 查看浏览器控制台错误信息
- 检查网络连接

#### 3. AI服务无响应
**症状**：请求长时间无响应

**解决方案**：
- 检查API密钥是否有效
- 确认服务提供商服务状态
- 尝试切换到其他服务提供商
- 查看日志文件获取详细信息

#### 4. 生成结果质量差
**症状**：AI生成的内容不符合预期

**解决方案**：
- 优化输入提示词
- 调整生成参数
- 尝试不同的服务提供商
- 使用更具体的描述

### 日志查看
```bash
# 查看应用日志
tail -f logs/app.log

# 查看错误日志
tail -f logs/error.log

# 查看MCP日志
tail -f logs/mcp.log
```

## 📈 性能优化

### 1. 提高响应速度
- 使用本地缓存
- 选择地理位置较近的服务提供商
- 优化网络连接
- 减少不必要的参数

### 2. 降低成本
- 合理设置生成长度
- 使用批量处理
- 缓存常用结果
- 选择性价比高的服务提供商

### 3. 提高质量
- 使用详细的提示词
- 调整生成参数
- 多次生成选择最佳结果
- 结合多个服务提供商的优势

## 🔒 安全最佳实践

### 1. API密钥管理
- 定期更换API密钥
- 不要在代码中硬编码密钥
- 使用环境变量存储敏感信息
- 限制API密钥权限

### 2. 数据保护
- 不要处理敏感个人信息
- 定期清理临时文件
- 使用HTTPS连接
- 验证输入数据

### 3. 访问控制
- 限制API访问频率
- 监控异常使用模式
- 设置使用配额
- 记录访问日志

## 📞 获取帮助

### 1. 文档资源
- [架构说明](ARCHITECTURE.md)
- [API文档](API_REFERENCE.md)
- [开发指南](DEVELOPMENT.md)

### 2. 社区支持
- GitHub Issues
- 用户论坛
- 技术博客

### 3. 专业支持
- 邮件支持
- 在线咨询
- 定制开发服务

---

**祝你使用愉快！** 🎉
