# DaVinci AI Co-pilot Pro - 最新架构记忆文档

## 核心架构状态 (2025年最新)

### 简化架构迁移完成
- **DaVinci AI Co-pilot Pro简化架构迁移100%成功**：从复杂的6层动态化架构成功简化为3层架构(Frontend → DirectServiceAdapter → MCP/Direct API)，使用强类型枚举系统(ServiceRequest、ServiceCapability、ServiceProvider)替代字符串配置，移除复杂JSON映射改为代码处理，所有核心功能(文本生成、翻译、提示词优化)测试通过，架构迁移100%完成

### 核心组件架构
- **统一服务适配器(DirectServiceAdapter)**：简化的3层架构核心，直接处理MCP调用和API调用，支持DeepSeek直接API调用特殊处理，自动初始化MCP管理器
- **强类型枚举系统**：ServiceRequest(统一请求模型)、ServiceCapability(服务能力枚举)、ServiceProvider(服务提供商枚举)替代字符串配置，提供类型安全和IDE支持
- **DeepSeek直接API集成**：通过deepseek.py模块提供直接API调用，支持TEXT_GENERATION、TRANSLATION、TEXT_ANALYSIS功能，包含SSL证书验证修复(macOS环境)

### 前端统一架构
- **统一AI服务管理器(unified-ai-service-manager.js)**：前端统一服务调用入口，支持所有AI功能的统一调用接口
- **动态配置加载器(dynamic-config-loader.js)**：简化配置加载，通过/api/config/*端点获取服务配置
- **响应字段映射修复**：修复前端字段映射问题(generated_text vs text/content)，确保所有服务返回数据正确显示

## 当前功能状态

### 已验证功能
- ✅ **文本生成功能**：DeepSeek直接API调用，正常工作
- ✅ **翻译功能**：DeepSeek翻译服务，返回简洁翻译结果
- ✅ **提示词优化功能**：已优化提示词逻辑，只返回优化结果不含技术说明
- ✅ **文案分析功能**：针对视频制作的结构化分析，包含场景拆解、拍摄建议、后期制作建议
- ✅ **MCP服务连接**：所有5个MCP服务器(minimax/elevenlabs/doubao/deepseek/vidu)成功连接

### 服务提供商状态
- **DeepSeek**：直接API调用，支持文本生成、翻译、文案分析
- **MiniMax**：MCP连接，支持语音合成(已修复voice_id参数)
- **ElevenLabs**：MCP连接，支持语音合成
- **Doubao**：MCP连接，支持图像生成
- **Vidu**：MCP连接，支持视频生成

## 新功能/新服务商接入指南

### 1. 添加新服务能力(ServiceCapability)

**步骤1：更新枚举定义**
```python
# 在 src/services/simplified_types.py 中添加新能力
class ServiceCapability(str, Enum):
    # 现有能力...
    NEW_CAPABILITY = "new_capability"  # 添加新能力
```

**步骤2：更新前端配置**
```javascript
// 在 web/static/js/modules/dynamic-config-loader.js 中确保新能力被识别
// 系统会自动通过 /api/config/capabilities 端点获取最新配置
```

### 2. 添加新服务提供商(ServiceProvider)

**步骤1：更新提供商枚举**
```python
# 在 src/services/simplified_types.py 中添加新提供商
class ServiceProvider(str, Enum):
    # 现有提供商...
    NEW_PROVIDER = "new_provider"  # 添加新提供商
```

**步骤2：配置环境变量**
```bash
# 在 .env 文件中添加API密钥
NEW_PROVIDER_API_KEY=your_api_key_here
```

**步骤3：选择集成方式**

**方式A：MCP集成(推荐)**
```python
# 在 config/mcp_config.json 中添加MCP服务器配置
{
  "servers": {
    "new_provider": {
      "command": "npx",
      "args": ["@new-provider/mcp-server"],
      "env": {
        "NEW_PROVIDER_API_KEY": "${NEW_PROVIDER_API_KEY}"
      }
    }
  }
}
```

**方式B：直接API集成**
```python
# 创建 src/services/new_provider.py
class NewProviderService:
    def __init__(self, api_key: str):
        self.api_key = api_key
        # 初始化API客户端
    
    async def process_request(self, request: ServiceRequest) -> str:
        # 实现具体的API调用逻辑
        pass
```

**步骤4：更新DirectServiceAdapter**
```python
# 在 src/services/direct_service_adapter.py 中添加特殊处理
async def process_request(self, request: ServiceRequest) -> Dict[str, Any]:
    if request.provider == ServiceProvider.NEW_PROVIDER:
        # 添加新提供商的特殊处理逻辑
        return await self._handle_new_provider(request)
    # 现有逻辑...
```

### 3. 添加新功能模块

**步骤1：创建服务模块**
```python
# 创建 src/services/new_feature.py
async def new_feature_handler(request: ServiceRequest) -> str:
    """新功能处理逻辑"""
    # 实现具体功能
    pass
```

**步骤2：添加API端点**
```python
# 在 src/api/routes.py 中添加新端点
@router.post("/new-feature")
async def new_feature_endpoint(request: UnifiedServiceRequest):
    service_request = ServiceRequest(
        capability=ServiceCapability.NEW_CAPABILITY,
        provider=ServiceProvider(request.provider.upper()),
        content=request.content,
        parameters=request.parameters or {}
    )
    result = await direct_service_adapter.process_request(service_request)
    return {"success": True, "data": result}
```

**步骤3：添加前端界面**
```html
<!-- 在 web/templates/index.html 中添加新功能页面 -->
<div id="new-feature-section" class="section" style="display: none;">
    <h2>🆕 新功能</h2>
    <!-- 功能界面 -->
</div>
```

```javascript
// 在 web/static/js/app.js 中添加功能逻辑
async newFeature() {
    const result = await window.unifiedAIServiceManager.callService(
        'new_capability',
        providerSelect.value || null,
        { content: inputText }
    );
    // 处理结果
}
```

## 架构优势与最佳实践

### 架构优势
1. **简化调试**：3层架构减少了数据转换复杂度
2. **类型安全**：强类型枚举系统提供编译时检查
3. **统一接口**：前端统一调用接口，后端统一适配器
4. **灵活扩展**：支持MCP和直接API两种集成方式

### 最佳实践
1. **优先使用MCP集成**：标准化接口，易于维护
2. **直接API作为备选**：当MCP不可用或性能要求高时使用
3. **强类型定义**：所有新功能都要定义对应的枚举类型
4. **统一错误处理**：使用标准的成功/失败响应格式
5. **环境变量管理**：所有API密钥统一在.env文件管理

### 避坑指南
1. **不要绕过DirectServiceAdapter**：所有服务调用必须通过统一适配器
2. **不要硬编码字符串**：使用枚举类型确保类型安全
3. **不要忽略错误处理**：每个新功能都要有完整的错误处理逻辑
4. **不要跳过测试**：新功能必须经过端到端测试验证

## 技术细节与调试

### 关键文件结构
```
src/
├── services/
│   ├── simplified_types.py          # 核心类型定义
│   ├── direct_service_adapter.py    # 统一服务适配器
│   ├── deepseek.py                  # DeepSeek直接API
│   └── simple_mcp_manager.py        # MCP管理器
├── api/
│   ├── routes.py                    # API路由
│   └── simple_config_routes.py      # 配置API
web/static/js/modules/
├── unified-ai-service-manager.js    # 前端统一服务管理
└── dynamic-config-loader.js         # 动态配置加载
```

### 调试技巧
1. **后端调试**：查看DirectServiceAdapter的process_request方法日志
2. **前端调试**：检查unified-ai-service-manager.js的callService方法
3. **MCP调试**：使用SimpleMCPManager的list_tools方法检查连接状态
4. **配置调试**：访问/api/config/capabilities端点检查配置加载

### 常见问题解决
1. **MCP连接失败**：检查.env文件API密钥配置
2. **前端显示异常**：检查响应字段映射(generated_text vs text/content)
3. **SSL证书错误**：参考deepseek.py的SSL上下文配置
4. **类型错误**：确保使用正确的枚举类型而非字符串

## 未来发展方向

### 短期目标
- 完善文案分析功能的前端显示
- 优化其他MCP服务商的响应映射
- 添加更多AI服务能力支持

### 长期规划
- 支持更多AI服务提供商
- 增强错误处理和用户体验
- 实现服务能力的动态发现和注册

## 项目信息
- **项目路径**：/Library/Application Support/Blackmagic Design/DaVinci Resolve/Fusion/Scripts/Utility/DaVinci AI Co-pilot Pro
- **核心配置**：config/config.json(应用级配置)、.env(API密钥)、config/mcp_config.json(MCP服务器配置)
- **架构版本**：简化架构 v2.0 (2025年最新)
- **最后更新**：2025-01-27
