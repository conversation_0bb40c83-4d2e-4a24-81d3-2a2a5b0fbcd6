# 环境变量配置指南

## 概述

DaVinci AI Co-pilot Pro 使用环境变量来管理API密钥和系统配置。所有环境变量都在 `.env` 文件中定义，并通过统一配置系统进行管理。

## 必需的环境变量

### AI服务提供商API密钥

| 变量名 | 服务商 | 用途 | 示例 |
|--------|--------|------|------|
| `MINIMAX_API_KEY` | MiniMax | 语音合成服务 | `eyJhbGciOiJSUzI1NiI...` |
| `MINIMAX_GROUP_ID` | MiniMax | 组织ID | `1894672146249027958` |
| `DEEPSEEK_API_KEY` | DeepSeek | 文本生成和分析 | `sk-cef7e86713b54407...` |
| `DOUBAO_API_KEY` | 豆包(Doubao) | 图像和视频生成 | `9dd1697b-8636-4964...` |
| `ELEVENLABS_API_KEY` | ElevenLabs | 语音合成 | `sk_9a195992104a8b95...` |
| `VIDU_API_KEY` | Vidu | 视频生成 | `vda_846893296268611584...` |

## 可选的环境变量

### 系统配置

| 变量名 | 默认值 | 用途 | 可选值 |
|--------|--------|------|--------|
| `MCP_TIMEOUT` | `30` | MCP服务超时时间(秒) | 任何正整数 |
| `MCP_LOG_LEVEL` | `INFO` | 日志级别 | `DEBUG`, `INFO`, `WARNING`, `ERROR` |
| `OUTPUT_DIR` | `./output` | 输出文件目录 | 任何有效路径 |
| `SSL_CERT_FILE` | `/etc/ssl/cert.pem` | SSL证书文件路径 | 任何有效证书文件路径 |

## 配置步骤

1. **复制示例文件**：
   ```bash
   cp .env.example .env
   ```

2. **编辑 `.env` 文件**：
   - 填入实际的API密钥
   - 根据需要调整可选配置

3. **验证配置**：
   ```bash
   python -c "from src.core.unified_config import get_config; print('配置加载成功')"
   ```

## 安全注意事项

- ⚠️ **永远不要将 `.env` 文件提交到版本控制系统**
- ✅ 确保 `.env` 文件在 `.gitignore` 中
- ✅ 定期轮换API密钥
- ✅ 使用最小权限原则配置API密钥

## 环境变量解析

系统通过以下方式解析环境变量：

1. **统一配置系统**：所有环境变量通过 `src/core/unified_config.py` 管理
2. **MCP服务配置**：在 `config/unified_config.json` 中使用 `${VARIABLE_NAME}` 语法引用
3. **运行时解析**：启动时自动从 `.env` 文件加载并解析所有变量

## 故障排除

### 常见问题

1. **API密钥无效**：
   - 检查密钥格式是否正确
   - 确认密钥未过期
   - 验证服务商账户状态

2. **环境变量未加载**：
   - 确认 `.env` 文件存在于项目根目录
   - 检查变量名拼写是否正确
   - 重启应用程序

3. **权限问题**：
   - 确保 `.env` 文件有正确的读取权限
   - 检查 `OUTPUT_DIR` 目录的写入权限

### 调试命令

```bash
# 检查环境变量加载状态
python -c "
import os
from dotenv import load_dotenv
load_dotenv()
vars = ['MINIMAX_API_KEY', 'DEEPSEEK_API_KEY', 'DOUBAO_API_KEY', 'ELEVENLABS_API_KEY', 'VIDU_API_KEY']
for var in vars:
    status = '✅' if os.getenv(var) else '❌'
    print(f'{status} {var}')
"
```

## 更新历史

- **v2.0.0** (2025-07-27): 统一配置系统，标准化所有环境变量
- **v1.0.0**: 初始版本，基本环境变量支持
