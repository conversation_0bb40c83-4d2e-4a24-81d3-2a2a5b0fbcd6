/**
 * 动态配置加载器
 * 替换前端硬编码配置，实现100%动态化
 */

class DynamicConfigLoader {
    constructor() {
        this.serviceTypes = new Map();
        this.serviceProviders = new Map();
        this.selectorMappings = {};
        this.capabilityProviders = {};
        this.smartParameters = new Map();
        this.configLoaded = false;

        // 缓存配置，避免重复请求
        this.cache = {
            serviceTypes: null,
            serviceProviders: null,
            selectorMappings: null,
            capabilityProviders: null,
            lastUpdate: null
        };

        this.init();
    }

    async init() {
        console.log('🔄 初始化动态配置加载器...');
        try {
            await this.loadAllConfigurations();
            this.configLoaded = true;
            console.log('✅ 动态配置加载完成');

            // 触发配置加载完成事件
            window.dispatchEvent(new CustomEvent('dynamicConfigLoaded', {
                detail: {
                    serviceTypes: this.serviceTypes.size,
                    serviceProviders: this.serviceProviders.size,
                    capabilities: Object.keys(this.capabilityProviders).length
                }
            }));

        } catch (error) {
            console.error('❌ 动态配置加载失败:', error);
            this.loadFallbackConfig();
        }
    }

    async loadAllConfigurations() {
        console.log('📦 加载简化配置...');

        try {
            // 使用新的简化配置API
            const [servicesResponse, providersResponse] = await Promise.all([
                fetch('/api/config/services'),
                fetch('/api/config/providers')
            ]);

            const servicesResult = await servicesResponse.json();
            const providersResult = await providersResponse.json();

            if (!servicesResult.success || !providersResult.success) {
                throw new Error('配置加载失败');
            }

            const services = servicesResult.data;
            const providers = providersResult.data;

            // 处理服务类型
            Object.entries(services).forEach(([serviceName, serviceConfig]) => {
                this.serviceTypes.set(serviceName, {
                    name: serviceName,
                    displayName: serviceConfig.name,
                    description: serviceConfig.description,
                    category: 'ai_services',
                    providers: serviceConfig.providers
                });

                // 构建能力提供商映射
                this.capabilityProviders[serviceName] = serviceConfig.providers;
            });

            // 处理服务提供商
            Object.entries(providers).forEach(([providerName, providerConfig]) => {
                this.serviceProviders.set(providerName, {
                    name: providerName,
                    displayName: providerConfig.name,
                    description: providerConfig.description,
                    type: 'ai_service',
                    capabilities: providerConfig.capabilities,
                    status: providerConfig.status
                });
            });

            // 简化的选择器映射
            this.selectorMappings = {};

            // 更新缓存
            this.updateCache();

            console.log(`✅ 简化配置加载完成: ${this.serviceTypes.size} 服务类型, ${this.serviceProviders.size} 提供商`);

        } catch (error) {
            console.error('❌ 简化配置加载失败:', error);
            throw error;
        }
    }

    // 旧的fetch方法已移除，使用简化配置API

    loadFallbackConfig() {
        console.log('🔄 加载fallback配置...');

        // 硬编码的fallback配置
        const fallbackServices = {
            'text_generation': {
                name: '文本生成',
                description: '生成各种类型的文本内容',
                providers: ['deepseek']
            },
            'translation': {
                name: '翻译',
                description: '文本翻译服务',
                providers: ['deepseek']
            },
            'text_analysis': {
                name: '文本分析',
                description: '分析文本内容',
                providers: ['deepseek']
            },
            'speech_synthesis': {
                name: '语音合成',
                description: '将文本转换为语音',
                providers: ['elevenlabs', 'minimax']
            },
            'image_generation': {
                name: '图像生成',
                description: '根据文本描述生成图像',
                providers: ['doubao']
            },
            'video_generation': {
                name: '视频生成',
                description: '根据文本或图像生成视频',
                providers: ['vidu']
            }
        };

        const fallbackProviders = {
            'deepseek': {
                name: 'DeepSeek',
                description: 'DeepSeek AI服务',
                capabilities: ['text_generation', 'translation', 'text_analysis'],
                status: 'active'
            },
            'elevenlabs': {
                name: 'ElevenLabs',
                description: '高质量语音合成服务',
                capabilities: ['speech_synthesis'],
                status: 'active'
            },
            'minimax': {
                name: 'MiniMax',
                description: 'MiniMax AI服务',
                capabilities: ['speech_synthesis'],
                status: 'active'
            },
            'doubao': {
                name: '豆包',
                description: '字节跳动豆包AI服务',
                capabilities: ['image_generation'],
                status: 'active'
            },
            'vidu': {
                name: 'Vidu',
                description: 'Vidu视频生成服务',
                capabilities: ['video_generation'],
                status: 'active'
            }
        };

        // 处理服务类型
        Object.entries(fallbackServices).forEach(([serviceName, serviceConfig]) => {
            this.serviceTypes.set(serviceName, {
                name: serviceName,
                displayName: serviceConfig.name,
                description: serviceConfig.description,
                category: 'ai_services',
                providers: serviceConfig.providers
            });

            this.capabilityProviders[serviceName] = serviceConfig.providers;
        });

        // 处理服务提供商
        Object.entries(fallbackProviders).forEach(([providerName, providerConfig]) => {
            this.serviceProviders.set(providerName, {
                name: providerName,
                displayName: providerConfig.name,
                description: providerConfig.description,
                type: 'ai_service',
                capabilities: providerConfig.capabilities,
                status: providerConfig.status
            });
        });

        this.selectorMappings = {};
        this.configLoaded = true;

        console.log('✅ Fallback配置加载完成');
    }

    async fetchSmartParameters(provider, capability) {
        const cacheKey = `${provider}_${capability}`;

        if (this.smartParameters.has(cacheKey)) {
            return this.smartParameters.get(cacheKey);
        }

        try {
            const response = await fetch(`/api/config/smart-parameters?provider=${provider}&capability=${capability}`);
            const result = await response.json();

            if (result.success) {
                this.smartParameters.set(cacheKey, result.data);
                return result.data;
            } else {
                console.warn(`获取智能参数失败: ${result.message}`);
                return [];
            }
        } catch (error) {
            console.error('获取智能参数时发生错误:', error);
            return [];
        }
    }

    async validateParameters(provider, capability, parameters) {
        try {
            const response = await fetch('/api/config/validate-parameters', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    provider,
                    capability,
                    parameters
                })
            });

            const result = await response.json();
            return result.success ? result.data : { valid: false, errors: [result.message] };

        } catch (error) {
            console.error('参数验证时发生错误:', error);
            return { valid: false, errors: ['参数验证请求失败'] };
        }
    }

    // 公共接口方法
    getServiceTypes() {
        return Array.from(this.serviceTypes.values());
    }

    getServiceType(name) {
        return this.serviceTypes.get(name);
    }

    getServiceProviders() {
        return Array.from(this.serviceProviders.values());
    }

    getServiceProvider(name) {
        return this.serviceProviders.get(name);
    }

    getProvidersForCapability(capability) {
        return this.capabilityProviders[capability] || [];
    }

    getCapabilitiesForProvider(provider) {
        const providerObj = this.serviceProviders.get(provider);
        return providerObj ? providerObj.capabilities : [];
    }

    getSelectorMappings() {
        return this.selectorMappings;
    }

    getSelectorsForCapability(capability) {
        return this.selectorMappings[capability] || [];
    }

    getServiceTypesByCategory(category) {
        return this.getServiceTypes().filter(type => type.category === category);
    }

    isProviderAvailable(provider) {
        const providerObj = this.serviceProviders.get(provider);
        return providerObj && providerObj.status === 'active';
    }

    isCapabilitySupported(provider, capability) {
        const providerObj = this.serviceProviders.get(provider);
        return providerObj && providerObj.capabilities.includes(capability);
    }

    // 缓存管理
    updateCache() {
        this.cache = {
            serviceTypes: this.getServiceTypes(),
            serviceProviders: this.getServiceProviders(),
            selectorMappings: this.selectorMappings,
            capabilityProviders: this.capabilityProviders,
            lastUpdate: new Date()
        };
    }

    clearCache() {
        this.smartParameters.clear();
        this.cache = {
            serviceTypes: null,
            serviceProviders: null,
            selectorMappings: null,
            capabilityProviders: null,
            lastUpdate: null
        };
    }

    async reloadConfiguration() {
        console.log('🔄 重新加载动态配置...');
        this.clearCache();

        try {
            // 调用后端重新加载配置
            const response = await fetch('/api/config/reload', { method: 'POST' });
            const result = await response.json();

            if (result.success) {
                await this.loadAllConfigurations();
                console.log('✅ 配置重新加载成功');

                // 触发配置重新加载事件
                window.dispatchEvent(new CustomEvent('dynamicConfigReloaded', {
                    detail: result.data
                }));

                return true;
            } else {
                throw new Error(result.message);
            }
        } catch (error) {
            console.error('❌ 配置重新加载失败:', error);
            return false;
        }
    }

    // 健康检查
    async healthCheck() {
        try {
            const response = await fetch('/api/config/health');
            const result = await response.json();
            return result.success ? result.data : null;
        } catch (error) {
            console.error('健康检查失败:', error);
            return null;
        }
    }

    // 获取配置摘要
    getConfigurationSummary() {
        return {
            serviceTypes: this.serviceTypes.size,
            serviceProviders: this.serviceProviders.size,
            capabilities: Object.keys(this.capabilityProviders).length,
            selectorMappings: Object.keys(this.selectorMappings).length,
            smartParametersCache: this.smartParameters.size,
            configLoaded: this.configLoaded,
            lastUpdate: this.cache.lastUpdate
        };
    }
}

// 全局实例
window.dynamicConfigLoader = new DynamicConfigLoader();

// 导出为模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DynamicConfigLoader;
}
