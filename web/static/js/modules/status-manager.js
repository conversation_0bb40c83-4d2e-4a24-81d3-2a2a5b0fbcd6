/**
 * 实时状态管理模块
 * 处理系统状态监控、进度跟踪和实时更新
 */

class StatusManager extends BaseComponent {
    constructor(element, options = {}) {
        super(element, options);
        this.websocket = null;
        this.connectionStatus = 'disconnected';
        this.systemStatus = {};
        this.activeJobs = new Map();
        this.statusHistory = [];
        this.reconnectAttempts = 0;
    }

    get defaultOptions() {
        return {
            websocketUrl: 'ws://localhost:8000/ws',
            reconnectInterval: 5000,
            maxReconnectAttempts: 10,
            statusUpdateInterval: 30000,
            historyLimit: 100,
            showNotifications: true
        };
    }

    init() {
        super.init();
        this.connectWebSocket();
        this.startStatusMonitoring();
        this.bindGlobalEvents();
    }

    render() {
        if (!this.element) return;

        this.element.innerHTML = `
            <div class="status-manager-container">
                <!-- 连接状态指示器 -->
                <div class="connection-indicator ${this.connectionStatus}">
                    <div class="indicator-dot"></div>
                    <span class="indicator-text">${this.getConnectionStatusText()}</span>
                    <div class="indicator-actions">
                        <button class="btn btn-sm btn-secondary" onclick="statusManager.reconnect()">
                            <i class="icon-refresh"></i>
                        </button>
                    </div>
                </div>

                <!-- 系统状态概览 -->
                <div class="system-overview">
                    <div class="status-grid">
                        <div class="status-item">
                            <div class="status-icon">
                                <i class="icon-cpu"></i>
                            </div>
                            <div class="status-info">
                                <span class="status-label">系统状态</span>
                                <span class="status-value" id="system-status">正常</span>
                            </div>
                        </div>

                        <div class="status-item">
                            <div class="status-icon">
                                <i class="icon-services"></i>
                            </div>
                            <div class="status-info">
                                <span class="status-label">AI服务</span>
                                <span class="status-value" id="ai-services-status">3/3 可用</span>
                            </div>
                        </div>

                        <div class="status-item">
                            <div class="status-icon">
                                <i class="icon-davinci"></i>
                            </div>
                            <div class="status-info">
                                <span class="status-label">DaVinci</span>
                                <span class="status-value" id="davinci-status">未连接</span>
                            </div>
                        </div>

                        <div class="status-item">
                            <div class="status-icon">
                                <i class="icon-tasks"></i>
                            </div>
                            <div class="status-info">
                                <span class="status-label">活动任务</span>
                                <span class="status-value" id="active-jobs">${this.activeJobs.size}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 活动任务列表 -->
                <div class="active-jobs-section">
                    <div class="section-header">
                        <h4>活动任务</h4>
                        <div class="section-actions">
                            <button class="btn btn-sm btn-secondary" onclick="statusManager.clearCompletedJobs()">
                                <i class="icon-clear"></i>
                                清理已完成
                            </button>
                        </div>
                    </div>
                    <div class="jobs-list" id="active-jobs-list">
                        ${this.renderActiveJobs()}
                    </div>
                </div>

                <!-- 状态历史 -->
                <div class="status-history-section">
                    <div class="section-header">
                        <h4>状态历史</h4>
                        <div class="section-actions">
                            <button class="btn btn-sm btn-secondary" onclick="statusManager.clearHistory()">
                                <i class="icon-clear"></i>
                                清空历史
                            </button>
                        </div>
                    </div>
                    <div class="history-list" id="status-history">
                        ${this.renderStatusHistory()}
                    </div>
                </div>

                <!-- 实时日志 -->
                <div class="realtime-logs-section">
                    <div class="section-header">
                        <h4>实时日志</h4>
                        <div class="section-actions">
                            <label class="toggle-switch">
                                <input type="checkbox" id="auto-scroll-logs" checked>
                                <span class="toggle-slider"></span>
                                <span class="toggle-label">自动滚动</span>
                            </label>
                        </div>
                    </div>
                    <div class="logs-container" id="realtime-logs">
                        <div class="log-entry">
                            <span class="log-time">${new Date().toLocaleTimeString()}</span>
                            <span class="log-level info">INFO</span>
                            <span class="log-message">状态管理器已启动</span>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    bindEvents() {
        // WebSocket事件处理
        componentManager.on('websocketMessage', (event) => {
            this.handleWebSocketMessage(event.detail);
        });

        // 任务状态变化事件
        componentManager.on('jobStatusChange', (event) => {
            this.updateJobStatus(event.detail);
        });

        // 系统状态变化事件
        componentManager.on('systemStatusChange', (event) => {
            this.updateSystemStatus(event.detail);
        });
    }

    bindGlobalEvents() {
        // 监听页面可见性变化
        document.addEventListener('visibilitychange', () => {
            if (document.visibilityState === 'visible') {
                this.refreshAllStatus();
            }
        });

        // 监听网络状态变化
        window.addEventListener('online', () => {
            this.addLogEntry('网络连接已恢复', 'success');
            this.reconnect();
        });

        window.addEventListener('offline', () => {
            this.addLogEntry('网络连接已断开', 'warning');
        });
    }

    connectWebSocket() {
        if (this.websocket) {
            this.websocket.close();
        }

        try {
            this.websocket = new WebSocket(this.options.websocketUrl);
            this.connectionStatus = 'connecting';
            this.updateConnectionIndicator();

            this.websocket.onopen = () => {
                this.connectionStatus = 'connected';
                this.reconnectAttempts = 0;
                this.updateConnectionIndicator();
                this.addLogEntry('WebSocket连接已建立', 'success');

                if (this.options.showNotifications && window.app) {
                    window.app.showSuccess('实时连接已建立');
                }
            };

            this.websocket.onmessage = (event) => {
                try {
                    const data = JSON.parse(event.data);
                    this.handleWebSocketMessage(data);
                } catch (error) {
                    console.error('Failed to parse WebSocket message:', error);
                }
            };

            this.websocket.onclose = () => {
                this.connectionStatus = 'disconnected';
                this.updateConnectionIndicator();
                this.addLogEntry('WebSocket连接已断开', 'warning');
                this.scheduleReconnect();
            };

            this.websocket.onerror = (error) => {
                console.error('WebSocket error:', error);
                this.addLogEntry('WebSocket连接错误', 'error');
            };

        } catch (error) {
            console.error('Failed to create WebSocket connection:', error);
            this.connectionStatus = 'error';
            this.updateConnectionIndicator();
        }
    }

    handleWebSocketMessage(data) {
        componentManager.emit('websocketMessage', data);

        switch (data.type) {
            case 'status_update':
                this.updateSystemStatus(data.data);
                break;
            case 'job_progress':
                this.updateJobProgress(data.data);
                break;
            case 'job_complete':
                this.completeJob(data.data);
                break;
            case 'job_error':
                this.errorJob(data.data);
                break;
            case 'system_notification':
                this.handleSystemNotification(data.data);
                break;
            default:
                this.addLogEntry(`收到未知消息类型: ${data.type}`, 'info');
        }
    }

    updateSystemStatus(status) {
        this.systemStatus = { ...this.systemStatus, ...status };

        // 更新UI显示
        const systemStatusEl = document.getElementById('system-status');
        const aiServicesStatusEl = document.getElementById('ai-services-status');
        const davinciStatusEl = document.getElementById('davinci-status');

        if (systemStatusEl) {
            systemStatusEl.textContent = status.system || '正常';
            systemStatusEl.className = `status-value ${status.system_level || 'normal'}`;
        }

        if (aiServicesStatusEl && status.ai_services) {
            const available = status.ai_services.available || 0;
            const total = status.ai_services.total || 0;
            aiServicesStatusEl.textContent = `${available}/${total} 可用`;
            aiServicesStatusEl.className = `status-value ${available === total ? 'normal' : 'warning'}`;
        }

        if (davinciStatusEl && status.davinci) {
            davinciStatusEl.textContent = status.davinci.connected ? '已连接' : '未连接';
            davinciStatusEl.className = `status-value ${status.davinci.connected ? 'normal' : 'warning'}`;
        }

        this.addStatusToHistory('系统状态更新', status);
    }

    startJob(jobData) {
        const jobId = jobData.id || this.generateJobId();
        const job = {
            id: jobId,
            type: jobData.type,
            title: jobData.title || `任务 ${jobId}`,
            status: 'running',
            progress: 0,
            startTime: new Date(),
            data: jobData
        };

        this.activeJobs.set(jobId, job);
        this.updateActiveJobsDisplay();
        this.updateActiveJobsCount();

        this.addLogEntry(`任务开始: ${job.title}`, 'info');

        return jobId;
    }

    updateJobProgress(progressData) {
        const job = this.activeJobs.get(progressData.job_id);
        if (job) {
            job.progress = progressData.progress || 0;
            job.message = progressData.message;
            job.lastUpdate = new Date();

            this.updateActiveJobsDisplay();
            this.addLogEntry(`任务进度: ${job.title} - ${job.progress}%`, 'info');
        }
    }

    completeJob(jobData) {
        const job = this.activeJobs.get(jobData.job_id);
        if (job) {
            job.status = 'completed';
            job.progress = 100;
            job.endTime = new Date();
            job.result = jobData.result;

            this.updateActiveJobsDisplay();
            this.addLogEntry(`任务完成: ${job.title}`, 'success');

            if (this.options.showNotifications && window.app) {
                window.app.showSuccess(`任务完成: ${job.title}`);
            }
        }
    }

    errorJob(jobData) {
        const job = this.activeJobs.get(jobData.job_id);
        if (job) {
            job.status = 'error';
            job.error = jobData.error;
            job.endTime = new Date();

            this.updateActiveJobsDisplay();
            this.addLogEntry(`任务失败: ${job.title} - ${jobData.error}`, 'error');

            if (this.options.showNotifications && window.app) {
                window.app.showError(`任务失败: ${job.title}`);
            }
        }
    }

    handleSystemNotification(notification) {
        this.addLogEntry(notification.message, notification.level || 'info');

        if (this.options.showNotifications && window.app) {
            switch (notification.level) {
                case 'success':
                    window.app.showSuccess(notification.message);
                    break;
                case 'warning':
                    window.app.showWarning(notification.message);
                    break;
                case 'error':
                    window.app.showError(notification.message);
                    break;
                default:
                    window.app.showInfo(notification.message);
            }
        }
    }

    renderActiveJobs() {
        if (this.activeJobs.size === 0) {
            return '<div class="empty-state"><p>暂无活动任务</p></div>';
        }

        return Array.from(this.activeJobs.values()).map(job => `
            <div class="job-item ${job.status}">
                <div class="job-header">
                    <span class="job-title">${job.title}</span>
                    <span class="job-status ${job.status}">${this.getJobStatusText(job.status)}</span>
                </div>
                <div class="job-progress">
                    <div class="progress">
                        <div class="progress-bar progress-bar-${this.getProgressBarColor(job.status)}"
                             style="width: ${job.progress}%">
                            ${job.progress}%
                        </div>
                    </div>
                </div>
                <div class="job-meta">
                    <span class="job-time">开始: ${job.startTime.toLocaleTimeString()}</span>
                    ${job.message ? `<span class="job-message">${job.message}</span>` : ''}
                </div>
                ${job.status !== 'running' ? `
                    <div class="job-actions">
                        <button class="btn btn-sm btn-secondary" onclick="statusManager.removeJob('${job.id}')">
                            <i class="icon-remove"></i>
                        </button>
                    </div>
                ` : ''}
            </div>
        `).join('');
    }

    renderStatusHistory() {
        if (this.statusHistory.length === 0) {
            return '<div class="empty-state"><p>暂无状态历史</p></div>';
        }

        return this.statusHistory.slice(0, 10).map(entry => `
            <div class="history-item">
                <div class="history-time">${entry.timestamp.toLocaleTimeString()}</div>
                <div class="history-content">
                    <span class="history-title">${entry.title}</span>
                    <span class="history-details">${JSON.stringify(entry.data)}</span>
                </div>
            </div>
        `).join('');
    }

    updateActiveJobsDisplay() {
        const jobsList = document.getElementById('active-jobs-list');
        if (jobsList) {
            jobsList.innerHTML = this.renderActiveJobs();
        }
    }

    updateActiveJobsCount() {
        const activeJobsEl = document.getElementById('active-jobs');
        if (activeJobsEl) {
            activeJobsEl.textContent = this.activeJobs.size;
        }
    }

    updateConnectionIndicator() {
        const indicator = this.element?.querySelector('.connection-indicator');
        if (indicator) {
            indicator.className = `connection-indicator ${this.connectionStatus}`;
            const text = indicator.querySelector('.indicator-text');
            if (text) {
                text.textContent = this.getConnectionStatusText();
            }
        }
    }

    addLogEntry(message, level = 'info') {
        const logsContainer = document.getElementById('realtime-logs');
        if (!logsContainer) return;

        const logEntry = document.createElement('div');
        logEntry.className = 'log-entry';
        logEntry.innerHTML = `
            <span class="log-time">${new Date().toLocaleTimeString()}</span>
            <span class="log-level ${level}">${level.toUpperCase()}</span>
            <span class="log-message">${message}</span>
        `;

        logsContainer.insertBefore(logEntry, logsContainer.firstChild);

        // 限制日志条目数量
        const entries = logsContainer.querySelectorAll('.log-entry');
        if (entries.length > 100) {
            entries[entries.length - 1].remove();
        }

        // 自动滚动
        const autoScroll = document.getElementById('auto-scroll-logs');
        if (autoScroll && autoScroll.checked) {
            logEntry.scrollIntoView({ behavior: 'smooth' });
        }
    }

    addStatusToHistory(title, data) {
        this.statusHistory.unshift({
            timestamp: new Date(),
            title: title,
            data: data
        });

        // 限制历史记录数量
        if (this.statusHistory.length > this.options.historyLimit) {
            this.statusHistory = this.statusHistory.slice(0, this.options.historyLimit);
        }

        const historyEl = document.getElementById('status-history');
        if (historyEl) {
            historyEl.innerHTML = this.renderStatusHistory();
        }
    }

    getConnectionStatusText() {
        const statusTexts = {
            'disconnected': '未连接',
            'connecting': '连接中...',
            'connected': '已连接',
            'error': '连接错误'
        };
        return statusTexts[this.connectionStatus] || '未知状态';
    }

    getJobStatusText(status) {
        const statusTexts = {
            'running': '运行中',
            'completed': '已完成',
            'error': '失败',
            'cancelled': '已取消'
        };
        return statusTexts[status] || status;
    }

    getProgressBarColor(status) {
        const colors = {
            'running': 'primary',
            'completed': 'success',
            'error': 'error',
            'cancelled': 'warning'
        };
        return colors[status] || 'primary';
    }

    generateJobId() {
        return 'job_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    scheduleReconnect() {
        if (this.reconnectAttempts < this.options.maxReconnectAttempts) {
            setTimeout(() => {
                this.reconnectAttempts++;
                this.addLogEntry(`尝试重连 (${this.reconnectAttempts}/${this.options.maxReconnectAttempts})`, 'info');
                this.connectWebSocket();
            }, this.options.reconnectInterval);
        } else {
            this.addLogEntry('已达到最大重连次数，停止重连', 'error');
        }
    }

    reconnect() {
        this.reconnectAttempts = 0;
        this.connectWebSocket();
    }

    startStatusMonitoring() {
        setInterval(() => {
            this.refreshAllStatus();
        }, this.options.statusUpdateInterval);
    }

    async refreshAllStatus() {
        try {
            // 刷新系统状态
            const response = await fetch('/api/status');
            if (response.ok) {
                const data = await response.json();
                this.updateSystemStatus(data);
            }
        } catch (error) {
            console.error('Failed to refresh status:', error);
        }
    }

    clearCompletedJobs() {
        const completedJobs = Array.from(this.activeJobs.entries())
            .filter(([id, job]) => job.status === 'completed' || job.status === 'error');

        completedJobs.forEach(([id]) => {
            this.activeJobs.delete(id);
        });

        this.updateActiveJobsDisplay();
        this.updateActiveJobsCount();
        this.addLogEntry(`清理了 ${completedJobs.length} 个已完成任务`, 'info');
    }

    removeJob(jobId) {
        if (this.activeJobs.has(jobId)) {
            const job = this.activeJobs.get(jobId);
            this.activeJobs.delete(jobId);
            this.updateActiveJobsDisplay();
            this.updateActiveJobsCount();
            this.addLogEntry(`移除任务: ${job.title}`, 'info');
        }
    }

    clearHistory() {
        this.statusHistory = [];
        const historyEl = document.getElementById('status-history');
        if (historyEl) {
            historyEl.innerHTML = '<div class="empty-state"><p>暂无状态历史</p></div>';
        }
        this.addLogEntry('状态历史已清空', 'info');
    }

    destroy() {
        super.destroy();
        if (this.websocket) {
            this.websocket.close();
        }
    }
}

// 创建全局状态管理器实例
window.statusManager = new StatusManager();

// 注册组件
componentManager.registerComponent('StatusManager', StatusManager);
