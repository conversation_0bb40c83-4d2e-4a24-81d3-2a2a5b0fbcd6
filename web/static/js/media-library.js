/**
 * DaVinci AI Co-pilot PRO - 媒体资源库管理系统
 *
 * 功能：
 * - 统一管理所有类型的媒体资源
 * - 本地持久化存储
 * - 分类和标签系统
 * - 搜索和筛选功能
 */

class MediaLibrary {
    constructor() {
        this.storageKey = 'davinci_media_library';
        this.mediaItems = new Map();
        this.categories = {
            'ai_image': 'AI生成图像',
            'ai_video': 'AI生成视频',
            'davinci_frame': 'DaVinci静帧',
            'uploaded_image': '上传图片',
            'ai_audio': 'AI语音合成'
        };
        this.init();
    }

    /**
     * 初始化媒体库
     */
    init() {
        this.loadFromStorage();
        this.setupEventListeners();
        console.log('Media Library initialized with', this.mediaItems.size, 'items');
    }

    /**
     * 从本地存储加载数据
     */
    loadFromStorage() {
        try {
            const stored = localStorage.getItem(this.storageKey);
            if (stored) {
                const data = JSON.parse(stored);
                this.mediaItems = new Map(data.items || []);
            }
        } catch (error) {
            console.error('Failed to load media library from storage:', error);
            this.mediaItems = new Map();
        }
    }

    /**
     * 保存到本地存储
     */
    saveToStorage() {
        try {
            const data = {
                items: Array.from(this.mediaItems.entries()),
                lastUpdated: new Date().toISOString()
            };
            localStorage.setItem(this.storageKey, JSON.stringify(data));
        } catch (error) {
            console.error('Failed to save media library to storage:', error);
        }
    }

    /**
     * 生成唯一ID
     */
    generateId() {
        return 'media_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    /**
     * 添加媒体项目
     */
    addMediaItem(mediaData) {
        const id = this.generateId();
        const mediaItem = {
            id: id,
            name: mediaData.name || `媒体文件_${Date.now()}`,
            type: mediaData.type, // 'image', 'video', 'audio'
            category: mediaData.category, // 'ai_image', 'ai_video', etc.
            filePath: mediaData.filePath,
            url: mediaData.url,
            thumbnailUrl: mediaData.thumbnailUrl || mediaData.url,
            size: mediaData.size || 0,
            duration: mediaData.duration, // for video/audio
            dimensions: mediaData.dimensions, // for images/videos
            metadata: {
                source: mediaData.source || 'unknown',
                prompt: mediaData.prompt, // AI generation prompt
                model: mediaData.model, // AI model used
                parameters: mediaData.parameters, // generation parameters
                tags: mediaData.tags || [],
                description: mediaData.description || ''
            },
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };

        this.mediaItems.set(id, mediaItem);
        this.saveToStorage();
        this.notifyUpdate('added', mediaItem);

        return mediaItem;
    }

    /**
     * 获取媒体项目
     */
    getMediaItem(id) {
        return this.mediaItems.get(id);
    }

    /**
     * 获取所有媒体项目
     */
    getAllMediaItems() {
        return Array.from(this.mediaItems.values());
    }

    /**
     * 按类别获取媒体项目
     */
    getMediaItemsByCategory(category) {
        return this.getAllMediaItems().filter(item => item.category === category);
    }

    /**
     * 按类型获取媒体项目
     */
    getMediaItemsByType(type) {
        return this.getAllMediaItems().filter(item => item.type === type);
    }

    /**
     * 搜索媒体项目
     */
    searchMediaItems(query, filters = {}) {
        let items = this.getAllMediaItems();

        // 文本搜索
        if (query && query.trim()) {
            const searchTerm = query.toLowerCase();
            items = items.filter(item =>
                item.name.toLowerCase().includes(searchTerm) ||
                (item.metadata.description && item.metadata.description.toLowerCase().includes(searchTerm)) ||
                (item.metadata.prompt && item.metadata.prompt.toLowerCase().includes(searchTerm)) ||
                (item.metadata.tags && item.metadata.tags.some(tag => tag.toLowerCase().includes(searchTerm)))
            );
        }

        // 类别筛选
        if (filters.category) {
            items = items.filter(item => item.category === filters.category);
        }

        // 类型筛选
        if (filters.type) {
            items = items.filter(item => item.type === filters.type);
        }

        // 日期范围筛选
        if (filters.dateFrom) {
            items = items.filter(item => new Date(item.createdAt) >= new Date(filters.dateFrom));
        }
        if (filters.dateTo) {
            items = items.filter(item => new Date(item.createdAt) <= new Date(filters.dateTo));
        }

        // 排序
        const sortBy = filters.sortBy || 'createdAt';
        const sortOrder = filters.sortOrder || 'desc';

        items.sort((a, b) => {
            let aVal = a[sortBy] || a.metadata[sortBy];
            let bVal = b[sortBy] || b.metadata[sortBy];

            if (sortBy === 'createdAt' || sortBy === 'updatedAt') {
                aVal = new Date(aVal);
                bVal = new Date(bVal);
            }

            if (sortOrder === 'desc') {
                return bVal > aVal ? 1 : -1;
            } else {
                return aVal > bVal ? 1 : -1;
            }
        });

        return items;
    }

    /**
     * 更新媒体项目
     */
    updateMediaItem(id, updates) {
        const item = this.mediaItems.get(id);
        if (!item) return null;

        const updatedItem = {
            ...item,
            ...updates,
            updatedAt: new Date().toISOString()
        };

        this.mediaItems.set(id, updatedItem);
        this.saveToStorage();
        this.notifyUpdate('updated', updatedItem);

        return updatedItem;
    }

    /**
     * 删除媒体项目
     */
    deleteMediaItem(id) {
        const item = this.mediaItems.get(id);
        if (!item) return false;

        this.mediaItems.delete(id);
        this.saveToStorage();
        this.notifyUpdate('deleted', item);

        return true;
    }

    /**
     * 批量删除
     */
    deleteMultipleItems(ids) {
        const deletedItems = [];
        ids.forEach(id => {
            const item = this.mediaItems.get(id);
            if (item) {
                this.mediaItems.delete(id);
                deletedItems.push(item);
            }
        });

        if (deletedItems.length > 0) {
            this.saveToStorage();
            this.notifyUpdate('bulk_deleted', deletedItems);
        }

        return deletedItems;
    }

    /**
     * 获取统计信息
     */
    getStatistics() {
        const items = this.getAllMediaItems();
        const stats = {
            total: items.length,
            byCategory: {},
            byType: {},
            totalSize: 0,
            recentItems: items.slice(0, 10)
        };

        items.forEach(item => {
            // 按类别统计
            stats.byCategory[item.category] = (stats.byCategory[item.category] || 0) + 1;

            // 按类型统计
            stats.byType[item.type] = (stats.byType[item.type] || 0) + 1;

            // 总大小
            stats.totalSize += item.size || 0;
        });

        return stats;
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 监听存储变化（多标签页同步）
        window.addEventListener('storage', (e) => {
            if (e.key === this.storageKey) {
                this.loadFromStorage();
                this.notifyUpdate('storage_sync', null);
            }
        });
    }

    /**
     * 通知更新
     */
    notifyUpdate(action, data) {
        const event = new CustomEvent('mediaLibraryUpdate', {
            detail: { action, data }
        });
        document.dispatchEvent(event);
    }

    /**
     * 导出数据
     */
    exportData() {
        const data = {
            items: Array.from(this.mediaItems.entries()),
            categories: this.categories,
            exportedAt: new Date().toISOString(),
            version: '1.0'
        };
        return JSON.stringify(data, null, 2);
    }

    /**
     * 导入数据
     */
    importData(jsonData) {
        try {
            const data = JSON.parse(jsonData);
            if (data.items && Array.isArray(data.items)) {
                this.mediaItems = new Map(data.items);
                this.saveToStorage();
                this.notifyUpdate('imported', data);
                return true;
            }
        } catch (error) {
            console.error('Failed to import media library data:', error);
        }
        return false;
    }
}

/**
 * 媒体库UI组件
 */
class MediaLibraryUI {
    constructor(mediaLibrary) {
        this.mediaLibrary = mediaLibrary;
        this.currentView = 'grid'; // 'grid' or 'list'
        this.currentFilters = {};
        this.selectedItems = new Set();
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupAudioHandlers();
    }

    /**
     * 设置音频文件专用处理器
     */
    setupAudioHandlers() {
        // 监听语音合成模块的更新事件
        document.addEventListener('mediaLibraryUpdate', (event) => {
            const { action, item, category } = event.detail;

            if (action === 'item_added' && category === 'ai_audio') {
                console.log('🎵 New audio item added to media library:', item);
                this.refreshMediaGrid(); // 重新加载媒体项目
                this.showAudioAddedNotification(item);
            }
        });
    }

    /**
     * 显示音频添加成功通知
     */
    showAudioAddedNotification(item) {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = 'audio-notification';
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas fa-music"></i>
                <span>语音文件已添加到媒体库: ${item.name}</span>
                <button onclick="this.parentElement.parentElement.remove()">×</button>
            </div>
        `;

        // 添加样式
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #28a745;
            color: white;
            padding: 12px 16px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10000;
            animation: slideInRight 0.3s ease-out;
        `;

        document.body.appendChild(notification);

        // 3秒后自动移除
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 3000);
    }

    /**
     * 播放音频预览
     */
    playAudioPreview(itemId, audioUrl) {
        // 停止当前播放的音频
        if (this.currentAudio) {
            this.currentAudio.pause();
            this.currentAudio = null;
        }

        // 重置所有播放按钮
        document.querySelectorAll('.play-btn-mini i').forEach(icon => {
            icon.className = 'fas fa-play';
        });

        // 创建新的音频元素
        const audio = new Audio(audioUrl);
        this.currentAudio = audio;

        // 找到对应的播放按钮
        const playBtn = document.querySelector(`[onclick*="${itemId}"] .play-btn-mini i`);

        if (playBtn) {
            playBtn.className = 'fas fa-spinner fa-spin';
        }

        // 设置音频事件监听器
        audio.addEventListener('loadeddata', () => {
            if (playBtn) {
                playBtn.className = 'fas fa-pause';
            }
            audio.play().catch(error => {
                console.error('Audio play failed:', error);
                if (playBtn) {
                    playBtn.className = 'fas fa-play';
                }
            });
        });

        audio.addEventListener('ended', () => {
            if (playBtn) {
                playBtn.className = 'fas fa-play';
            }
            this.currentAudio = null;
        });

        audio.addEventListener('error', (error) => {
            console.error('Audio load failed:', error);
            if (playBtn) {
                playBtn.className = 'fas fa-exclamation-triangle';
            }
            this.currentAudio = null;
        });

        // 加载音频
        audio.load();
    }

    /**
     * 下载音频文件
     */
    downloadAudio(audioUrl, fileName) {
        try {
            // 创建下载链接
            const link = document.createElement('a');
            link.href = audioUrl;
            link.download = fileName || 'audio.mp3';
            link.style.display = 'none';

            // 添加到DOM并触发下载
            document.body.appendChild(link);
            link.click();

            // 清理
            document.body.removeChild(link);

            console.log('Audio download initiated:', fileName);
        } catch (error) {
            console.error('Audio download failed:', error);
            alert('下载失败，请稍后重试');
        }
    }

    /**
     * 渲染媒体库界面
     */
    render(containerId) {
        const container = document.getElementById(containerId);
        if (!container) return;

        container.innerHTML = `
            <div class="media-library">
                <div class="media-library-header">
                    <div class="media-library-title">
                        <h2>📚 媒体资源库</h2>
                        <div class="media-stats">
                            <span id="media-count">0 个项目</span>
                        </div>
                    </div>
                    <div class="media-library-actions">
                        <button class="btn btn-primary" onclick="mediaLibraryUI.showUploadDialog()">
                            <i class="fas fa-upload"></i> 上传文件
                        </button>
                        <button class="btn btn-secondary" onclick="mediaLibraryUI.toggleView()">
                            <i class="fas fa-th" id="view-toggle-icon"></i>
                        </button>
                    </div>
                </div>

                <div class="media-library-toolbar">
                    <div class="search-filters">
                        <div class="search-box">
                            <input type="text" id="media-search" placeholder="搜索媒体文件..." />
                            <i class="fas fa-search"></i>
                        </div>
                        <select id="category-filter">
                            <option value="">所有类别</option>
                            <option value="ai_image">AI生成图像</option>
                            <option value="ai_video">AI生成视频</option>
                            <option value="davinci_frame">DaVinci静帧</option>
                            <option value="uploaded_image">上传图片</option>
                            <option value="ai_audio">AI语音合成</option>
                        </select>
                        <select id="type-filter">
                            <option value="">所有类型</option>
                            <option value="image">图片</option>
                            <option value="video">视频</option>
                            <option value="audio">音频</option>
                        </select>
                        <select id="sort-filter">
                            <option value="createdAt-desc">最新创建</option>
                            <option value="createdAt-asc">最早创建</option>
                            <option value="name-asc">名称 A-Z</option>
                            <option value="name-desc">名称 Z-A</option>
                            <option value="size-desc">文件大小 ↓</option>
                            <option value="size-asc">文件大小 ↑</option>
                        </select>
                    </div>
                    <div class="bulk-actions" id="bulk-actions" style="display: none;">
                        <span id="selected-count">0 个已选择</span>
                        <button class="btn btn-danger btn-sm" onclick="mediaLibraryUI.deleteSelected()">
                            <i class="fas fa-trash"></i> 删除选中
                        </button>
                        <button class="btn btn-secondary btn-sm" onclick="mediaLibraryUI.clearSelection()">
                            取消选择
                        </button>
                    </div>
                </div>

                <div class="media-library-content">
                    <div id="media-grid" class="media-grid"></div>
                    <div id="media-list" class="media-list" style="display: none;"></div>
                    <div id="media-empty" class="media-empty" style="display: none;">
                        <div class="empty-state">
                            <i class="fas fa-folder-open"></i>
                            <h3>媒体库为空</h3>
                            <p>开始创建或上传一些媒体文件吧！</p>
                            <button class="btn btn-primary" onclick="mediaLibraryUI.showUploadDialog()">
                                上传文件
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 预览模态框 -->
            <div id="media-preview-modal" class="modal" style="display: none;">
                <div class="modal-backdrop"></div>
                <div class="modal-content media-preview-content">
                    <div class="modal-header">
                        <h3 id="preview-title">媒体预览</h3>
                        <button class="modal-close" onclick="mediaLibraryUI.closePreview()">×</button>
                    </div>
                    <div class="modal-body">
                        <div id="preview-container"></div>
                        <div id="preview-info" class="media-info"></div>
                    </div>
                    <div class="modal-actions">
                        <button class="btn btn-primary" id="use-media-btn" onclick="mediaLibraryUI.useSelectedMedia()">
                            使用此媒体
                        </button>
                        <button class="btn btn-danger" id="delete-media-btn" onclick="mediaLibraryUI.deleteCurrentMedia()">
                            删除
                        </button>
                        <button class="btn btn-secondary" onclick="mediaLibraryUI.closePreview()">
                            关闭
                        </button>
                    </div>
                </div>
            </div>

            <!-- 上传对话框 -->
            <div id="upload-modal" class="modal" style="display: none;">
                <div class="modal-backdrop"></div>
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>上传媒体文件</h3>
                        <button class="modal-close" onclick="mediaLibraryUI.closeUploadDialog()">×</button>
                    </div>
                    <div class="modal-body">
                        <div class="upload-area" id="upload-area">
                            <i class="fas fa-cloud-upload-alt"></i>
                            <p>拖拽文件到此处或点击选择文件</p>
                            <input type="file" id="file-input" multiple accept="image/*,video/*,audio/*" style="display: none;">
                            <button class="btn btn-primary" onclick="document.getElementById('file-input').click()">
                                选择文件
                            </button>
                        </div>
                        <div id="upload-progress" style="display: none;">
                            <div class="progress-bar">
                                <div class="progress-fill" id="progress-fill"></div>
                            </div>
                            <div id="upload-status">上传中...</div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        this.setupUIEventListeners();
        this.refreshMediaGrid();
    }

    /**
     * 设置UI事件监听器
     */
    setupUIEventListeners() {
        // 搜索
        const searchInput = document.getElementById('media-search');
        if (searchInput) {
            searchInput.addEventListener('input', () => this.handleSearch());
        }

        // 筛选器
        ['category-filter', 'type-filter', 'sort-filter'].forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.addEventListener('change', () => this.handleFilterChange());
            }
        });

        // 文件上传
        const fileInput = document.getElementById('file-input');
        if (fileInput) {
            fileInput.addEventListener('change', (e) => this.handleFileUpload(e));
        }

        // 拖拽上传
        const uploadArea = document.getElementById('upload-area');
        if (uploadArea) {
            uploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadArea.classList.add('drag-over');
            });

            uploadArea.addEventListener('dragleave', () => {
                uploadArea.classList.remove('drag-over');
            });

            uploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadArea.classList.remove('drag-over');
                this.handleFileUpload(e);
            });
        }
    }

    /**
     * 设置全局事件监听器
     */
    setupEventListeners() {
        // 监听媒体库更新
        document.addEventListener('mediaLibraryUpdate', (e) => {
            this.refreshMediaGrid();
            this.updateStats();
        });
    }

    /**
     * 刷新媒体网格
     */
    async refreshMediaGrid() {
        try {
            // 从后端API加载媒体项目
            await this.loadMediaItemsFromAPI();

            // 获取筛选后的项目
            const items = this.mediaLibrary.searchMediaItems(
                document.getElementById('media-search')?.value || '',
                this.currentFilters
            );

            if (this.currentView === 'grid') {
                this.renderGridView(items);
            } else {
                this.renderListView(items);
            }

            this.updateStats();
            this.updateEmptyState(items.length === 0);
        } catch (error) {
            console.error('Failed to refresh media grid:', error);
            this.showError('加载媒体库失败，请刷新页面重试');
        }
    }

    /**
     * 从API加载媒体项目
     */
    async loadMediaItemsFromAPI() {
        try {
            const query = document.getElementById('media-search')?.value || '';
            const category = document.getElementById('category-filter')?.value || '';
            const typeFilter = document.getElementById('type-filter')?.value || '';

            const params = new URLSearchParams();
            if (query) params.append('query', query);
            if (category) params.append('category', category);
            if (typeFilter) params.append('type_filter', typeFilter);

            const response = await fetch(`/api/media-library/items?${params.toString()}`);
            const result = await response.json();

            if (result.success) {
                // 清空现有数据并加载新数据
                this.mediaLibrary.mediaItems.clear();

                result.data.forEach(item => {
                    this.mediaLibrary.mediaItems.set(item.id, item);
                });

                console.log(`📚 Loaded ${result.data.length} media items from API`);
            } else {
                throw new Error(result.error || 'Failed to load media items');
            }
        } catch (error) {
            console.error('Failed to load media items from API:', error);
            throw error;
        }
    }

    /**
     * 显示错误消息
     */
    showError(message) {
        // 创建错误通知
        const notification = document.createElement('div');
        notification.className = 'error-notification';
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas fa-exclamation-triangle"></i>
                <span>${message}</span>
                <button onclick="this.parentElement.parentElement.remove()">×</button>
            </div>
        `;

        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #dc3545;
            color: white;
            padding: 12px 16px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10000;
            animation: slideInRight 0.3s ease-out;
        `;

        document.body.appendChild(notification);

        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 5000);
    }

    /**
     * 渲染网格视图
     */
    renderGridView(items) {
        const gridContainer = document.getElementById('media-grid');
        if (!gridContainer) return;

        gridContainer.innerHTML = items.map(item => `
            <div class="media-item" data-id="${item.id}" onclick="mediaLibraryUI.selectItem('${item.id}', event)">
                <div class="media-thumbnail">
                    ${this.renderMediaThumbnail(item)}
                    <div class="media-overlay">
                        <button class="btn-icon" onclick="mediaLibraryUI.previewMedia('${item.id}'); event.stopPropagation();">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn-icon" onclick="mediaLibraryUI.useMedia('${item.id}'); event.stopPropagation();">
                            <i class="fas fa-arrow-right"></i>
                        </button>
                    </div>
                    <div class="media-type-badge">${this.getCategoryIcon(item.category)}</div>
                </div>
                <div class="media-info">
                    <div class="media-name" title="${item.name}">${item.name}</div>
                    <div class="media-meta">
                        <span class="media-date">${this.formatDate(item.createdAt)}</span>
                        <span class="media-size">${this.formatFileSize(item.size)}</span>
                    </div>
                </div>
                <div class="media-checkbox">
                    <input type="checkbox" onchange="mediaLibraryUI.toggleSelection('${item.id}', this.checked)">
                </div>
            </div>
        `).join('');

        gridContainer.style.display = 'grid';
        document.getElementById('media-list').style.display = 'none';
    }

    /**
     * 渲染列表视图
     */
    renderListView(items) {
        const listContainer = document.getElementById('media-list');
        if (!listContainer) return;

        listContainer.innerHTML = `
            <table class="media-table">
                <thead>
                    <tr>
                        <th><input type="checkbox" onchange="mediaLibraryUI.toggleSelectAll(this.checked)"></th>
                        <th>预览</th>
                        <th>名称</th>
                        <th>类别</th>
                        <th>大小</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    ${items.map(item => `
                        <tr class="media-row" data-id="${item.id}">
                            <td>
                                <input type="checkbox" onchange="mediaLibraryUI.toggleSelection('${item.id}', this.checked)">
                            </td>
                            <td>
                                <div class="media-thumbnail-small">
                                    ${this.renderMediaThumbnail(item, true)}
                                </div>
                            </td>
                            <td>
                                <div class="media-name-cell">
                                    <span class="media-name">${item.name}</span>
                                    <span class="media-description">${item.metadata.description || ''}</span>
                                </div>
                            </td>
                            <td>
                                <span class="category-badge">${this.getCategoryIcon(item.category)} ${this.mediaLibrary.categories[item.category]}</span>
                            </td>
                            <td>${this.formatFileSize(item.size)}</td>
                            <td>${this.formatDate(item.createdAt)}</td>
                            <td>
                                <div class="action-buttons">
                                    <button class="btn-icon" onclick="mediaLibraryUI.previewMedia('${item.id}')" title="预览">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn-icon" onclick="mediaLibraryUI.useMedia('${item.id}')" title="使用">
                                        <i class="fas fa-arrow-right"></i>
                                    </button>
                                    <button class="btn-icon btn-danger" onclick="mediaLibraryUI.deleteMedia('${item.id}')" title="删除">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        `;

        listContainer.style.display = 'block';
        document.getElementById('media-grid').style.display = 'none';
    }

    /**
     * 渲染媒体缩略图
     */
    renderMediaThumbnail(item, small = false) {
        const sizeClass = small ? 'small' : '';

        switch (item.type) {
            case 'image':
                return `<img src="${item.thumbnailUrl || item.url}" alt="${item.name}" class="media-thumb ${sizeClass}" loading="lazy">`;
            case 'video':
                return `
                    <div class="video-thumbnail ${sizeClass}">
                        <img src="${item.thumbnailUrl || '/static/images/video-placeholder.png'}" alt="${item.name}" class="media-thumb" loading="lazy">
                        <div class="play-overlay"><i class="fas fa-play"></i></div>
                        ${item.duration ? `<div class="duration-badge">${this.formatDuration(item.duration)}</div>` : ''}
                    </div>
                `;
            case 'audio':
                return `
                    <div class="audio-thumbnail ${sizeClass}">
                        <div class="audio-icon-container">
                            <i class="fas fa-music audio-icon"></i>
                            <div class="audio-waveform">
                                <span></span><span></span><span></span><span></span><span></span>
                            </div>
                        </div>
                        ${item.duration ? `<div class="duration-badge">${this.formatDuration(item.duration)}</div>` : ''}
                        ${item.metadata?.provider ? `<div class="provider-badge">${item.metadata.provider.toUpperCase()}</div>` : ''}
                        <div class="audio-controls-mini">
                            <button class="play-btn-mini" onclick="mediaLibraryUI.playAudioPreview('${item.id}', '${item.url}'); event.stopPropagation();" title="播放预览">
                                <i class="fas fa-play"></i>
                            </button>
                        </div>
                    </div>
                `;
            default:
                return `<div class="default-thumbnail ${sizeClass}"><i class="fas fa-file"></i></div>`;
        }
    }

    /**
     * 获取类别图标
     */
    getCategoryIcon(category) {
        const icons = {
            'ai_image': '🎨',
            'ai_video': '🎥',
            'davinci_frame': '📸',
            'uploaded_image': '📁',
            'ai_audio': '🎵'
        };
        return icons[category] || '📄';
    }

    /**
     * 格式化文件大小
     */
    formatFileSize(bytes) {
        if (!bytes) return '未知';
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(1024));
        return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
    }

    /**
     * 格式化日期
     */
    formatDate(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diff = now - date;

        if (diff < 24 * 60 * 60 * 1000) {
            return '今天 ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
        } else if (diff < 7 * 24 * 60 * 60 * 1000) {
            const days = Math.floor(diff / (24 * 60 * 60 * 1000));
            return `${days}天前`;
        } else {
            return date.toLocaleDateString('zh-CN');
        }
    }

    /**
     * 格式化时长
     */
    formatDuration(seconds) {
        const mins = Math.floor(seconds / 60);
        const secs = Math.floor(seconds % 60);
        return `${mins}:${secs.toString().padStart(2, '0')}`;
    }

    /**
     * 处理搜索
     */
    handleSearch() {
        this.refreshMediaGrid();
    }

    /**
     * 处理筛选器变化
     */
    handleFilterChange() {
        const categoryFilter = document.getElementById('category-filter')?.value;
        const typeFilter = document.getElementById('type-filter')?.value;
        const sortFilter = document.getElementById('sort-filter')?.value;

        this.currentFilters = {
            category: categoryFilter || undefined,
            type: typeFilter || undefined
        };

        if (sortFilter) {
            const [sortBy, sortOrder] = sortFilter.split('-');
            this.currentFilters.sortBy = sortBy;
            this.currentFilters.sortOrder = sortOrder;
        }

        this.refreshMediaGrid();
    }

    /**
     * 切换视图模式
     */
    toggleView() {
        this.currentView = this.currentView === 'grid' ? 'list' : 'grid';
        const icon = document.getElementById('view-toggle-icon');
        if (icon) {
            icon.className = this.currentView === 'grid' ? 'fas fa-list' : 'fas fa-th';
        }
        this.refreshMediaGrid();
    }

    /**
     * 选择项目
     */
    selectItem(id, event) {
        if (event && event.ctrlKey) {
            this.toggleSelection(id, !this.selectedItems.has(id));
        } else {
            this.previewMedia(id);
        }
    }

    /**
     * 切换选择状态
     */
    toggleSelection(id, selected) {
        if (selected) {
            this.selectedItems.add(id);
        } else {
            this.selectedItems.delete(id);
        }
        this.updateSelectionUI();
    }

    /**
     * 全选/取消全选
     */
    toggleSelectAll(selected) {
        const items = this.mediaLibrary.searchMediaItems(
            document.getElementById('media-search')?.value || '',
            this.currentFilters
        );

        if (selected) {
            items.forEach(item => this.selectedItems.add(item.id));
        } else {
            this.selectedItems.clear();
        }

        this.updateSelectionUI();
        this.refreshMediaGrid();
    }

    /**
     * 清除选择
     */
    clearSelection() {
        this.selectedItems.clear();
        this.updateSelectionUI();
        this.refreshMediaGrid();
    }

    /**
     * 更新选择UI
     */
    updateSelectionUI() {
        const bulkActions = document.getElementById('bulk-actions');
        const selectedCount = document.getElementById('selected-count');

        if (this.selectedItems.size > 0) {
            bulkActions.style.display = 'flex';
            selectedCount.textContent = `${this.selectedItems.size} 个已选择`;
        } else {
            bulkActions.style.display = 'none';
        }
    }

    /**
     * 删除选中项目
     */
    deleteSelected() {
        if (this.selectedItems.size === 0) return;

        if (confirm(`确定要删除选中的 ${this.selectedItems.size} 个项目吗？`)) {
            const ids = Array.from(this.selectedItems);
            this.mediaLibrary.deleteMultipleItems(ids);
            this.selectedItems.clear();
            this.updateSelectionUI();
        }
    }

    /**
     * 预览媒体
     */
    previewMedia(id) {
        const item = this.mediaLibrary.getMediaItem(id);
        if (!item) return;

        this.currentPreviewItem = item;

        const modal = document.getElementById('media-preview-modal');
        const title = document.getElementById('preview-title');
        const container = document.getElementById('preview-container');
        const info = document.getElementById('preview-info');

        title.textContent = item.name;

        // 渲染预览内容
        container.innerHTML = this.renderPreviewContent(item);

        // 渲染信息
        info.innerHTML = this.renderMediaInfo(item);

        modal.style.display = 'flex';
    }

    /**
     * 渲染预览内容
     */
    renderPreviewContent(item) {
        switch (item.type) {
            case 'image':
                return `<img src="${item.url}" alt="${item.name}" class="preview-image">`;
            case 'video':
                return `
                    <video controls class="preview-video">
                        <source src="${item.url}" type="video/mp4">
                        您的浏览器不支持视频播放。
                    </video>
                `;
            case 'audio':
                return `
                    <div class="audio-preview">
                        <div class="audio-visual">
                            <div class="audio-icon-large">
                                <i class="fas fa-music"></i>
                                <div class="audio-waveform-large">
                                    <span></span><span></span><span></span><span></span><span></span>
                                    <span></span><span></span><span></span><span></span><span></span>
                                </div>
                            </div>
                            ${item.metadata?.text ? `
                                <div class="audio-text-content">
                                    <h4>文本内容</h4>
                                    <p>"${item.metadata.text}"</p>
                                </div>
                            ` : ''}
                        </div>
                        <div class="audio-player-container">
                            <audio controls class="preview-audio" preload="metadata">
                                <source src="${item.url}" type="audio/mpeg">
                                您的浏览器不支持音频播放。
                            </audio>
                            <div class="audio-controls-extended">
                                <button class="btn btn-secondary" onclick="mediaLibraryUI.downloadAudio('${item.url}', '${item.name}')">
                                    <i class="fas fa-download"></i> 下载音频
                                </button>
                                ${item.metadata?.voice_name ? `
                                    <div class="voice-info">
                                        <i class="fas fa-user"></i> ${item.metadata.voice_name}
                                    </div>
                                ` : ''}
                            </div>
                        </div>
                    </div>
                `;
            default:
                return `<div class="preview-placeholder">无法预览此文件类型</div>`;
        }
    }

    /**
     * 渲染媒体信息
     */
    renderMediaInfo(item) {
        let infoHTML = `
            <div class="info-section">
                <h4>基本信息</h4>
                <div class="info-grid">
                    <div class="info-item">
                        <label>名称:</label>
                        <span>${item.name}</span>
                    </div>
                    <div class="info-item">
                        <label>类别:</label>
                        <span>${this.getCategoryIcon(item.category)} ${this.mediaLibrary.categories[item.category]}</span>
                    </div>
                    <div class="info-item">
                        <label>大小:</label>
                        <span>${this.formatFileSize(item.size)}</span>
                    </div>
                    <div class="info-item">
                        <label>创建时间:</label>
                        <span>${new Date(item.createdAt).toLocaleString('zh-CN')}</span>
                    </div>
                    ${item.dimensions ? `
                        <div class="info-item">
                            <label>尺寸:</label>
                            <span>${item.dimensions.width} × ${item.dimensions.height}</span>
                        </div>
                    ` : ''}
                    ${item.duration ? `
                        <div class="info-item">
                            <label>时长:</label>
                            <span>${this.formatDuration(item.duration)}</span>
                        </div>
                    ` : ''}
                </div>
            </div>
        `;

        // 为音频文件添加专门的语音合成信息
        if (item.type === 'audio' && item.category === 'ai_audio') {
            infoHTML += `
                <div class="info-section">
                    <h4>语音合成信息</h4>
                    <div class="info-grid">
                        ${item.metadata?.provider ? `
                            <div class="info-item">
                                <label>服务提供商:</label>
                                <span class="provider-badge">${item.metadata.provider.toUpperCase()}</span>
                            </div>
                        ` : ''}
                        ${item.metadata?.voice_name ? `
                            <div class="info-item">
                                <label>语音模型:</label>
                                <span><i class="fas fa-user"></i> ${item.metadata.voice_name}</span>
                            </div>
                        ` : ''}
                        ${item.metadata?.voice_id ? `
                            <div class="info-item">
                                <label>语音ID:</label>
                                <span class="voice-id">${item.metadata.voice_id}</span>
                            </div>
                        ` : ''}
                        ${item.metadata?.model_id ? `
                            <div class="info-item">
                                <label>模型版本:</label>
                                <span>${item.metadata.model_id}</span>
                            </div>
                        ` : ''}
                        ${item.metadata?.output_format ? `
                            <div class="info-item">
                                <label>音频格式:</label>
                                <span>${item.metadata.output_format.toUpperCase()}</span>
                            </div>
                        ` : ''}
                        ${item.metadata?.generated_at ? `
                            <div class="info-item">
                                <label>生成时间:</label>
                                <span>${new Date(item.metadata.generated_at).toLocaleString('zh-CN')}</span>
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;
        }

        // 添加通用元数据信息
        if (item.metadata?.description) {
            infoHTML += `
                <div class="info-section">
                    <h4>描述</h4>
                    <p>${item.metadata.description}</p>
                </div>
            `;
        }

        if (item.metadata?.text && item.type === 'audio') {
            infoHTML += `
                <div class="info-section">
                    <h4>原始文本</h4>
                    <div class="text-content">
                        <p>"${item.metadata.text}"</p>
                        <div class="text-stats">
                            <span>字符数: ${item.metadata.text.length}</span>
                        </div>
                    </div>
                </div>
            `;
        }

        if (item.metadata?.prompt) {
            infoHTML += `
                <div class="info-section">
                    <h4>生成提示词</h4>
                    <p class="prompt-text">${item.metadata.prompt}</p>
                </div>
            `;
        }

        if (item.metadata?.tags && item.metadata.tags.length > 0) {
            infoHTML += `
                <div class="info-section">
                    <h4>标签</h4>
                    <div class="tags">
                        ${item.metadata.tags.map(tag => `<span class="tag">${tag}</span>`).join('')}
                    </div>
                </div>
            `;
        }

        return infoHTML;
    }

    /**
     * 关闭预览
     */
    closePreview() {
        document.getElementById('media-preview-modal').style.display = 'none';
        this.currentPreviewItem = null;
    }

    /**
     * 使用选中的媒体
     */
    useSelectedMedia() {
        if (!this.currentPreviewItem) return;
        this.useMedia(this.currentPreviewItem.id);
        this.closePreview();
    }

    /**
     * 使用媒体
     */
    useMedia(id) {
        const item = this.mediaLibrary.getMediaItem(id);
        if (!item) return;

        // 根据媒体类型和当前页面决定如何使用
        const currentTab = document.querySelector('.nav-button.active')?.textContent;

        if (item.type === 'image') {
            if (currentTab === '图像生成') {
                this.setAsImageReference(item);
            } else if (currentTab === '视频生成') {
                this.setAsVideoFirstFrame(item);
            } else {
                // 切换到合适的标签页
                if (window.app && window.app.switchTab) {
                    window.app.switchTab('image-generation');
                    setTimeout(() => this.setAsImageReference(item), 100);
                }
            }
        } else if (item.type === 'video') {
            // 可以添加视频相关的使用逻辑
            console.log('Using video:', item);
        } else if (item.type === 'audio') {
            // 可以添加音频相关的使用逻辑
            console.log('Using audio:', item);
        }

        // 显示使用成功通知
        if (window.app && window.app.showNotification) {
            window.app.showNotification(`已将 "${item.name}" 应用到当前功能`, 'success');
        }
    }

    /**
     * 设置为图像参考
     */
    setAsImageReference(item) {
        if (window.app && window.app.setImageReferenceFromPath) {
            window.app.setImageReferenceFromPath(item.filePath);
        }
    }

    /**
     * 设置为视频首帧
     */
    setAsVideoFirstFrame(item) {
        if (window.app && window.app.setVideoFirstFrameFromPath) {
            window.app.setVideoFirstFrameFromPath(item.filePath);
        }
    }

    /**
     * 删除当前预览的媒体
     */
    deleteCurrentMedia() {
        if (!this.currentPreviewItem) return;
        this.deleteMedia(this.currentPreviewItem.id);
        this.closePreview();
    }

    /**
     * 删除媒体
     */
    deleteMedia(id) {
        const item = this.mediaLibrary.getMediaItem(id);
        if (!item) return;

        if (confirm(`确定要删除 "${item.name}" 吗？`)) {
            this.mediaLibrary.deleteMediaItem(id);
        }
    }

    /**
     * 显示上传对话框
     */
    showUploadDialog() {
        document.getElementById('upload-modal').style.display = 'flex';
    }

    /**
     * 关闭上传对话框
     */
    closeUploadDialog() {
        document.getElementById('upload-modal').style.display = 'none';
        document.getElementById('upload-progress').style.display = 'none';
        document.getElementById('upload-area').style.display = 'block';
    }

    /**
     * 处理文件上传
     */
    async handleFileUpload(event) {
        const files = event.target?.files || event.dataTransfer?.files;
        if (!files || files.length === 0) return;

        const uploadProgress = document.getElementById('upload-progress');
        const uploadArea = document.getElementById('upload-area');
        const progressFill = document.getElementById('progress-fill');
        const uploadStatus = document.getElementById('upload-status');

        uploadArea.style.display = 'none';
        uploadProgress.style.display = 'block';

        for (let i = 0; i < files.length; i++) {
            const file = files[i];
            const progress = ((i + 1) / files.length) * 100;

            progressFill.style.width = `${progress}%`;
            uploadStatus.textContent = `上传中... (${i + 1}/${files.length})`;

            try {
                await this.uploadFile(file);
            } catch (error) {
                console.error('Upload failed:', error);
                if (window.app && window.app.showNotification) {
                    window.app.showNotification(`上传 "${file.name}" 失败`, 'error');
                }
            }
        }

        uploadStatus.textContent = '上传完成！';
        setTimeout(() => {
            this.closeUploadDialog();
        }, 1000);
    }

    /**
     * 上传单个文件
     */
    async uploadFile(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();

            reader.onload = (e) => {
                const mediaData = {
                    name: file.name,
                    type: this.getFileType(file.type),
                    category: 'uploaded_image', // 根据文件类型调整
                    url: e.target.result,
                    size: file.size,
                    metadata: {
                        source: 'upload',
                        originalName: file.name,
                        mimeType: file.type
                    }
                };

                // 如果是图片，获取尺寸信息
                if (mediaData.type === 'image') {
                    const img = new Image();
                    img.onload = () => {
                        mediaData.dimensions = {
                            width: img.width,
                            height: img.height
                        };
                        this.mediaLibrary.addMediaItem(mediaData);
                        resolve();
                    };
                    img.src = e.target.result;
                } else {
                    this.mediaLibrary.addMediaItem(mediaData);
                    resolve();
                }
            };

            reader.onerror = () => reject(reader.error);
            reader.readAsDataURL(file);
        });
    }

    /**
     * 获取文件类型
     */
    getFileType(mimeType) {
        if (mimeType.startsWith('image/')) return 'image';
        if (mimeType.startsWith('video/')) return 'video';
        if (mimeType.startsWith('audio/')) return 'audio';
        return 'file';
    }

    /**
     * 更新统计信息
     */
    updateStats() {
        const stats = this.mediaLibrary.getStatistics();
        const countElement = document.getElementById('media-count');
        if (countElement) {
            countElement.textContent = `${stats.total} 个项目`;
        }
    }

    /**
     * 更新空状态显示
     */
    updateEmptyState(isEmpty) {
        const emptyElement = document.getElementById('media-empty');
        const gridElement = document.getElementById('media-grid');
        const listElement = document.getElementById('media-list');

        if (isEmpty) {
            emptyElement.style.display = 'flex';
            gridElement.style.display = 'none';
            listElement.style.display = 'none';
        } else {
            emptyElement.style.display = 'none';
        }
    }
}

// 创建全局实例
window.mediaLibrary = new MediaLibrary();
window.mediaLibraryUI = new MediaLibraryUI(window.mediaLibrary);
