/* DaVinci AI Co-pilot PRO 样式文件 */

/* ===== 全局样式 ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* 深色主题色彩 */
    --primary-color: #ff6b35;
    --secondary-color: #f7931e;
    --accent-color: #4ecdc4;
    --success-color: #2ecc71;
    --warning-color: #f39c12;
    --error-color: #e74c3c;

    /* 背景色 */
    --bg-primary: #1a1a1a;
    --bg-secondary: #2d2d2d;
    --bg-tertiary: #3d3d3d;
    --bg-card: #2a2a2a;

    /* 文字色 */
    --text-primary: #ffffff;
    --text-secondary: #b0b0b0;
    --text-muted: #808080;

    /* 边框色 */
    --border-color: #404040;
    --border-hover: #606060;

    /* 阴影 */
    --shadow-light: 0 2px 4px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 4px 8px rgba(0, 0, 0, 0.2);
    --shadow-heavy: 0 8px 16px rgba(0, 0, 0, 0.3);

    /* 字体 */
    --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    --font-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;

    /* 间距 */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-xxl: 3rem;

    /* 圆角 */
    --radius-sm: 4px;
    --radius-md: 8px;
    --radius-lg: 12px;
    --radius-xl: 16px;

    /* 过渡 */
    --transition-fast: 0.15s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
}

body {
    font-family: var(--font-family);
    background-color: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* ===== 容器和布局 ===== */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

#app {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

.main {
    flex: 1;
    padding: var(--spacing-lg) 0;
}

/* ===== 头部导航 ===== */
.header {
    background-color: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
    padding: var(--spacing-md) 0;
    position: sticky;
    top: 0;
    z-index: 100;
}

.header .container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--spacing-lg);
}

.logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.logo h1 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin: 0;
}

.version {
    background-color: var(--accent-color);
    color: var(--bg-primary);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 600;
}

.nav {
    display: flex;
    gap: var(--spacing-sm);
}

.nav-btn {
    background: none;
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all var(--transition-fast);
    font-size: 0.9rem;
    font-weight: 500;
}

.nav-btn:hover {
    border-color: var(--border-hover);
    color: var(--text-primary);
    background-color: var(--bg-tertiary);
}

.nav-btn.active {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.status {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 0.9rem;
}

.status-indicator {
    font-size: 1.2rem;
    color: var(--warning-color);
    animation: pulse 2s infinite;
}

.status-indicator.connected {
    color: var(--success-color);
    animation: none;
}

.status-indicator.error {
    color: var(--error-color);
    animation: none;
}

@keyframes pulse {

    0%,
    100% {
        opacity: 1;
    }

    50% {
        opacity: 0.5;
    }
}

/* ===== 标签页内容 ===== */
.tab-content {
    display: none;
    animation: fadeIn var(--transition-normal);
}

.tab-content.active {
    display: block;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ===== 仪表板 ===== */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
}

.card {
    background-color: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-light);
    transition: all var(--transition-normal);
}

.card:hover {
    border-color: var(--border-hover);
    box-shadow: var(--shadow-medium);
}

.card h3 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
    font-size: 1.1rem;
    font-weight: 600;
}

.loading {
    color: var(--text-muted);
    font-style: italic;
    text-align: center;
    padding: var(--spacing-md);
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm) 0;
    border-bottom: 1px solid var(--border-color);
}

.stat-item:last-child {
    border-bottom: none;
}

.stat-label {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.stat-value {
    color: var(--text-primary);
    font-weight: 600;
    font-family: var(--font-mono);
}

.quick-actions {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.action-btn {
    background-color: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all var(--transition-fast);
    text-align: left;
    font-size: 0.9rem;
}

.action-btn:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    transform: translateY(-1px);
    box-shadow: var(--shadow-medium);
}

/* ===== 工具容器 ===== */
.tool-container {
    max-width: 800px;
    margin: 0 auto;
}

.tool-container h2 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-sm);
    font-size: 1.8rem;
    font-weight: 700;
}

.description {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xl);
    font-size: 1.1rem;
    line-height: 1.5;
}

/* ===== 表单样式 ===== */
.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
}

.form-group label {
    display: block;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
    font-weight: 500;
    font-size: 0.95rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    background-color: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    font-size: 0.95rem;
    font-family: var(--font-family);
    transition: all var(--transition-fast);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
    font-family: var(--font-mono);
    line-height: 1.5;
}

.form-group input[type="checkbox"] {
    width: auto;
    margin-right: var(--spacing-sm);
}

.form-group input[type="number"] {
    font-family: var(--font-mono);
}

/* ===== 按钮样式 ===== */
.form-actions {
    display: flex;
    gap: var(--spacing-md);
    margin-top: var(--spacing-xl);
}

.primary-btn,
.secondary-btn {
    padding: var(--spacing-md) var(--spacing-xl);
    border-radius: var(--radius-md);
    font-size: 0.95rem;
    font-weight: 600;
    cursor: pointer;
    transition: all var(--transition-fast);
    border: none;
    position: relative;
    overflow: hidden;
}

.primary-btn {
    background-color: var(--primary-color);
    color: white;
}

.primary-btn:hover:not(:disabled) {
    background-color: #e55a2b;
    transform: translateY(-1px);
    box-shadow: var(--shadow-medium);
}

.primary-btn:disabled {
    background-color: var(--text-muted);
    cursor: not-allowed;
    transform: none;
}

.secondary-btn {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.secondary-btn:hover {
    background-color: var(--bg-secondary);
    border-color: var(--border-hover);
}

.btn-loading {
    display: none;
}

.primary-btn:disabled .btn-text {
    display: none;
}

.primary-btn:disabled .btn-loading {
    display: inline;
}

/* ===== 结果容器 ===== */
.result-container {
    margin-top: var(--spacing-xl);
    background-color: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    animation: slideIn var(--transition-normal);
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.result-container h3 {
    color: var(--accent-color);
    margin-bottom: var(--spacing-md);
    font-size: 1.2rem;
    font-weight: 600;
}

/* ===== 设置页面 ===== */
.settings-section {
    margin-bottom: var(--spacing-xxl);
    padding-bottom: var(--spacing-xl);
    border-bottom: 1px solid var(--border-color);
}

.settings-section:last-child {
    border-bottom: none;
}

.settings-section h3 {
    color: var(--secondary-color);
    margin-bottom: var(--spacing-lg);
    font-size: 1.3rem;
    font-weight: 600;
}

/* ===== 底部状态栏 ===== */
.footer {
    background-color: var(--bg-secondary);
    border-top: 1px solid var(--border-color);
    padding: var(--spacing-md) 0;
    margin-top: auto;
}

.footer .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.85rem;
    color: var(--text-secondary);
}

.footer-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.footer-links {
    display: flex;
    gap: var(--spacing-md);
}

.footer-links a {
    color: var(--text-secondary);
    text-decoration: none;
    transition: color var(--transition-fast);
}

.footer-links a:hover {
    color: var(--primary-color);
}

/* ===== 响应式设计 ===== */
@media (max-width: 768px) {
    .header .container {
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .nav {
        flex-wrap: wrap;
        justify-content: center;
    }

    .nav-btn {
        font-size: 0.8rem;
        padding: var(--spacing-xs) var(--spacing-sm);
    }

    .dashboard-grid {
        grid-template-columns: 1fr;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .form-actions {
        flex-direction: column;
    }

    .footer .container {
        flex-direction: column;
        gap: var(--spacing-sm);
        text-align: center;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 var(--spacing-sm);
    }

    .tool-container h2 {
        font-size: 1.5rem;
    }

    .card {
        padding: var(--spacing-md);
    }
}

/* ===== 模态框样式 ===== */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1050;
    display: none;
    overflow: hidden;
}

.modal.active {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.modal-backdrop.active {
    opacity: 1;
}

.modal-dialog {
    position: relative;
    max-width: 90%;
    max-height: 90%;
    margin: auto;
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.modal.active .modal-dialog {
    transform: scale(1);
}

.modal-small .modal-dialog {
    max-width: 400px;
}

.modal-medium .modal-dialog {
    max-width: 600px;
}

.modal-large .modal-dialog {
    max-width: 900px;
}

.modal-fullscreen .modal-dialog {
    max-width: 95%;
    max-height: 95%;
    width: 95%;
    height: 95%;
}

.modal-content {
    background: var(--card-bg);
    border-radius: 12px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--primary-color);
    color: white;
}

.modal-title {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
}

.modal-close {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s ease;
}

.modal-close:hover {
    background: rgba(255, 255, 255, 0.2);
}

.modal-body {
    padding: 1.5rem;
    flex: 1;
    overflow-y: auto;
}

.modal-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: flex-end;
    gap: 0.75rem;
}

/* AI服务选择模态框样式 */
.ai-service-selection {
    text-align: center;
}

.ai-service-selection p {
    margin-bottom: 1.5rem;
    color: var(--text-primary);
    font-size: 1rem;
    font-weight: 500;
}

.service-options {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.service-option-btn {
    display: flex;
    align-items: center;
    padding: 1.25rem;
    background: var(--bg-tertiary);
    border: 2px solid var(--border-color);
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: left;
    color: var(--text-primary);
}

.service-option-btn:hover {
    border-color: var(--primary-color);
    background: var(--bg-secondary);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(255, 107, 53, 0.2);
}

.service-option-btn .service-icon {
    font-size: 2.5rem;
    margin-right: 1.25rem;
    flex-shrink: 0;
}

.service-option-btn .service-content {
    flex: 1;
}

.service-option-btn .service-name {
    font-weight: 600;
    font-size: 1.2rem;
    display: block;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.service-option-btn .service-desc {
    font-size: 1rem;
    color: var(--text-secondary);
    display: block;
    line-height: 1.4;
}

.modal-actions {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-top: 1rem;
}

.modal-actions .btn {
    min-width: 100px;
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
    font-weight: 500;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: none;
}

.modal-actions .btn-secondary {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    border: 2px solid var(--border-color);
}

.modal-actions .btn-secondary:hover {
    background: var(--bg-secondary);
    border-color: var(--border-hover);
    transform: translateY(-1px);
}

/* ===== 通知样式 ===== */
.notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
    max-width: 400px;
    pointer-events: none;
}

.notification {
    background: var(--card-bg);
    border-radius: 8px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
    margin-bottom: 0.75rem;
    max-width: 400px;
    pointer-events: auto;
    display: flex;
    align-items: flex-start;
    padding: 1rem;
    transform: translateX(100%);
    transition: all 0.3s ease;
    border-left: 4px solid var(--primary-color);
}

.notification.show {
    transform: translateX(0);
    opacity: 1;
}

.notification.hide {
    transform: translateX(100%);
    opacity: 0;
}

.notification-top-right {
    top: 1rem;
    right: 1rem;
}

.notification-top-left {
    top: 1rem;
    left: 1rem;
    transform: translateX(-100%);
}

.notification-top-left.notification-enter {
    transform: translateX(0);
}

.notification-top-left.notification-exit {
    transform: translateX(-100%);
}

.notification-bottom-right {
    bottom: 1rem;
    right: 1rem;
}

.notification-bottom-left {
    bottom: 1rem;
    left: 1rem;
    transform: translateX(-100%);
}

.notification-bottom-left.notification-enter {
    transform: translateX(0);
}

.notification-bottom-left.notification-exit {
    transform: translateX(-100%);
}

.notification-success {
    border-left-color: var(--success-color);
}

.notification-error {
    border-left-color: var(--error-color);
}

.notification-warning {
    border-left-color: var(--warning-color);
}

.notification-info {
    border-left-color: var(--info-color);
}

.notification-icon {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-right: 0.75rem;
    flex-shrink: 0;
}

.notification-success .notification-icon {
    background: var(--success-color);
    color: white;
}

.notification-error .notification-icon {
    background: var(--error-color);
    color: white;
}

.notification-warning .notification-icon {
    background: var(--warning-color);
    color: white;
}

.notification-info .notification-icon {
    background: var(--info-color);
    color: white;
}

.notification-content {
    flex: 1;
}

.notification-title {
    font-weight: 600;
    margin-bottom: 0.25rem;
    color: var(--text-primary);
}

.notification-message {
    color: var(--text-primary);
    font-size: 0.95rem;
    line-height: 1.4;
}

.notification-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    font-size: 1.25rem;
    padding: 0;
    margin-left: 0.75rem;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.notification-close:hover {
    background: var(--hover-bg);
    color: var(--text-primary);
}

/* 详细通知样式 */
.notification-title {
    font-weight: 600;
    margin-bottom: 0.25rem;
    color: var(--text-primary);
}

.notification-details {
    margin-top: 0.5rem;
    font-size: 0.85rem;
    color: var(--text-secondary);
}

.notification-details>div {
    margin: 0.25rem 0;
    padding-left: 0.5rem;
    border-left: 2px solid var(--border-color);
}

/* ===== 媒体库样式 ===== */
.media-library {
    padding: 1rem;
    max-width: 1200px;
    margin: 0 auto;
    min-height: calc(100vh - 200px);
}

.media-library-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
    padding: 1rem;
    background: linear-gradient(135deg, var(--bg-secondary), var(--bg-tertiary));
    border-radius: 8px;
    border: 1px solid var(--border-color);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.media-library-title {
    flex: 1;
}

.media-library-title h2 {
    margin: 0 0 0.25rem 0;
    color: var(--text-primary);
    font-size: 1.4rem;
    font-weight: 600;
    background: linear-gradient(135deg, var(--accent-color), #4a90e2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.media-stats {
    color: var(--text-secondary);
    font-size: 0.85rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.media-stats::before {
    content: '📊';
    font-size: 0.9rem;
}

.media-library-actions {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.media-library-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding: 0.75rem;
    background: var(--bg-secondary);
    border-radius: 6px;
    border: 1px solid var(--border-color);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.search-filters {
    display: flex;
    gap: 0.5rem;
    align-items: center;
    flex: 1;
    flex-wrap: wrap;
}

.search-box {
    position: relative;
    min-width: 200px;
    flex: 1;
    max-width: 300px;
}

.search-box input {
    width: 100%;
    padding: 0.5rem 2.5rem 0.5rem 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 15px;
    background: var(--bg-primary);
    color: var(--text-primary);
    font-size: 0.85rem;
    transition: all 0.2s ease;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.search-box input:focus {
    border-color: var(--accent-color);
    box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.1);
    outline: none;
}

.search-box i {
    position: absolute;
    right: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* 筛选器下拉菜单样式 */
.search-filters select {
    padding: 0.5rem 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background: var(--bg-primary);
    color: var(--text-primary);
    font-size: 0.8rem;
    min-width: 100px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.search-filters select:focus {
    border-color: var(--accent-color);
    box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.1);
    outline: none;
}

.search-filters select:hover {
    border-color: var(--accent-hover);
}

.bulk-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 1.5rem;
    background: linear-gradient(135deg, var(--accent-color), #4a90e2);
    border-radius: 25px;
    color: white;
    box-shadow: 0 4px 12px rgba(74, 144, 226, 0.3);
    backdrop-filter: blur(10px);
    animation: slideInRight 0.3s ease;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(20px);
    }

    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.bulk-actions span {
    font-weight: 600;
    font-size: 0.95rem;
}

.bulk-actions .btn {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.bulk-actions .btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
}

/* 媒体网格视图 */
.media-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 0.75rem;
    padding: 0.5rem;
}

.media-item {
    position: relative;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.2s ease;
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.media-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
    border-color: var(--accent-color);
}

.media-thumbnail {
    position: relative;
    width: 100%;
    height: 120px;
    overflow: hidden;
    background: linear-gradient(135deg, var(--bg-tertiary), var(--bg-secondary));
    display: flex;
    align-items: center;
    justify-content: center;
}

.media-thumb {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.2s ease;
}

.media-item:hover .media-thumb {
    transform: scale(1.02);
}

.media-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.8));
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    opacity: 0;
    transition: all 0.3s ease;
    backdrop-filter: blur(4px);
}

.media-item:hover .media-overlay {
    opacity: 1;
}

.media-overlay .btn-icon {
    background: rgba(255, 255, 255, 0.9);
    color: var(--text-primary);
    width: 48px;
    height: 48px;
    border-radius: 50%;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.media-overlay .btn-icon:hover {
    background: var(--accent-color);
    color: white;
    transform: scale(1.1);
}

.media-type-badge {
    position: absolute;
    top: 0.5rem;
    left: 0.5rem;
    background: linear-gradient(135deg, var(--accent-color), #4a90e2);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
}

.media-info {
    padding: 0.75rem;
    background: var(--bg-secondary);
}

.media-name {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
    font-size: 0.8rem;
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.media-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.7rem;
    color: var(--text-secondary);
    gap: 0.25rem;
}

.media-date {
    display: flex;
    align-items: center;
    gap: 0.15rem;
}

.media-date::before {
    content: '🕒';
    font-size: 0.7rem;
}

.media-size {
    display: flex;
    align-items: center;
    gap: 0.15rem;
}

.media-size::before {
    content: '📏';
    font-size: 0.7rem;
}

.media-checkbox {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    opacity: 0;
    transition: all 0.2s ease;
    transform: scale(0.8);
}

.media-checkbox input[type="checkbox"] {
    width: 16px;
    height: 16px;
    accent-color: var(--accent-color);
    cursor: pointer;
}

.media-item:hover .media-checkbox {
    opacity: 1;
    transform: scale(1);
}

/* 视频缩略图 */
.video-thumbnail {
    position: relative;
    width: 100%;
    height: 100%;
}

.play-overlay {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 40px;
    height: 40px;
    background: rgba(0, 0, 0, 0.8);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
}

.duration-badge {
    position: absolute;
    bottom: 0.5rem;
    right: 0.5rem;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: var(--border-radius);
    font-size: 0.7rem;
}

/* 音频缩略图 */
.audio-thumbnail {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--accent-color), var(--accent-hover));
    color: white;
    font-size: 2rem;
    position: relative;
    overflow: hidden;
}

.audio-icon-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
}

.audio-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    animation: pulse 2s infinite;
}

.audio-waveform {
    display: flex;
    gap: 2px;
    align-items: flex-end;
    height: 20px;
    margin-top: 0.5rem;
}

.audio-waveform span {
    width: 3px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 2px;
    animation: wave 1.5s infinite ease-in-out;
}

.audio-waveform span:nth-child(1) {
    height: 8px;
    animation-delay: 0s;
}

.audio-waveform span:nth-child(2) {
    height: 15px;
    animation-delay: 0.1s;
}

.audio-waveform span:nth-child(3) {
    height: 20px;
    animation-delay: 0.2s;
}

.audio-waveform span:nth-child(4) {
    height: 12px;
    animation-delay: 0.3s;
}

.audio-waveform span:nth-child(5) {
    height: 6px;
    animation-delay: 0.4s;
}

@keyframes wave {

    0%,
    100% {
        transform: scaleY(0.5);
        opacity: 0.7;
    }

    50% {
        transform: scaleY(1);
        opacity: 1;
    }
}

@keyframes pulse {

    0%,
    100% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.05);
    }
}

.provider-badge {
    position: absolute;
    top: 4px;
    right: 4px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 0.6rem;
    font-weight: bold;
}

.audio-controls-mini {
    position: absolute;
    bottom: 4px;
    right: 4px;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.audio-thumbnail:hover .audio-controls-mini {
    opacity: 1;
}

.play-btn-mini {
    background: rgba(255, 255, 255, 0.9);
    color: var(--accent-color);
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.7rem;
}

.play-btn-mini:hover {
    background: white;
    transform: scale(1.1);
}

.default-thumbnail {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--bg-tertiary);
    color: var(--text-secondary);
    font-size: 2rem;
}

/* 媒体列表视图 */
.media-list {
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.media-table {
    width: 100%;
    border-collapse: collapse;
}

.media-table th,
.media-table td {
    padding: var(--spacing-sm);
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.media-table th {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    font-weight: 600;
    position: sticky;
    top: 0;
    z-index: 1;
}

.media-table tr:hover {
    background: var(--hover-bg);
}

.media-thumbnail-small {
    width: 40px;
    height: 40px;
    border-radius: var(--border-radius);
    overflow: hidden;
}

.media-thumbnail-small .media-thumb {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.media-name-cell {
    display: flex;
    flex-direction: column;
}

.media-name-cell .media-name {
    font-weight: 600;
    color: var(--text-primary);
}

.media-name-cell .media-description {
    font-size: 0.8rem;
    color: var(--text-secondary);
    margin-top: 0.25rem;
}

.category-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.25rem 0.5rem;
    background: var(--accent-color);
    color: white;
    border-radius: var(--border-radius);
    font-size: 0.8rem;
}

.action-buttons {
    display: flex;
    gap: 0.25rem;
}

.btn-icon {
    width: 32px;
    height: 32px;
    border: none;
    background: var(--bg-tertiary);
    color: var(--text-secondary);
    border-radius: var(--border-radius);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.btn-icon:hover {
    background: var(--accent-color);
    color: white;
}

.btn-icon.btn-danger:hover {
    background: var(--error-color);
}

/* 空状态 */
.media-empty {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 500px;
    padding: 3rem;
    background: linear-gradient(135deg, var(--bg-secondary), var(--bg-tertiary));
    border-radius: 16px;
    border: 2px dashed var(--border-color);
    margin: 2rem 0;
}

.empty-state {
    text-align: center;
    max-width: 500px;
    animation: fadeInUp 0.6s ease;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.empty-state i {
    font-size: 5rem;
    margin-bottom: 2rem;
    color: var(--text-secondary);
    opacity: 0.6;
    animation: float 3s ease-in-out infinite;
}

@keyframes float {

    0%,
    100% {
        transform: translateY(0px);
    }

    50% {
        transform: translateY(-10px);
    }
}

.empty-state h3 {
    margin: 0 0 1rem 0;
    color: var(--text-primary);
    font-size: 1.5rem;
    font-weight: 700;
}

.empty-state p {
    margin: 0 0 2rem 0;
    line-height: 1.6;
    color: var(--text-secondary);
    font-size: 1.1rem;
}

.empty-state .btn {
    padding: 1rem 2rem;
    font-size: 1rem;
    border-radius: 25px;
    background: linear-gradient(135deg, var(--accent-color), #4a90e2);
    color: white;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(74, 144, 226, 0.3);
}

.empty-state .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(74, 144, 226, 0.4);
}

/* 预览模态框 */
.media-preview-content {
    max-width: 90vw;
    max-height: 90vh;
    width: 1000px;
}

.preview-image {
    max-width: 100%;
    max-height: 60vh;
    object-fit: contain;
    border-radius: var(--border-radius);
}

.preview-video {
    max-width: 100%;
    max-height: 60vh;
    border-radius: var(--border-radius);
}

.audio-preview {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-lg);
    padding: var(--spacing-xl);
}

.audio-visual {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-md);
    width: 100%;
}

.audio-icon-large {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    padding: var(--spacing-lg);
    background: linear-gradient(135deg, var(--accent-color), var(--accent-hover));
    border-radius: 50%;
    color: white;
    margin-bottom: var(--spacing-md);
}

.audio-icon-large i {
    font-size: 3rem;
    margin-bottom: 0.5rem;
    animation: pulse 2s infinite;
}

.audio-waveform-large {
    display: flex;
    gap: 3px;
    align-items: flex-end;
    height: 30px;
}

.audio-waveform-large span {
    width: 4px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 2px;
    animation: wave 1.8s infinite ease-in-out;
}

.audio-waveform-large span:nth-child(1) {
    height: 10px;
    animation-delay: 0s;
}

.audio-waveform-large span:nth-child(2) {
    height: 20px;
    animation-delay: 0.1s;
}

.audio-waveform-large span:nth-child(3) {
    height: 30px;
    animation-delay: 0.2s;
}

.audio-waveform-large span:nth-child(4) {
    height: 15px;
    animation-delay: 0.3s;
}

.audio-waveform-large span:nth-child(5) {
    height: 8px;
    animation-delay: 0.4s;
}

.audio-waveform-large span:nth-child(6) {
    height: 25px;
    animation-delay: 0.5s;
}

.audio-waveform-large span:nth-child(7) {
    height: 18px;
    animation-delay: 0.6s;
}

.audio-waveform-large span:nth-child(8) {
    height: 12px;
    animation-delay: 0.7s;
}

.audio-waveform-large span:nth-child(9) {
    height: 22px;
    animation-delay: 0.8s;
}

.audio-waveform-large span:nth-child(10) {
    height: 6px;
    animation-delay: 0.9s;
}

.audio-text-content {
    background: var(--bg-secondary);
    padding: var(--spacing-md);
    border-radius: var(--border-radius);
    border-left: 4px solid var(--accent-color);
    width: 100%;
    max-width: 500px;
}

.audio-text-content h4 {
    margin: 0 0 var(--spacing-sm) 0;
    color: var(--accent-color);
    font-size: 0.9rem;
    font-weight: 600;
}

.audio-text-content p {
    margin: 0;
    font-style: italic;
    color: var(--text-secondary);
    line-height: 1.5;
}

.audio-player-container {
    width: 100%;
    max-width: 500px;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.preview-audio {
    width: 100%;
    height: 40px;
    border-radius: var(--border-radius);
}

.audio-controls-extended {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
}

.voice-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.voice-info i {
    color: var(--accent-color);
}

/* 音频通知样式 */
.audio-notification,
.error-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    animation: slideInRight 0.3s ease-out;
}

.notification-content {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: 12px 16px;
}

.notification-content button {
    background: none;
    border: none;
    color: inherit;
    font-size: 1.2rem;
    cursor: pointer;
    margin-left: auto;
    opacity: 0.8;
    transition: opacity 0.2s ease;
}

.notification-content button:hover {
    opacity: 1;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }

    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 语音ID样式 */
.voice-id {
    font-family: 'Courier New', monospace;
    background: var(--bg-tertiary);
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 0.8rem;
    color: var(--text-secondary);
}

/* 文本内容样式 */
.text-content {
    background: var(--bg-tertiary);
    padding: var(--spacing-md);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.text-stats {
    margin-top: var(--spacing-sm);
    padding-top: var(--spacing-sm);
    border-top: 1px solid var(--border-color);
    font-size: 0.8rem;
    color: var(--text-secondary);
}

.preview-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 200px;
    background: var(--bg-tertiary);
    color: var(--text-secondary);
    border-radius: var(--border-radius);
    font-size: 1.2rem;
}

/* 媒体信息 */
.media-info {
    margin-top: var(--spacing-lg);
}

.info-section {
    margin-bottom: var(--spacing-lg);
}

.info-section h4 {
    margin: 0 0 var(--spacing-sm) 0;
    color: var(--text-primary);
    font-size: 1.1rem;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-sm);
}

.info-item {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.info-item label {
    font-weight: 600;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.info-item span {
    color: var(--text-primary);
}

.prompt-text {
    background: var(--bg-tertiary);
    padding: var(--spacing-sm);
    border-radius: var(--border-radius);
    border-left: 3px solid var(--accent-color);
    font-family: monospace;
    line-height: 1.5;
}

.tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.tag {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    padding: 0.25rem 0.5rem;
    border-radius: var(--border-radius);
    font-size: 0.8rem;
    border: 1px solid var(--border-color);
}

/* 上传区域 */
.upload-area {
    border: 2px dashed var(--border-color);
    border-radius: var(--border-radius);
    padding: var(--spacing-xl);
    text-align: center;
    background: var(--bg-tertiary);
    transition: all 0.2s ease;
    cursor: pointer;
}

.upload-area:hover,
.upload-area.drag-over {
    border-color: var(--accent-color);
    background: var(--hover-bg);
}

.upload-area i {
    font-size: 3rem;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-md);
}

.upload-area p {
    margin: 0 0 var(--spacing-md) 0;
    color: var(--text-secondary);
    font-size: 1.1rem;
}

/* 上传进度 */
.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--bg-tertiary);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: var(--spacing-sm);
}

.progress-fill {
    height: 100%;
    background: var(--accent-color);
    transition: width 0.3s ease;
    width: 0%;
}

#upload-status {
    text-align: center;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .media-grid {
        grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
        gap: 0.5rem;
    }

    .search-box {
        min-width: 180px;
    }

    .media-thumbnail {
        height: 100px;
    }
}

@media (max-width: 768px) {
    .media-library {
        padding: 0.75rem;
    }

    .media-library-header {
        flex-direction: column;
        gap: 0.75rem;
        align-items: stretch;
        padding: 0.75rem;
    }

    .media-library-title h2 {
        font-size: 1.2rem;
    }

    .media-library-actions {
        justify-content: center;
    }

    .media-library-toolbar {
        flex-direction: column;
        gap: 0.5rem;
        padding: 0.5rem;
    }

    .search-filters {
        flex-direction: column;
        align-items: stretch;
        gap: 0.5rem;
    }

    .search-box {
        min-width: auto;
        max-width: none;
    }

    .search-filters select {
        width: 100%;
    }

    .media-grid {
        grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
        gap: 0.5rem;
        padding: 0.25rem;
    }

    .media-thumbnail {
        height: 90px;
    }

    .media-info {
        padding: 0.5rem;
    }

    .bulk-actions {
        padding: 0.5rem 0.75rem;
        border-radius: 15px;
        font-size: 0.8rem;
    }

    .media-table {
        font-size: 0.8rem;
    }

    .media-table th,
    .media-table td {
        padding: 0.25rem;
    }

    .media-preview-content {
        width: 95vw;
        max-height: 95vh;
        margin: 0.5rem;
    }

    .info-grid {
        grid-template-columns: 1fr;
    }

    .empty-state {
        padding: 1.5rem;
    }

    .empty-state i {
        font-size: 3rem;
    }

    .empty-state h3 {
        font-size: 1.1rem;
    }
}

/* 小屏幕优化 */
@media (max-width: 480px) {
    .media-library {
        padding: 0.5rem;
    }

    .media-library-header {
        padding: 0.5rem;
    }

    .media-library-title h2 {
        font-size: 1.1rem;
    }

    .media-library-toolbar {
        padding: 0.5rem;
    }

    .media-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        gap: 0.25rem;
    }

    .media-thumbnail {
        height: 80px;
    }

    .media-info {
        padding: 0.4rem;
    }

    .media-name {
        font-size: 0.7rem;
    }

    .media-meta {
        font-size: 0.6rem;
    }

    .media-library-actions {
        flex-direction: column;
        gap: 0.5rem;
    }

    .bulk-actions {
        flex-direction: column;
        gap: 0.5rem;
        padding: 0.5rem;
        text-align: center;
        font-size: 0.7rem;
    }

    .empty-state {
        padding: 1rem;
    }

    .empty-state i {
        font-size: 2.5rem;
        margin-bottom: 1rem;
    }

    .empty-state h3 {
        font-size: 1rem;
    }

    .empty-state p {
        font-size: 0.9rem;
    }
}

/* 保存到媒体库按钮样式 */
.save-btn {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    margin-left: 0.5rem;
}

.save-btn:hover {
    background: linear-gradient(135deg, #218838, #1ea085);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
}

.save-btn:active {
    transform: translateY(0);
}

.save-btn::before {
    content: '💾';
    font-size: 1rem;
}

/* ===== 语音试听面板样式 ===== */
.voice-preview-panel {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-md);
    margin-top: var(--spacing-sm);
    box-shadow: var(--shadow-light);
    animation: slideDown var(--transition-normal);
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--border-color);
}

.preview-header h4 {
    margin: 0;
    color: var(--primary-color);
    font-size: 1rem;
    font-weight: 600;
}

.btn-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 1.2rem;
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: 50%;
    transition: all var(--transition-fast);
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-close:hover {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
}

.preview-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.voice-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm);
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
}

.voice-info span:first-child {
    font-weight: 600;
    color: var(--text-primary);
}

.provider-badge {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.provider-badge.elevenlabs {
    background-color: var(--primary-color);
    color: white;
}

.provider-badge.minimax {
    background-color: var(--accent-color);
    color: white;
}

.preview-controls {
    display: flex;
    gap: var(--spacing-sm);
    align-items: center;
}

.preview-tips {
    color: var(--text-secondary);
    font-size: 0.85rem;
    text-align: center;
    padding: var(--spacing-xs);
    background: var(--bg-tertiary);
    border-radius: var(--radius-sm);
}

#preview-audio {
    width: 100%;
    margin-top: var(--spacing-sm);
    border-radius: var(--radius-md);
}

/* 图像和视频结果区域的动作按钮组 */
.image-item-actions,
.video-actions {
    display: flex;
    gap: 0.5rem;
    align-items: center;
    flex-wrap: wrap;
    margin-top: 0.5rem;
}

.image-item-actions .save-btn,
.video-actions .save-btn {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
}

/* 媒体库相关的按钮样式增强 */
.media-library .btn-primary {
    background: linear-gradient(135deg, var(--accent-color), #4a90e2);
    border: none;
    transition: all 0.2s ease;
}

.media-library .btn-primary:hover {
    background: linear-gradient(135deg, var(--accent-hover), #357abd);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(74, 144, 226, 0.3);
}

.media-library .btn-secondary {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    transition: all 0.2s ease;
}

.media-library .btn-secondary:hover {
    background: var(--hover-bg);
    border-color: var(--accent-color);
    transform: translateY(-1px);
}

/* ===== 进度条样式 ===== */
.progress {
    background: var(--bg-secondary);
    border-radius: 8px;
    height: 8px;
    overflow: hidden;
    position: relative;
}

.progress-bar {
    height: 100%;
    background: var(--primary-color);
    border-radius: 8px;
    transition: width 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    font-weight: 600;
    color: white;
    min-width: 0;
}

.progress-bar-success {
    background: var(--success-color);
}

.progress-bar-warning {
    background: var(--warning-color);
}

.progress-bar-error {
    background: var(--error-color);
}

.progress-striped .progress-bar {
    background-image: linear-gradient(45deg,
            rgba(255, 255, 255, 0.15) 25%,
            transparent 25%,
            transparent 50%,
            rgba(255, 255, 255, 0.15) 50%,
            rgba(255, 255, 255, 0.15) 75%,
            transparent 75%,
            transparent);
    background-size: 1rem 1rem;
}

.progress-animated .progress-bar {
    animation: progress-bar-stripes 1s linear infinite;
}

@keyframes progress-bar-stripes {
    0% {
        background-position: 1rem 0;
    }

    100% {
        background-position: 0 0;
    }
}

/* ===== 工具提示样式 ===== */
.tooltip {
    position: absolute;
    z-index: 1070;
    background: var(--tooltip-bg, rgba(0, 0, 0, 0.9));
    color: var(--tooltip-color, white);
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
    font-size: 0.875rem;
    max-width: 200px;
    word-wrap: break-word;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.2s ease;
}

.tooltip.active {
    opacity: 1;
}

.tooltip-arrow {
    position: absolute;
    width: 0;
    height: 0;
    border: 5px solid transparent;
}

.tooltip[data-placement="top"] .tooltip-arrow {
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    border-top-color: var(--tooltip-bg, rgba(0, 0, 0, 0.9));
}

.tooltip[data-placement="bottom"] .tooltip-arrow {
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
    border-bottom-color: var(--tooltip-bg, rgba(0, 0, 0, 0.9));
}

.tooltip[data-placement="left"] .tooltip-arrow {
    right: -10px;
    top: 50%;
    transform: translateY(-50%);
    border-left-color: var(--tooltip-bg, rgba(0, 0, 0, 0.9));
}

.tooltip[data-placement="right"] .tooltip-arrow {
    left: -10px;
    top: 50%;
    transform: translateY(-50%);
    border-right-color: var(--tooltip-bg, rgba(0, 0, 0, 0.9));
}

/* ===== 功能模块样式 ===== */

/* AI服务模块 */
.ai-services-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
    height: 100%;
}

.service-selector {
    background: var(--card-bg);
    border-radius: 12px;
    padding: var(--spacing-lg);
    border: 1px solid var(--border-color);
}

.service-tabs {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.service-tab {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md);
    background: none;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all var(--transition-fast);
    text-align: left;
}

.service-tab:hover {
    background: var(--hover-bg);
    color: var(--text-primary);
    border-color: var(--primary-color);
}

.service-tab.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.service-tab.available::after {
    content: '●';
    color: var(--success-color);
    margin-left: auto;
}

.service-content {
    background: var(--card-bg);
    border-radius: 12px;
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.service-panel {
    display: none;
    height: 100%;
}

.service-panel.active {
    display: flex;
    flex-direction: column;
}

.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    background: var(--bg-secondary);
}

.panel-header h3 {
    margin: 0;
    color: var(--text-primary);
}

.provider-selector select {
    min-width: 120px;
}

.panel-body {
    flex: 1;
    padding: var(--spacing-lg);
    overflow-y: auto;
}

.checkbox-group {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.checkbox-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    cursor: pointer;
}

.checkbox-item input[type="checkbox"] {
    margin: 0;
}

.result-container {
    grid-column: 1 / -1;
    background: var(--card-bg);
    border-radius: 12px;
    border: 1px solid var(--border-color);
    max-height: 400px;
    display: flex;
    flex-direction: column;
}

.result-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    background: var(--bg-secondary);
}

.result-header h3 {
    margin: 0;
}

.result-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.result-content {
    flex: 1;
    padding: var(--spacing-lg);
    overflow-y: auto;
}

.result-item {
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background: var(--bg-primary);
}

.result-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-sm);
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.result-type {
    background: var(--primary-color);
    color: white;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 0.75rem;
    text-transform: uppercase;
}

.text-result {
    white-space: pre-wrap;
    line-height: 1.6;
}

.analysis-result {
    font-size: 0.9rem;
}

.scene-item {
    margin-bottom: var(--spacing-md);
    padding: var(--spacing-sm);
    background: var(--bg-secondary);
    border-radius: 6px;
}

.scene-item h5 {
    margin: 0 0 var(--spacing-xs) 0;
    color: var(--primary-color);
}

.analysis-summary {
    margin-top: var(--spacing-md);
    padding: var(--spacing-sm);
    background: var(--info-bg);
    border-radius: 6px;
    border-left: 4px solid var(--info-color);
}

.empty-state {
    text-align: center;
    padding: var(--spacing-xl);
    color: var(--text-secondary);
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: var(--spacing-md);
    opacity: 0.5;
}

/* DaVinci集成模块 */
.davinci-integration-container {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
    height: 100%;
}

.connection-status {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    background: var(--card-bg);
    border-radius: 12px;
    border: 1px solid var(--border-color);
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: var(--error-color);
    animation: pulse 2s infinite;
}

.status-indicator.connected .status-dot {
    background: var(--success-color);
}

.status-indicator.connecting .status-dot {
    background: var(--warning-color);
}

.status-indicator.error .status-dot {
    background: var(--error-color);
}

@keyframes pulse {

    0%,
    100% {
        opacity: 1;
    }

    50% {
        opacity: 0.5;
    }
}

.connection-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
}

.section-header h3 {
    margin: 0;
    color: var(--text-primary);
}

.section-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.media-management,
.timeline-management,
.render-export,
.system-info {
    background: var(--card-bg);
    border-radius: 12px;
    padding: var(--spacing-lg);
    border: 1px solid var(--border-color);
    transition: opacity var(--transition-fast);
}

.media-management.disabled,
.timeline-management.disabled,
.render-export.disabled,
.system-info.disabled {
    opacity: 0.5;
    pointer-events: none;
}

/* 项目管理相关样式已移除 */

.timeline-info {
    margin-top: var(--spacing-sm);
    font-size: 0.875rem;
}

/* 项目列表相关样式已移除 */

.file-drop-zone {
    border: 2px dashed var(--border-color);
    border-radius: 8px;
    padding: var(--spacing-xl);
    text-align: center;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.file-drop-zone:hover,
.file-drop-zone.drag-over {
    border-color: var(--primary-color);
    background: var(--primary-bg);
}

.drop-zone-content i {
    font-size: 2rem;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-sm);
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm);
    background: var(--bg-secondary);
    border-radius: 6px;
}

.info-label {
    font-weight: 500;
    color: var(--text-secondary);
}

.info-value {
    font-weight: 600;
    color: var(--text-primary);
}

.render-job {
    margin-bottom: var(--spacing-md);
    padding: var(--spacing-md);
    background: var(--bg-secondary);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.job-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-sm);
}

.job-status {
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 0.75rem;
    text-transform: uppercase;
    font-weight: 600;
}

.job-status.rendering {
    background: var(--warning-color);
    color: white;
}

.job-status.completed {
    background: var(--success-color);
    color: white;
}

.job-status.failed {
    background: var(--error-color);
    color: white;
}

/* ===== 状态管理器样式 ===== */
.status-manager-container {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
    height: 100%;
}

.connection-indicator {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md);
    background: var(--card-bg);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.connection-indicator.connected {
    border-color: var(--success-color);
    background: var(--success-bg);
}

.connection-indicator.connecting {
    border-color: var(--warning-color);
    background: var(--warning-bg);
}

.connection-indicator.disconnected,
.connection-indicator.error {
    border-color: var(--error-color);
    background: var(--error-bg);
}

.indicator-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--error-color);
    margin-right: var(--spacing-sm);
    animation: pulse 2s infinite;
}

.connection-indicator.connected .indicator-dot {
    background: var(--success-color);
}

.connection-indicator.connecting .indicator-dot {
    background: var(--warning-color);
}

.system-overview {
    background: var(--card-bg);
    border-radius: 12px;
    padding: var(--spacing-lg);
    border: 1px solid var(--border-color);
}

.status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
}

.status-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background: var(--bg-secondary);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.status-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--primary-color);
    color: white;
    border-radius: 8px;
    font-size: 1.2rem;
}

.status-info {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.status-label {
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.status-value {
    font-weight: 600;
    color: var(--text-primary);
}

.status-value.normal {
    color: var(--success-color);
}

.status-value.warning {
    color: var(--warning-color);
}

.status-value.error {
    color: var(--error-color);
}

.active-jobs-section,
.status-history-section,
.realtime-logs-section {
    background: var(--card-bg);
    border-radius: 12px;
    padding: var(--spacing-lg);
    border: 1px solid var(--border-color);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--border-color);
}

.section-header h4 {
    margin: 0;
    color: var(--text-primary);
}

.jobs-list {
    max-height: 300px;
    overflow-y: auto;
}

.job-item {
    padding: var(--spacing-md);
    background: var(--bg-secondary);
    border-radius: 8px;
    border: 1px solid var(--border-color);
    margin-bottom: var(--spacing-sm);
    transition: all var(--transition-fast);
}

.job-item.running {
    border-left: 4px solid var(--primary-color);
}

.job-item.completed {
    border-left: 4px solid var(--success-color);
}

.job-item.error {
    border-left: 4px solid var(--error-color);
}

.job-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-sm);
}

.job-title {
    font-weight: 600;
    color: var(--text-primary);
}

.job-status {
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 0.75rem;
    text-transform: uppercase;
    font-weight: 600;
}

.job-status.running {
    background: var(--primary-color);
    color: white;
}

.job-status.completed {
    background: var(--success-color);
    color: white;
}

.job-status.error {
    background: var(--error-color);
    color: white;
}

.job-progress {
    margin-bottom: var(--spacing-sm);
}

.job-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.job-message {
    font-style: italic;
}

.job-actions {
    margin-top: var(--spacing-sm);
    display: flex;
    justify-content: flex-end;
}

.history-list {
    max-height: 200px;
    overflow-y: auto;
}

.history-item {
    display: flex;
    gap: var(--spacing-md);
    padding: var(--spacing-sm);
    border-bottom: 1px solid var(--border-color);
}

.history-item:last-child {
    border-bottom: none;
}

.history-time {
    flex-shrink: 0;
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-family: monospace;
}

.history-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.history-title {
    font-weight: 500;
    color: var(--text-primary);
}

.history-details {
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-family: monospace;
    word-break: break-all;
}

.logs-container {
    max-height: 300px;
    overflow-y: auto;
    background: var(--bg-primary);
    border-radius: 6px;
    padding: var(--spacing-sm);
    font-family: monospace;
    font-size: 0.875rem;
}

.log-entry {
    display: flex;
    gap: var(--spacing-sm);
    padding: 2px 0;
    border-bottom: 1px solid var(--border-color);
}

.log-entry:last-child {
    border-bottom: none;
}

.log-time {
    flex-shrink: 0;
    color: var(--text-secondary);
    width: 80px;
}

.log-level {
    flex-shrink: 0;
    width: 60px;
    font-weight: 600;
    text-align: center;
}

.log-level.info {
    color: var(--info-color);
}

.log-level.success {
    color: var(--success-color);
}

.log-level.warning {
    color: var(--warning-color);
}

.log-level.error {
    color: var(--error-color);
}

.log-message {
    flex: 1;
    color: var(--text-primary);
}

.toggle-switch {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    cursor: pointer;
}

.toggle-switch input[type="checkbox"] {
    display: none;
}

.toggle-slider {
    width: 40px;
    height: 20px;
    background: var(--border-color);
    border-radius: 10px;
    position: relative;
    transition: background var(--transition-fast);
}

.toggle-slider::before {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    background: white;
    border-radius: 50%;
    top: 2px;
    left: 2px;
    transition: transform var(--transition-fast);
}

.toggle-switch input[type="checkbox"]:checked+.toggle-slider {
    background: var(--primary-color);
}

.toggle-switch input[type="checkbox"]:checked+.toggle-slider::before {
    transform: translateX(20px);
}

.toggle-label {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

/* ===== 响应式设计 ===== */

/* 平板设备 (768px - 1024px) */
@media (max-width: 1024px) {
    .container {
        max-width: 95%;
        padding: 0 var(--spacing-md);
    }

    .ai-services-container {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .service-selector {
        order: 2;
    }

    .service-content {
        order: 1;
    }

    .service-tabs {
        flex-direction: row;
        flex-wrap: wrap;
        gap: var(--spacing-xs);
    }

    .service-tab {
        flex: 1;
        min-width: 120px;
        justify-content: center;
        padding: var(--spacing-sm);
    }

    /* 项目信息样式已移除 */

    .status-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .info-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* 移动设备 (最大 768px) */
@media (max-width: 768px) {
    :root {
        --spacing-xs: 0.25rem;
        --spacing-sm: 0.5rem;
        --spacing-md: 0.75rem;
        --spacing-lg: 1rem;
        --spacing-xl: 1.5rem;
    }

    .app {
        min-height: 100vh;
    }

    .header {
        padding: var(--spacing-md);
    }

    .header-content {
        flex-direction: column;
        gap: var(--spacing-md);
        text-align: center;
    }

    .header-title {
        font-size: 1.5rem;
    }

    .header-actions {
        width: 100%;
        justify-content: center;
    }

    .nav {
        flex-direction: column;
        gap: var(--spacing-xs);
        padding: var(--spacing-md);
    }

    .nav-btn {
        width: 100%;
        justify-content: center;
        padding: var(--spacing-md);
        font-size: 0.9rem;
    }

    .main {
        padding: var(--spacing-md);
    }

    .container {
        max-width: 100%;
        padding: 0;
    }

    .card {
        margin-bottom: var(--spacing-md);
        padding: var(--spacing-md);
    }

    .card-header {
        padding: var(--spacing-md);
        flex-direction: column;
        gap: var(--spacing-sm);
        text-align: center;
    }

    .card-body {
        padding: var(--spacing-md);
    }

    .form-row {
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .form-group {
        margin-bottom: var(--spacing-md);
    }

    .form-actions {
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .btn-group {
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .btn-group .btn {
        width: 100%;
        justify-content: center;
    }

    /* AI服务模块移动端适配 */
    .ai-services-container {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .service-selector {
        order: 1;
        padding: var(--spacing-md);
    }

    .service-tabs {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-xs);
    }

    .service-tab {
        padding: var(--spacing-sm);
        font-size: 0.875rem;
        flex-direction: column;
        gap: var(--spacing-xs);
        text-align: center;
    }

    .service-content {
        order: 2;
    }

    .panel-header {
        flex-direction: column;
        gap: var(--spacing-sm);
        text-align: center;
    }

    .provider-selector {
        width: 100%;
    }

    .provider-selector select {
        width: 100%;
    }

    .checkbox-group {
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .result-container {
        order: 3;
        max-height: 300px;
    }

    /* DaVinci集成模块移动端适配 */
    .connection-status {
        flex-direction: column;
        gap: var(--spacing-md);
        text-align: center;
    }

    .connection-actions {
        width: 100%;
        justify-content: center;
    }

    .section-header {
        flex-direction: column;
        gap: var(--spacing-sm);
        text-align: center;
    }

    .section-actions {
        width: 100%;
        justify-content: center;
    }

    /* 项目相关响应式样式已移除 */

    /* AI服务选择对话框样式 */
    .ai-service-selection {
        padding: var(--spacing-md);
    }

    .ai-service-selection p {
        margin-bottom: var(--spacing-lg);
        text-align: center;
        color: var(--text-primary);
    }

    .service-options {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .service-option-btn {
        display: flex;
        align-items: center;
        padding: var(--spacing-lg);
        background: var(--card-bg);
        border: 2px solid var(--border-color);
        border-radius: var(--border-radius);
        cursor: pointer;
        transition: all var(--transition-fast);
        text-align: left;
    }

    .service-option-btn:hover {
        border-color: var(--primary-color);
        background: var(--bg-secondary);
        transform: translateY(-2px);
    }

    .service-icon {
        font-size: 2rem;
        margin-right: var(--spacing-lg);
    }

    .service-name {
        font-weight: 600;
        font-size: 1.1rem;
        color: var(--text-primary);
        display: block;
        margin-bottom: var(--spacing-xs);
    }

    .service-desc {
        font-size: 0.9rem;
        color: var(--text-secondary);
        display: block;
    }

    /* 详细通知样式 */

    .detailed-notification {
        background: var(--card-bg);
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        margin-bottom: var(--spacing-sm);
        overflow: hidden;
        animation: slideInRight 0.3s ease-out;
    }

    .detailed-notification.success {
        border-left: 4px solid var(--success-color);
    }

    .detailed-notification.error {
        border-left: 4px solid var(--error-color);
    }

    .detailed-notification.warning {
        border-left: 4px solid var(--warning-color);
    }

    .detailed-notification.info {
        border-left: 4px solid var(--primary-color);
    }

    .notification-header {
        display: flex;
        align-items: center;
        padding: var(--spacing-md);
        background: var(--bg-secondary);
        border-bottom: 1px solid var(--border-color);
    }

    .notification-icon {
        margin-right: var(--spacing-sm);
        font-size: 1.2rem;
    }

    .notification-title {
        flex: 1;
        font-weight: 600;
        color: var(--text-primary);
    }

    .notification-close {
        background: none;
        border: none;
        font-size: 1.5rem;
        cursor: pointer;
        color: var(--text-secondary);
        padding: 0;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .notification-close:hover {
        color: var(--text-primary);
    }

    .notification-body {
        padding: var(--spacing-md);
    }

    .notification-message {
        margin: 0 0 var(--spacing-sm) 0;
        color: var(--text-primary);
    }

    .notification-details {
        background: var(--bg-primary);
        border-radius: 4px;
        padding: var(--spacing-sm);
    }

    .detail-item {
        font-size: 0.875rem;
        color: var(--text-secondary);
        margin-bottom: var(--spacing-xs);
        font-family: monospace;
    }

    .detail-item:last-child {
        margin-bottom: 0;
    }

    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }

        to {
            transform: translateX(0);
            opacity: 1;
        }
    }

    .file-drop-zone {
        padding: var(--spacing-lg);
    }

    .drop-zone-content {
        font-size: 0.9rem;
    }

    .drop-zone-content i {
        font-size: 1.5rem;
    }

    /* 状态管理器移动端适配 */
    .status-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
    }

    .status-item {
        padding: var(--spacing-sm);
    }

    .status-icon {
        width: 32px;
        height: 32px;
        font-size: 1rem;
    }

    .job-header {
        flex-direction: column;
        gap: var(--spacing-xs);
        text-align: center;
    }

    .job-meta {
        flex-direction: column;
        gap: var(--spacing-xs);
        text-align: center;
    }

    .history-item {
        flex-direction: column;
        gap: var(--spacing-xs);
    }

    .history-time {
        width: auto;
        text-align: center;
    }

    .log-entry {
        flex-direction: column;
        gap: var(--spacing-xs);
        padding: var(--spacing-xs) 0;
    }

    .log-time,
    .log-level {
        width: auto;
        text-align: center;
    }

    /* 模态框移动端适配 */
    .modal-dialog {
        max-width: 95%;
        margin: var(--spacing-md);
    }

    .modal-header,
    .modal-body,
    .modal-footer {
        padding: var(--spacing-md);
    }

    .modal-footer {
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .modal-footer .btn {
        width: 100%;
    }

    /* 通知移动端适配 */
    .notification {
        max-width: calc(100vw - 2rem);
        margin: 0 var(--spacing-md) var(--spacing-sm) var(--spacing-md);
    }

    .notification-content {
        font-size: 0.875rem;
    }

    /* 工具提示移动端适配 */
    .tooltip {
        max-width: calc(100vw - 2rem);
        font-size: 0.8rem;
        padding: var(--spacing-xs) var(--spacing-sm);
    }

    /* 表单元素移动端适配 */
    .form-input,
    .form-textarea,
    .form-select {
        font-size: 16px;
        /* 防止iOS缩放 */
        padding: var(--spacing-md);
    }

    .form-textarea {
        min-height: 100px;
    }

    .form-range {
        height: 8px;
    }

    /* 按钮移动端适配 */
    .btn {
        padding: var(--spacing-md) var(--spacing-lg);
        font-size: 0.9rem;
        min-height: 44px;
        /* iOS推荐的最小触摸目标 */
    }

    .btn-sm {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: 0.8rem;
        min-height: 36px;
    }

    /* 进度条移动端适配 */
    .progress {
        height: 12px;
    }

    .progress-bar {
        font-size: 0.7rem;
    }

    /* 标签页移动端适配 */
    .tabs-demo .tab-list {
        flex-wrap: wrap;
        gap: var(--spacing-xs);
    }

    .tabs-demo .tab {
        flex: 1;
        min-width: 80px;
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: 0.875rem;
    }
}

/* 小屏幕移动设备 (最大 480px) */
@media (max-width: 480px) {
    .header-title {
        font-size: 1.25rem;
        line-height: 1.3;
    }

    .service-tabs {
        grid-template-columns: 1fr;
    }

    .service-tab {
        padding: var(--spacing-md);
    }

    .status-item {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-sm);
    }

    .connection-indicator {
        padding: var(--spacing-sm);
    }

    .card-header h3 {
        font-size: 1.1rem;
    }

    .section-header h4 {
        font-size: 1rem;
    }

    .btn-group {
        gap: var(--spacing-xs);
    }

    .form-actions .btn {
        padding: var(--spacing-sm) var(--spacing-md);
    }
}

/* 横屏模式适配 */
@media (max-height: 500px) and (orientation: landscape) {
    .header {
        padding: var(--spacing-sm) var(--spacing-md);
    }

    .header-title {
        font-size: 1.25rem;
    }

    .nav {
        flex-direction: row;
        flex-wrap: wrap;
        padding: var(--spacing-sm);
    }

    .nav-btn {
        flex: 1;
        min-width: 120px;
        padding: var(--spacing-sm);
        font-size: 0.8rem;
    }

    .main {
        padding: var(--spacing-sm);
    }

    .modal-dialog {
        max-height: 90vh;
    }

    .modal-body {
        max-height: 60vh;
        overflow-y: auto;
    }
}

/* 高分辨率屏幕适配 */
@media (-webkit-min-device-pixel-ratio: 2),
(min-resolution: 192dpi) {

    .status-dot,
    .indicator-dot {
        border: 0.5px solid rgba(255, 255, 255, 0.2);
    }

    .progress-bar {
        background-image: linear-gradient(45deg,
                rgba(255, 255, 255, 0.1) 25%,
                transparent 25%,
                transparent 50%,
                rgba(255, 255, 255, 0.1) 50%,
                rgba(255, 255, 255, 0.1) 75%,
                transparent 75%,
                transparent);
        background-size: 8px 8px;
    }
}

/* 打印样式 */
@media print {

    .header,
    .nav,
    .modal,
    .notification,
    .tooltip {
        display: none !important;
    }

    .main {
        padding: 0;
    }

    .card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #ccc;
    }

    .btn {
        display: none;
    }

    .form-input,
    .form-textarea,
    .form-select {
        border: 1px solid #ccc;
        background: white;
    }
}

/* 深色模式媒体查询 */
@media (prefers-color-scheme: dark) {
    :root {
        --bg-primary: #1a1a1a;
        --bg-secondary: #2d2d2d;
        --card-bg: #2d2d2d;
        --text-primary: #ffffff;
        --text-secondary: #b3b3b3;
        --border-color: #404040;
        --hover-bg: #404040;
    }
}

/* 减少动画偏好 */
@media (prefers-reduced-motion: reduce) {

    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .progress-animated .progress-bar {
        animation: none;
    }

    .status-dot,
    .indicator-dot {
        animation: none;
    }
}

/* ===== 语音参数控件样式 ===== */
.form-section {
    margin: var(--spacing-lg) 0;
    padding: var(--spacing-lg);
    background: var(--bg-tertiary);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.form-section h3 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    font-size: 1.1rem;
    font-weight: 600;
}

/* 滑块控件样式 */
input[type="range"] {
    width: 100%;
    height: 6px;
    border-radius: 3px;
    background: var(--bg-secondary);
    outline: none;
    margin: var(--spacing-sm) 0;
    -webkit-appearance: none;
}

input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: var(--primary-color);
    cursor: pointer;
    border: 2px solid var(--bg-primary);
    box-shadow: var(--shadow-light);
}

input[type="range"]::-moz-range-thumb {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: var(--primary-color);
    cursor: pointer;
    border: 2px solid var(--bg-primary);
    box-shadow: var(--shadow-light);
}

input[type="range"]:hover::-webkit-slider-thumb {
    background: var(--secondary-color);
    transform: scale(1.1);
    transition: all 0.2s ease;
}

input[type="range"]:hover::-moz-range-thumb {
    background: var(--secondary-color);
    transform: scale(1.1);
    transition: all 0.2s ease;
}

/* 滑块标签样式 */
.range-labels {
    display: flex;
    justify-content: space-between;
    font-size: 0.8rem;
    color: var(--text-muted);
    margin-top: var(--spacing-xs);
}

/* 复选框样式 */
input[type="checkbox"] {
    width: 16px;
    height: 16px;
    margin-right: var(--spacing-sm);
    accent-color: var(--primary-color);
}

/* 帮助文本样式 */
.help-text {
    display: block;
    font-size: 0.8rem;
    color: var(--text-muted);
    margin-top: var(--spacing-xs);
    line-height: 1.4;
}

/* 标签值显示样式 */
label span {
    color: var(--accent-color);
    font-weight: 600;
    margin-left: var(--spacing-xs);
}

/* ===== 图片上传和预览样式 ===== */
input[type="file"] {
    width: 100%;
    padding: var(--spacing-sm);
    border: 2px dashed var(--border-color);
    border-radius: 8px;
    background: var(--bg-secondary);
    color: var(--text-primary);
    cursor: pointer;
    transition: all 0.3s ease;
}

input[type="file"]:hover {
    border-color: var(--primary-color);
    background: var(--bg-tertiary);
}

input[type="file"]:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 2px rgba(78, 205, 196, 0.2);
}

.reference-preview {
    margin-top: var(--spacing-md);
    padding: var(--spacing-md);
    background: var(--bg-secondary);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.reference-preview h4 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
    font-size: 0.9rem;
    font-weight: 600;
}

.preview-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
}

.preview-item {
    position: relative;
    aspect-ratio: 1;
    border-radius: 6px;
    overflow: hidden;
    border: 2px solid var(--border-color);
    transition: all 0.3s ease;
}

.preview-item:hover {
    border-color: var(--primary-color);
    transform: scale(1.05);
}

.preview-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.preview-item .remove-btn {
    position: absolute;
    top: 4px;
    right: 4px;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--error-color);
    color: white;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.preview-item:hover .remove-btn {
    opacity: 1;
}

.preview-item .remove-btn:hover {
    background: #c0392b;
    transform: scale(1.1);
}

/* 单张图片预览样式 */
.preview-single {
    display: flex;
    justify-content: center;
    margin-bottom: var(--spacing-md);
}

.preview-single .preview-item {
    max-width: 200px;
    width: 100%;
}

/* ===== 异步任务取消按钮样式 ===== */
.cancel-btn {
    margin-left: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--error-color);
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.cancel-btn:hover {
    background: #c0392b;
    transform: translateY(-1px);
    box-shadow: var(--shadow-light);
}

.cancel-btn:active {
    transform: translateY(0);
    box-shadow: none;
}

/* ===== 结果显示优化样式 ===== */
.success-message {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md);
    background: var(--success-bg);
    border: 1px solid var(--success-color);
    border-radius: 6px;
    margin-bottom: var(--spacing-md);
    color: var(--success-color);
    font-weight: 500;
}

.success-icon {
    font-size: 1.2rem;
}

.error {
    padding: var(--spacing-md);
    background: var(--error-bg);
    border: 1px solid var(--error-color);
    border-radius: 6px;
    color: var(--error-color);
}

.error h4 {
    margin: 0 0 var(--spacing-sm) 0;
    color: var(--error-color);
    font-size: 1rem;
    font-weight: 600;
}

.error p {
    margin: var(--spacing-sm) 0;
    line-height: 1.4;
}

.error-help {
    margin-top: var(--spacing-md);
    padding-top: var(--spacing-sm);
    border-top: 1px solid rgba(220, 53, 69, 0.2);
}

.error-help p {
    font-weight: 500;
    margin-bottom: var(--spacing-xs);
}

.error-help ul {
    margin: var(--spacing-xs) 0;
    padding-left: var(--spacing-md);
}

.error-help li {
    margin-bottom: var(--spacing-xs);
    line-height: 1.3;
}

.error-icon {
    font-size: 1.2rem;
    margin-right: var(--spacing-sm);
}

.audio-url,
.image-meta {
    margin-top: var(--spacing-md);
    padding: var(--spacing-sm);
    background: var(--bg-secondary);
    border-radius: 4px;
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.image-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-md);
    margin: var(--spacing-md) 0;
}

.image-item {
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: var(--spacing-sm);
    background: var(--bg-secondary);
}

.image-item-actions {
    display: flex;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-sm);
}

.copy-btn,
.download-all-btn {
    padding: var(--spacing-xs) var(--spacing-sm);
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.85rem;
    transition: background-color 0.3s ease;
}

.copy-btn:hover,
.download-all-btn:hover {
    background: var(--primary-hover);
}

.batch-actions {
    margin-top: var(--spacing-md);
    text-align: center;
}

details {
    margin-top: var(--spacing-sm);
}

details summary {
    cursor: pointer;
    padding: var(--spacing-xs);
    background: var(--bg-secondary);
    border-radius: 4px;
    font-size: 0.9rem;
}

details pre {
    background: var(--bg-primary);
    padding: var(--spacing-sm);
    border-radius: 4px;
    font-size: 0.8rem;
    overflow-x: auto;
    margin-top: var(--spacing-xs);
}

/* ===== 提示词优化功能样式 ===== */
.prompt-input-container {
    position: relative;
    display: flex;
    align-items: flex-start;
    gap: 10px;
}

.prompt-input-container textarea {
    flex: 1;
}

/* 确保容器内的翻译按钮与优化按钮样式一致 */
.prompt-input-container .translate-btn {
    background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%) !important;
    color: white !important;
    border: none !important;
    border-radius: 8px !important;
    padding: 12px 16px !important;
    cursor: pointer !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    display: flex !important;
    align-items: center !important;
    gap: 6px !important;
    transition: all 0.3s ease !important;
    white-space: nowrap !important;
    min-height: 40px !important;
    box-shadow: 0 2px 4px rgba(78, 205, 196, 0.2) !important;
}

.enhance-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 12px 16px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 6px;
    transition: all 0.3s ease;
    white-space: nowrap;
    min-height: 40px;
    box-shadow: 0 2px 4px rgba(102, 126, 234, 0.2);
}

.enhance-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(102, 126, 234, 0.3);
}

.enhance-btn:active {
    transform: translateY(0);
}

.enhance-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* 翻译按钮样式 */
.translate-btn {
    background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%) !important;
    color: white !important;
    border: none !important;
    border-radius: 8px !important;
    padding: 12px 16px !important;
    cursor: pointer !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    display: flex !important;
    align-items: center !important;
    gap: 6px !important;
    transition: all 0.3s ease !important;
    white-space: nowrap !important;
    min-height: 40px !important;
    box-shadow: 0 2px 4px rgba(78, 205, 196, 0.2) !important;
}

.translate-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(78, 205, 196, 0.3);
}

.translate-btn:active {
    transform: translateY(0);
}

.translate-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* 翻译按钮加载状态 */
.translate-btn.loading {
    background: var(--text-muted);
    cursor: not-allowed;
}

.translate-btn.loading .translate-text::after {
    content: '...';
    animation: dots 1.5s infinite;
}

/* 文本框容器样式 */
.textarea-container {
    position: relative;
    display: flex;
    align-items: flex-start;
    gap: 8px;
}

.textarea-container textarea {
    flex: 1;
}

/* 确保textarea-container内的翻译按钮样式一致 */
.textarea-container .translate-btn {
    background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%) !important;
    color: white !important;
    border: none !important;
    border-radius: 8px !important;
    padding: 12px 16px !important;
    cursor: pointer !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    display: flex !important;
    align-items: center !important;
    gap: 6px !important;
    transition: all 0.3s ease !important;
    white-space: nowrap !important;
    min-height: 40px !important;
    box-shadow: 0 2px 4px rgba(78, 205, 196, 0.2) !important;
}

.enhance-icon {
    font-size: 14px;
}

.enhance-text {
    font-size: 11px;
}

.prompt-preview {
    margin-top: 15px;
    padding: 15px;
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    animation: slideDown 0.3s ease;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    flex-wrap: wrap;
    gap: 10px;
}

.preview-title {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 14px;
}

.preview-actions {
    display: flex;
    gap: 8px;
}

.apply-btn {
    background: var(--success-color);
    color: white;
    border: none;
    border-radius: 6px;
    padding: 6px 12px;
    font-size: 12px;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.apply-btn:hover {
    background: #27ae60;
}

.cancel-btn {
    background: var(--text-muted);
    color: white;
    border: none;
    border-radius: 6px;
    padding: 6px 12px;
    font-size: 12px;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.cancel-btn:hover {
    background: #6c757d;
}

.enhanced-prompt {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 12px;
    font-size: 14px;
    line-height: 1.5;
    color: var(--text-primary);
    max-height: 200px;
    overflow-y: auto;
    white-space: pre-wrap;
}

/* 加载状态 */
.enhance-btn.loading {
    background: var(--text-muted);
    cursor: not-allowed;
}

.enhance-btn.loading .enhance-text::after {
    content: '...';
    animation: dots 1.5s infinite;
}

@keyframes dots {

    0%,
    20% {
        content: '';
    }

    40% {
        content: '.';
    }

    60% {
        content: '..';
    }

    80%,
    100% {
        content: '...';
    }
}

/* ===== AI深度集成样式 ===== */
.ai-integration-tabs {
    display: flex;
    gap: var(--spacing-xs);
    margin-bottom: var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
}

.ai-tab-btn {
    padding: var(--spacing-sm) var(--spacing-md);
    background: transparent;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.ai-tab-btn:hover {
    color: var(--text-primary);
    background: var(--bg-secondary);
}

.ai-tab-btn.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
    background: var(--bg-secondary);
}

.ai-tab-content {
    display: none;
    padding: var(--spacing-md) 0;
}

.ai-tab-content.active {
    display: block;
}

/* ===== 信息显示样式 ===== */
.info-display {
    margin-top: var(--spacing-md);
    padding: var(--spacing-md);
    background: var(--bg-secondary);
    border-radius: 6px;
    border: 1px solid var(--border-color);
}

.info-display h4 {
    margin: 0 0 var(--spacing-md) 0;
    color: var(--text-primary);
    font-size: 1rem;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm);
    background: var(--bg-primary);
    border-radius: 4px;
    border: 1px solid var(--border-color);
}

.info-label {
    font-weight: 500;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.info-value {
    font-weight: 600;
    color: var(--text-primary);
    font-family: 'Courier New', monospace;
}

/* ===== 静帧捕获特殊样式 ===== */
#frame-capture-status {
    margin-bottom: var(--spacing-md);
    padding: var(--spacing-md);
    background: var(--bg-secondary);
    border-radius: 6px;
    border: 1px solid var(--border-color);
}

#frame-capture-status .loading {
    text-align: center;
    color: var(--text-secondary);
}

#frame-capture-status.ready {
    border-color: var(--success-color);
    background: rgba(34, 197, 94, 0.1);
}

#frame-capture-status.error {
    border-color: var(--error-color);
    background: var(--error-bg);
}

/* ===== 渲染状态样式 ===== */
#render-status {
    margin-bottom: var(--spacing-md);
    padding: var(--spacing-md);
    background: var(--bg-secondary);
    border-radius: 6px;
    border: 1px solid var(--border-color);
}

#render-status.rendering {
    border-color: var(--warning-color);
    background: rgba(245, 158, 11, 0.1);
}

#render-status.completed {
    border-color: var(--success-color);
    background: rgba(34, 197, 94, 0.1);
}

#render-status.error {
    border-color: var(--error-color);
    background: var(--error-bg);
}

/* ===== 滑块值显示样式 ===== */
#confidence-value,
#marker-confidence-value {
    font-weight: 600;
    color: var(--primary-color);
    min-width: 2em;
    text-align: center;
}