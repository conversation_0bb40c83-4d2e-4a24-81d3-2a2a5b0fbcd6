/* DaVinci Resolve静帧联动功能样式 */

.frame-capture-panel,
.frame-capture-section {
    background: #2a2a2a;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
    border: 1px solid #444;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.frame-capture-section.disabled {
    opacity: 0.6;
    pointer-events: none;
}

.frame-capture-panel .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #444;
}

.frame-capture-panel h3 {
    color: #fff;
    margin: 0;
    font-size: 18px;
    font-weight: 600;
}

.connection-status {
    display: flex;
    align-items: center;
    gap: 8px;
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
}

.status-indicator.online {
    background: #4CAF50;
    box-shadow: 0 0 8px rgba(76, 175, 80, 0.6);
}

.status-indicator.offline {
    background: #f44336;
    box-shadow: 0 0 8px rgba(244, 67, 54, 0.6);
}

.status-text {
    color: #ccc;
    font-size: 14px;
}

/* 播放头信息 */
.playhead-info {
    background: #333;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 20px;
}

.info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.info-row:last-child {
    margin-bottom: 0;
}

.info-row label {
    color: #aaa;
    font-size: 14px;
    font-weight: 500;
}

.info-row span {
    color: #fff;
    font-size: 14px;
    font-family: 'Courier New', monospace;
    background: #444;
    padding: 4px 8px;
    border-radius: 4px;
}

/* 操作按钮 */
.frame-actions {
    margin-bottom: 20px;
}

.frame-actions .btn {
    width: 100%;
    margin-bottom: 10px;
    padding: 12px;
    font-size: 14px;
    font-weight: 500;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.btn-primary:disabled {
    background: #555;
    color: #999;
    cursor: not-allowed;
}

/* AI集成部分 */
.ai-integration {
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid #444;
}

.ai-integration h4 {
    color: #fff;
    margin: 0 0 15px 0;
    font-size: 16px;
    font-weight: 500;
}

.ai-service-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
}

.btn-ai {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
    padding: 10px;
    font-size: 13px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
}

.btn-ai:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(240, 147, 251, 0.4);
}

.btn-ai:disabled {
    background: #555;
    color: #999;
    cursor: not-allowed;
}

/* 导出设置 */
.export-settings {
    background: #333;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 20px;
}

.export-settings h4 {
    color: #fff;
    margin: 0 0 15px 0;
    font-size: 16px;
    font-weight: 500;
}

.setting-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.setting-row:last-child {
    margin-bottom: 0;
}

.setting-row label {
    color: #aaa;
    font-size: 14px;
}

.setting-row select {
    background: #444;
    color: #fff;
    border: 1px solid #555;
    border-radius: 4px;
    padding: 6px 10px;
    font-size: 14px;
    min-width: 100px;
}

.setting-row select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

/* 导出进度 */
.export-progress {
    background: #333;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 20px;
    text-align: center;
}

.progress-bar {
    width: 100%;
    height: 6px;
    background: #444;
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    width: 100%;
    animation: progressAnimation 1.5s ease-in-out infinite;
}

@keyframes progressAnimation {
    0% {
        transform: translateX(-100%);
    }

    50% {
        transform: translateX(0%);
    }

    100% {
        transform: translateX(100%);
    }
}

.progress-text {
    color: #ccc;
    font-size: 14px;
}

/* 最近捕获 */
.recent-captures {
    background: #333;
    border-radius: 6px;
    padding: 15px;
}

.recent-captures h4 {
    color: #fff;
    margin: 0 0 15px 0;
    font-size: 16px;
    font-weight: 500;
}

.captures-list {
    max-height: 200px;
    overflow-y: auto;
}

.capture-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background: #444;
    border-radius: 4px;
    margin-bottom: 8px;
    transition: background 0.2s ease;
}

.capture-item:hover {
    background: #4a4a4a;
}

.capture-item:last-child {
    margin-bottom: 0;
}

.capture-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.capture-frame {
    color: #fff;
    font-size: 14px;
    font-weight: 500;
}

.capture-time {
    color: #aaa;
    font-size: 12px;
    font-family: 'Courier New', monospace;
}

.capture-type {
    color: #667eea;
    font-size: 12px;
    background: rgba(102, 126, 234, 0.2);
    padding: 2px 6px;
    border-radius: 3px;
    display: inline-block;
}

.capture-actions {
    display: flex;
    gap: 6px;
}

.btn-small {
    background: #555;
    color: #ccc;
    border: none;
    border-radius: 4px;
    padding: 6px 8px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s ease;
}

.btn-small:hover {
    background: #667eea;
    color: white;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .ai-service-buttons {
        grid-template-columns: 1fr;
    }

    .setting-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .setting-row select {
        width: 100%;
    }
}

/* 滚动条样式 */
.captures-list::-webkit-scrollbar {
    width: 6px;
}

.captures-list::-webkit-scrollbar-track {
    background: #444;
    border-radius: 3px;
}

.captures-list::-webkit-scrollbar-thumb {
    background: #666;
    border-radius: 3px;
}

.captures-list::-webkit-scrollbar-thumb:hover {
    background: #777;
}

/* 深度集成功能样式 */
.deep-integration-section {
    background: #2a2a2a;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
    border: 1px solid #444;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.deep-integration-section.disabled {
    opacity: 0.6;
    pointer-events: none;
}

.integration-features {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
}

.feature-card {
    background: #333;
    border-radius: 6px;
    padding: 15px;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.feature-card h4 {
    color: #fff;
    margin: 0;
    font-size: 16px;
}

.feature-card p {
    color: #aaa;
    margin: 0;
    font-size: 14px;
}

.btn-feature {
    background: #444;
    color: #fff;
    border: none;
    border-radius: 4px;
    padding: 8px 12px;
    margin-top: auto;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-feature:hover {
    background: #555;
}

/* 模态框样式 */
.frame-preview-modal,
.analysis-results-modal,
.subtitle-results-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.frame-preview-modal .modal-content,
.analysis-results-modal .modal-content,
.subtitle-results-modal .modal-content {
    background: #2a2a2a;
    border-radius: 8px;
    width: 80%;
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #444;
}

.modal-header h3 {
    margin: 0;
    color: #fff;
}

.close-btn {
    background: none;
    border: none;
    color: #aaa;
    font-size: 24px;
    cursor: pointer;
}

.close-btn:hover {
    color: #fff;
}

.modal-body {
    padding: 20px;
}

/* 分析结果样式 */
.analysis-summary,
.subtitle-info {
    background: #333;
    padding: 15px;
    border-radius: 6px;
    margin-bottom: 20px;
}

.suggested-markers,
.subtitle-list {
    background: #333;
    padding: 15px;
    border-radius: 6px;
}

.marker-item,
.subtitle-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px;
    border-bottom: 1px solid #444;
}

.marker-item:last-child,
.subtitle-item:last-child {
    border-bottom: none;
}

.marker-type,
.subtitle-index {
    background: #555;
    color: #fff;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
}

.marker-frame,
.subtitle-time {
    color: #aaa;
    font-size: 12px;
    font-family: 'Courier New', monospace;
}

.marker-confidence {
    color: #4CAF50;
    font-size: 12px;
}

.marker-description,
.subtitle-text {
    color: #fff;
    flex-grow: 1;
}