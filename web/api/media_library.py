"""
DaVinci AI Co-pilot PRO - 媒体库API接口

提供媒体资源的管理、存储和检索功能
"""

import os
import json
import shutil
import mimetypes
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any
from fastapi import APIRouter, HTTPException, UploadFile, File, Form
from fastapi.responses import FileResponse
from pydantic import BaseModel

# 创建路由器
router = APIRouter(prefix="/api/media-library", tags=["media-library"])

# 媒体库配置
MEDIA_LIBRARY_DIR = Path("output/media_library")
THUMBNAILS_DIR = MEDIA_LIBRARY_DIR / "thumbnails"
METADATA_FILE = MEDIA_LIBRARY_DIR / "metadata.json"

# 确保目录存在
MEDIA_LIBRARY_DIR.mkdir(parents=True, exist_ok=True)
THUMBNAILS_DIR.mkdir(parents=True, exist_ok=True)

class MediaItem(BaseModel):
    """媒体项目数据模型"""
    id: str
    name: str
    type: str  # 'image', 'video', 'audio'
    category: str  # 'ai_image', 'ai_video', 'davinci_frame', 'uploaded_image', 'ai_audio'
    file_path: str
    url: str
    thumbnail_url: Optional[str] = None
    size: int
    duration: Optional[float] = None
    dimensions: Optional[Dict[str, int]] = None
    metadata: Dict[str, Any]
    created_at: str
    updated_at: str

class MediaLibraryManager:
    """媒体库管理器"""

    def __init__(self):
        self.metadata_cache = {}
        self.load_metadata()

    def load_metadata(self):
        """加载元数据"""
        try:
            if METADATA_FILE.exists():
                with open(METADATA_FILE, 'r', encoding='utf-8') as f:
                    self.metadata_cache = json.load(f)
        except Exception as e:
            print(f"Failed to load metadata: {e}")
            self.metadata_cache = {}

    def save_metadata(self):
        """保存元数据"""
        try:
            with open(METADATA_FILE, 'w', encoding='utf-8') as f:
                json.dump(self.metadata_cache, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"Failed to save metadata: {e}")

    def add_media_item(self, media_data: Dict[str, Any]) -> MediaItem:
        """添加媒体项目"""
        media_id = media_data.get('id') or self.generate_id()

        # 创建媒体项目
        media_item = MediaItem(
            id=media_id,
            name=media_data.get('name', f'媒体文件_{datetime.now().strftime("%Y%m%d_%H%M%S")}'),
            type=media_data['type'],
            category=media_data['category'],
            file_path=media_data['file_path'],
            url=media_data['url'],
            thumbnail_url=media_data.get('thumbnail_url'),
            size=media_data.get('size', 0),
            duration=media_data.get('duration'),
            dimensions=media_data.get('dimensions'),
            metadata=media_data.get('metadata', {}),
            created_at=datetime.now().isoformat(),
            updated_at=datetime.now().isoformat()
        )

        # 保存到缓存
        self.metadata_cache[media_id] = media_item.model_dump()
        self.save_metadata()

        return media_item

    def get_media_item(self, media_id: str) -> Optional[MediaItem]:
        """获取媒体项目"""
        item_data = self.metadata_cache.get(media_id)
        if item_data:
            return MediaItem(**item_data)
        return None

    def get_all_media_items(self) -> List[MediaItem]:
        """获取所有媒体项目"""
        items = []
        for item_data in self.metadata_cache.values():
            try:
                items.append(MediaItem(**item_data))
            except Exception as e:
                print(f"Failed to parse media item: {e}")
        return items

    def update_media_item(self, media_id: str, updates: Dict[str, Any]) -> Optional[MediaItem]:
        """更新媒体项目"""
        if media_id not in self.metadata_cache:
            return None

        item_data = self.metadata_cache[media_id].copy()
        item_data.update(updates)
        item_data['updated_at'] = datetime.now().isoformat()

        self.metadata_cache[media_id] = item_data
        self.save_metadata()

        return MediaItem(**item_data)

    def delete_media_item(self, media_id: str) -> bool:
        """删除媒体项目"""
        if media_id not in self.metadata_cache:
            return False

        item_data = self.metadata_cache[media_id]

        # 删除文件
        try:
            file_path = Path(item_data['file_path'])
            if file_path.exists():
                file_path.unlink()

            # 删除缩略图
            if item_data.get('thumbnail_url'):
                thumbnail_path = Path(item_data['thumbnail_url'].replace('/output/', 'output/'))
                if thumbnail_path.exists():
                    thumbnail_path.unlink()
        except Exception as e:
            print(f"Failed to delete files: {e}")

        # 从缓存中删除
        del self.metadata_cache[media_id]
        self.save_metadata()

        return True

    def search_media_items(self, query: str = "", category: str = "", type_filter: str = "") -> List[MediaItem]:
        """搜索媒体项目"""
        items = self.get_all_media_items()

        # 文本搜索
        if query:
            query_lower = query.lower()
            items = [item for item in items if
                    query_lower in item.name.lower() or
                    query_lower in item.metadata.get('description', '').lower() or
                    query_lower in item.metadata.get('prompt', '').lower()]

        # 类别筛选
        if category:
            items = [item for item in items if item.category == category]

        # 类型筛选
        if type_filter:
            items = [item for item in items if item.type == type_filter]

        # 按创建时间排序
        items.sort(key=lambda x: x.created_at, reverse=True)

        return items

    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        items = self.get_all_media_items()

        stats = {
            'total': len(items),
            'by_category': {},
            'by_type': {},
            'total_size': 0
        }

        for item in items:
            # 按类别统计
            stats['by_category'][item.category] = stats['by_category'].get(item.category, 0) + 1

            # 按类型统计
            stats['by_type'][item.type] = stats['by_type'].get(item.type, 0) + 1

            # 总大小
            stats['total_size'] += item.size

        return stats

    def generate_id(self) -> str:
        """生成唯一ID"""
        import time
        import random
        import string

        timestamp = str(int(time.time() * 1000))
        random_str = ''.join(random.choices(string.ascii_lowercase + string.digits, k=8))
        return f"media_{timestamp}_{random_str}"

# 创建全局管理器实例
media_manager = MediaLibraryManager()

@router.get("/items")
async def get_media_items(
    query: str = "",
    category: str = "",
    type_filter: str = ""
):
    """获取媒体项目列表"""
    try:
        items = media_manager.search_media_items(query, category, type_filter)
        return {
            "success": True,
            "data": [item.model_dump() for item in items]
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/items/{media_id}")
async def get_media_item(media_id: str):
    """获取单个媒体项目"""
    item = media_manager.get_media_item(media_id)
    if not item:
        raise HTTPException(status_code=404, detail="Media item not found")

    return {
        "success": True,
        "data": item.model_dump()
    }

@router.post("/items")
async def add_media_item(media_data: dict):
    """添加媒体项目"""
    try:
        item = media_manager.add_media_item(media_data)
        return {
            "success": True,
            "data": item.model_dump()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.put("/items/{media_id}")
async def update_media_item(media_id: str, updates: dict):
    """更新媒体项目"""
    item = media_manager.update_media_item(media_id, updates)
    if not item:
        raise HTTPException(status_code=404, detail="Media item not found")

    return {
        "success": True,
        "data": item.model_dump()
    }

@router.delete("/items/{media_id}")
async def delete_media_item(media_id: str):
    """删除媒体项目"""
    success = media_manager.delete_media_item(media_id)
    if not success:
        raise HTTPException(status_code=404, detail="Media item not found")

    return {
        "success": True,
        "message": "Media item deleted successfully"
    }

@router.get("/stats")
async def get_media_stats():
    """获取媒体库统计信息"""
    try:
        stats = media_manager.get_statistics()
        return {
            "success": True,
            "data": stats
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/upload")
async def upload_media_file(
    file: UploadFile = File(...),
    category: str = Form("uploaded_image"),
    description: str = Form("")
):
    """上传媒体文件"""
    try:
        # 检查文件类型
        content_type = file.content_type or mimetypes.guess_type(file.filename)[0]
        if not content_type:
            raise HTTPException(status_code=400, detail="Unknown file type")

        # 确定媒体类型
        if content_type.startswith('image/'):
            media_type = 'image'
        elif content_type.startswith('video/'):
            media_type = 'video'
        elif content_type.startswith('audio/'):
            media_type = 'audio'
        else:
            raise HTTPException(status_code=400, detail="Unsupported file type")

        # 生成文件名
        file_id = media_manager.generate_id()
        file_extension = Path(file.filename).suffix
        filename = f"{file_id}{file_extension}"

        # 保存文件
        file_path = MEDIA_LIBRARY_DIR / filename
        with open(file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)

        # 创建媒体项目数据
        media_data = {
            'id': file_id,
            'name': file.filename,
            'type': media_type,
            'category': category,
            'file_path': str(file_path),
            'url': f"/output/media_library/{filename}",
            'size': file_path.stat().st_size,
            'metadata': {
                'source': 'upload',
                'original_name': file.filename,
                'mime_type': content_type,
                'description': description
            }
        }

        # 添加到媒体库
        item = media_manager.add_media_item(media_data)

        return {
            "success": True,
            "data": item.model_dump()
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
