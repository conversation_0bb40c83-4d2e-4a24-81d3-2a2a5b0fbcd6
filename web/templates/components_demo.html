<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>组件演示 - DaVinci AI Co-pilot PRO</title>
    <link rel="stylesheet" href="/static/css/style.css">
</head>
<body>
    <div class="app">
        <header class="header">
            <div class="header-content">
                <h1 class="header-title">🎬 DaVinci AI Co-pilot PRO - 组件演示</h1>
                <div class="header-actions">
                    <button class="btn btn-primary" onclick="showDemoModal()">演示模态框</button>
                </div>
            </div>
        </header>

        <main class="main">
            <div class="container">
                <!-- 通知演示 -->
                <div class="card">
                    <div class="card-header">
                        <h3>通知系统演示</h3>
                    </div>
                    <div class="card-body">
                        <div class="btn-group">
                            <button class="btn btn-success" onclick="showSuccessNotification()">成功通知</button>
                            <button class="btn btn-error" onclick="showErrorNotification()">错误通知</button>
                            <button class="btn btn-warning" onclick="showWarningNotification()">警告通知</button>
                            <button class="btn btn-info" onclick="showInfoNotification()">信息通知</button>
                        </div>
                    </div>
                </div>

                <!-- 进度条演示 -->
                <div class="card">
                    <div class="card-header">
                        <h3>进度条演示</h3>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <label>基础进度条</label>
                            <div class="progress" id="demo-progress-1">
                                <div class="progress-bar" style="width: 60%">60%</div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label>动画进度条</label>
                            <div class="progress progress-animated progress-striped" id="demo-progress-2">
                                <div class="progress-bar progress-bar-success" style="width: 40%">40%</div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label>错误状态进度条</label>
                            <div class="progress" id="demo-progress-3">
                                <div class="progress-bar progress-bar-error" style="width: 25%">25%</div>
                            </div>
                        </div>
                        
                        <div class="btn-group">
                            <button class="btn btn-primary" onclick="updateProgress()">更新进度</button>
                            <button class="btn btn-secondary" onclick="resetProgress()">重置进度</button>
                        </div>
                    </div>
                </div>

                <!-- 工具提示演示 -->
                <div class="card">
                    <div class="card-header">
                        <h3>工具提示演示</h3>
                    </div>
                    <div class="card-body">
                        <div class="btn-group">
                            <button class="btn btn-primary" 
                                    data-tooltip="这是一个顶部工具提示" 
                                    data-tooltip-placement="top">顶部提示</button>
                            <button class="btn btn-secondary" 
                                    data-tooltip="这是一个底部工具提示" 
                                    data-tooltip-placement="bottom">底部提示</button>
                            <button class="btn btn-success" 
                                    data-tooltip="这是一个左侧工具提示" 
                                    data-tooltip-placement="left">左侧提示</button>
                            <button class="btn btn-warning" 
                                    data-tooltip="这是一个右侧工具提示" 
                                    data-tooltip-placement="right">右侧提示</button>
                        </div>
                    </div>
                </div>

                <!-- 标签页演示 -->
                <div class="card">
                    <div class="card-header">
                        <h3>标签页演示</h3>
                    </div>
                    <div class="card-body">
                        <div class="tabs-demo">
                            <div class="tab-list">
                                <button class="tab active" data-tab="tab1">标签1</button>
                                <button class="tab" data-tab="tab2">标签2</button>
                                <button class="tab" data-tab="tab3">标签3</button>
                            </div>
                            <div class="tab-panels">
                                <div class="tab-panel active" id="tab1">
                                    <h4>标签1内容</h4>
                                    <p>这是第一个标签页的内容。您可以在这里放置任何内容。</p>
                                </div>
                                <div class="tab-panel" id="tab2">
                                    <h4>标签2内容</h4>
                                    <p>这是第二个标签页的内容。标签页组件支持键盘导航。</p>
                                </div>
                                <div class="tab-panel" id="tab3">
                                    <h4>标签3内容</h4>
                                    <p>这是第三个标签页的内容。组件系统完全模块化。</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 加载状态演示 -->
                <div class="card">
                    <div class="card-header">
                        <h3>加载状态演示</h3>
                    </div>
                    <div class="card-body">
                        <div class="btn-group">
                            <button class="btn btn-primary" onclick="showLoadingDemo()">显示加载</button>
                            <button class="btn btn-secondary" onclick="hideLoadingDemo()">隐藏加载</button>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 演示模态框 -->
    <div id="demo-modal" class="modal">
        <!-- 模态框内容将由JavaScript动态生成 -->
    </div>

    <!-- JavaScript -->
    <script src="/static/js/components.js"></script>
    
    <script>
        // 初始化应用
        let demoApp;
        
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化组件
            initializeComponents();
        });
        
        function initializeComponents() {
            // 初始化标签页
            const tabsContainer = document.querySelector('.tabs-demo');
            if (tabsContainer) {
                new TabsComponent(tabsContainer);
            }
            
            // 初始化工具提示
            document.querySelectorAll('[data-tooltip]').forEach(element => {
                new TooltipComponent(element, {
                    placement: element.getAttribute('data-tooltip-placement') || 'top',
                    trigger: 'hover'
                });
            });
            
            // 初始化进度条
            const progressBars = document.querySelectorAll('.progress');
            progressBars.forEach((element, index) => {
                new ProgressComponent(element, {
                    value: [60, 40, 25][index] || 0,
                    animated: element.classList.contains('progress-animated'),
                    striped: element.classList.contains('progress-striped')
                });
            });
        }
        
        // 通知演示函数
        function showSuccessNotification() {
            new NotificationComponent(null, {
                type: 'success',
                title: '操作成功',
                message: '您的操作已成功完成！',
                position: 'top-right'
            });
        }
        
        function showErrorNotification() {
            new NotificationComponent(null, {
                type: 'error',
                title: '操作失败',
                message: '抱歉，操作过程中出现了错误。',
                duration: 8000,
                position: 'top-right'
            });
        }
        
        function showWarningNotification() {
            new NotificationComponent(null, {
                type: 'warning',
                title: '注意',
                message: '请注意这个重要的警告信息。',
                position: 'top-right'
            });
        }
        
        function showInfoNotification() {
            new NotificationComponent(null, {
                type: 'info',
                title: '提示',
                message: '这是一条信息提示。',
                position: 'top-right'
            });
        }
        
        // 模态框演示
        function showDemoModal() {
            const modal = new ModalComponent(document.getElementById('demo-modal'), {
                title: '演示模态框',
                content: `
                    <div>
                        <h4>这是一个演示模态框</h4>
                        <p>模态框组件支持多种配置选项：</p>
                        <ul>
                            <li>可配置大小（small, medium, large, fullscreen）</li>
                            <li>可配置是否可关闭</li>
                            <li>支持背景点击关闭</li>
                            <li>支持键盘ESC关闭</li>
                            <li>支持自定义内容和按钮</li>
                        </ul>
                        <div class="progress progress-animated progress-striped mt-3">
                            <div class="progress-bar progress-bar-success" style="width: 75%">75%</div>
                        </div>
                    </div>
                `,
                footer: `
                    <button class="btn btn-secondary" onclick="closeModal()">取消</button>
                    <button class="btn btn-primary" onclick="confirmModal()">确认</button>
                `,
                size: 'medium'
            });
            
            modal.open();
            window.currentModal = modal;
        }
        
        function closeModal() {
            if (window.currentModal) {
                window.currentModal.close();
            }
        }
        
        function confirmModal() {
            showSuccessNotification();
            closeModal();
        }
        
        // 进度条演示
        let progressValues = [60, 40, 25];
        
        function updateProgress() {
            const progressBars = document.querySelectorAll('.progress .progress-bar');
            progressBars.forEach((bar, index) => {
                progressValues[index] = Math.min(100, progressValues[index] + 10);
                bar.style.width = progressValues[index] + '%';
                bar.textContent = progressValues[index] + '%';
            });
        }
        
        function resetProgress() {
            progressValues = [0, 0, 0];
            const progressBars = document.querySelectorAll('.progress .progress-bar');
            progressBars.forEach((bar, index) => {
                bar.style.width = '0%';
                bar.textContent = '0%';
            });
        }
        
        // 加载状态演示
        let loadingModal;
        
        function showLoadingDemo() {
            loadingModal = new ModalComponent(document.createElement('div'), {
                title: '加载中',
                content: `
                    <div class="text-center">
                        <div class="loading-spinner"></div>
                        <p class="mt-3">正在处理您的请求，请稍候...</p>
                    </div>
                `,
                closable: false,
                backdrop: false,
                size: 'small'
            });
            
            loadingModal.open();
            
            // 3秒后自动关闭
            setTimeout(() => {
                hideLoadingDemo();
                showSuccessNotification();
            }, 3000);
        }
        
        function hideLoadingDemo() {
            if (loadingModal) {
                loadingModal.close();
                loadingModal = null;
            }
        }
    </script>
    
    <style>
        .tabs-demo .tab-list {
            display: flex;
            border-bottom: 2px solid var(--border-color);
            margin-bottom: 1rem;
        }
        
        .tabs-demo .tab {
            background: none;
            border: none;
            padding: 0.75rem 1.5rem;
            cursor: pointer;
            color: var(--text-secondary);
            border-bottom: 2px solid transparent;
            transition: all 0.2s ease;
        }
        
        .tabs-demo .tab:hover {
            color: var(--text-primary);
            background: var(--hover-bg);
        }
        
        .tabs-demo .tab.active {
            color: var(--primary-color);
            border-bottom-color: var(--primary-color);
        }
        
        .tabs-demo .tab-panel {
            display: none;
            padding: 1rem 0;
        }
        
        .tabs-demo .tab-panel.active {
            display: block;
        }
        
        .mt-3 {
            margin-top: 1rem;
        }
    </style>
</body>
</html>
