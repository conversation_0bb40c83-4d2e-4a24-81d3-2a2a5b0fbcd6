<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DaVinci AI Co-pilot PRO</title>
    <link rel="stylesheet" href="/static/css/style.css">
    <link rel="stylesheet" href="/static/css/frame-capture.css">
</head>

<body>
    <div id="app">
        <!-- 头部导航 -->
        <header class="header">
            <div class="container">
                <div class="logo">
                    <h1>🎬 DaVinci AI Co-pilot PRO</h1>
                    <span class="version">v0.1.0</span>
                </div>
                <nav class="nav">
                    <button class="nav-btn active" data-tab="dashboard">仪表板</button>
                    <button class="nav-btn" data-tab="text-generation">文本生成</button>
                    <button class="nav-btn" data-tab="text-analysis">文案分析</button>
                    <button class="nav-btn" data-tab="prompt-enhancement">提示词增强</button>
                    <button class="nav-btn" data-tab="speech-synthesis">语音合成</button>
                    <button class="nav-btn" data-tab="video-generation">视频生成</button>
                    <button class="nav-btn" data-tab="image-generation">图像生成</button>
                    <button class="nav-btn" data-tab="media-library">媒体库</button>
                    <button class="nav-btn" data-tab="davinci-integration">DaVinci集成</button>
                    <button class="nav-btn" data-tab="settings">设置</button>
                </nav>
                <div class="status">
                    <span class="status-indicator" id="status-indicator">●</span>
                    <span id="status-text">连接中...</span>
                </div>
            </div>
        </header>

        <!-- 主要内容区域 -->
        <main class="main">
            <div class="container">
                <!-- 仪表板 -->
                <div id="dashboard" class="tab-content active">
                    <div class="dashboard-grid">
                        <div class="card">
                            <h3>🤖 AI服务状态</h3>
                            <div id="service-status">
                                <div class="loading">加载中...</div>
                            </div>
                        </div>

                        <div class="card">
                            <h3>📊 使用统计</h3>
                            <div id="usage-stats">
                                <div class="stat-item">
                                    <span class="stat-label">今日请求:</span>
                                    <span class="stat-value" id="today-requests">0</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">成功率:</span>
                                    <span class="stat-value" id="success-rate">0%</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">平均响应时间:</span>
                                    <span class="stat-value" id="avg-response-time">0ms</span>
                                </div>
                            </div>
                        </div>

                        <div class="card">
                            <h3>🎯 快速操作</h3>
                            <div class="quick-actions">
                                <button class="action-btn" onclick="switchTab('text-generation')">
                                    ✍️ 文本生成
                                </button>
                                <button class="action-btn" onclick="switchTab('text-analysis')">
                                    📝 文案分析
                                </button>
                                <button class="action-btn" onclick="switchTab('prompt-enhancement')">
                                    ✨ 提示词增强
                                </button>
                                <button class="action-btn" onclick="switchTab('speech-synthesis')">
                                    🎤 语音合成
                                </button>
                                <button class="action-btn" onclick="switchTab('video-generation')">
                                    🎥 视频生成
                                </button>
                                <button class="action-btn" onclick="switchTab('image-generation')">
                                    🎨 图像生成
                                </button>
                            </div>
                        </div>

                        <div class="card">
                            <h3>📈 服务能力</h3>
                            <div id="service-capabilities">
                                <div class="loading">加载中...</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 文本生成 -->
                <div id="text-generation" class="tab-content">
                    <div class="tool-container">
                        <h2>✍️ 文本生成</h2>
                        <p class="description">使用AI生成高质量的文本内容</p>

                        <div class="form-group">
                            <label for="generation-prompt">输入提示词：</label>
                            <div class="prompt-input-container">
                                <textarea id="generation-prompt" placeholder="请输入您的提示词，描述您想要生成的内容..."
                                    rows="4"></textarea>
                                <button id="enhance-generation-prompt-btn" class="enhance-btn" title="优化提示词">
                                    <span class="enhance-icon">✨</span>
                                    <span class="enhance-text">优化</span>
                                </button>
                                <button class="translate-btn" onclick="autoTranslateTextarea('generation-prompt')"
                                    title="智能翻译">
                                    <span class="translate-icon">🌐</span>
                                    <span class="translate-text">翻译</span>
                                </button>
                            </div>
                            <div id="generation-prompt-preview" class="prompt-preview" style="display: none;">
                                <div class="preview-header">
                                    <span class="preview-title">优化后的提示词：</span>
                                    <div class="preview-actions">
                                        <button id="apply-generation-prompt-btn" class="apply-btn">使用优化版本</button>
                                        <button id="cancel-generation-prompt-btn" class="cancel-btn">保持原版</button>
                                    </div>
                                </div>
                                <div id="generation-prompt-enhanced" class="enhanced-prompt"></div>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="generation-provider">AI服务商：</label>
                                <select id="generation-provider">
                                    <option value="">自动选择</option>
                                    <option value="deepseek">DeepSeek</option>
                                    <option value="minimax">🔗 Minimax (MCP)</option>
                                    <option value="volcano">火山引擎</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="generation-length">文本长度：</label>
                                <select id="generation-length">
                                    <option value="short">短文本 (~100字)</option>
                                    <option value="medium" selected>中等长度 (~300字)</option>
                                    <option value="long">长文本 (~500字)</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button id="generate-text-btn" class="primary-btn">
                                <span class="btn-text">开始生成</span>
                                <span class="btn-loading" style="display: none;">生成中...</span>
                            </button>
                            <button id="clear-generation-btn" class="secondary-btn">清空</button>
                        </div>

                        <div id="generation-result" class="result-container" style="display: none;">
                            <h3>生成结果</h3>
                            <div id="generation-content"></div>
                            <div class="result-actions">
                                <button id="copy-generation-btn" class="secondary-btn">复制文本</button>
                                <button id="save-generation-btn" class="secondary-btn">保存文件</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 文案分析 -->
                <div id="text-analysis" class="tab-content">
                    <div class="tool-container">
                        <h2>📝 文案分析</h2>
                        <p class="description">将文案智能拆解为视频场景，提供详细的制作建议</p>

                        <div class="form-group">
                            <label for="analysis-text">输入文案内容：</label>
                            <div class="textarea-container">
                                <textarea id="analysis-text" placeholder="请输入需要分析的文案内容..." rows="6"></textarea>
                                <button class="translate-btn" onclick="autoTranslateTextarea('analysis-text')"
                                    title="智能翻译">
                                    <span class="translate-icon">🌐</span>
                                    <span class="translate-text">翻译</span>
                                </button>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="analysis-provider">选择AI服务商：</label>
                            <select id="analysis-provider">
                                <option value="">自动选择</option>
                                <option value="deepseek">DeepSeek</option>
                                <option value="minimax">🔗 Minimax (MCP)</option>
                                <option value="volcano">火山引擎</option>
                            </select>
                        </div>

                        <div class="form-actions">
                            <button id="analyze-btn" class="primary-btn">
                                <span class="btn-text">开始分析</span>
                                <span class="btn-loading" style="display: none;">分析中...</span>
                            </button>
                            <button id="clear-analysis-btn" class="secondary-btn">清空</button>
                        </div>

                        <div id="analysis-result" class="result-container" style="display: none;">
                            <h3>分析结果</h3>
                            <div id="analysis-content"></div>
                        </div>
                    </div>
                </div>

                <!-- 提示词增强 -->
                <div id="prompt-enhancement" class="tab-content">
                    <div class="tool-container">
                        <h2>✨ 提示词增强</h2>
                        <p class="description">将简单的关键词或短语转换为详细、高质量的AI提示词</p>

                        <div class="form-group">
                            <label for="enhancement-input">输入简单提示词：</label>
                            <div class="textarea-container">
                                <textarea id="enhancement-input" placeholder="请输入简单的关键词或短语，例如：'画一只猫'、'写一篇文章'..."
                                    rows="3"></textarea>
                                <button class="translate-btn" onclick="autoTranslateTextarea('enhancement-input')"
                                    title="智能翻译">
                                    <span class="translate-icon">🌐</span>
                                    <span class="translate-text">翻译</span>
                                </button>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="enhancement-provider">MCP服务商：</label>
                                <select id="enhancement-provider">
                                    <option value="">自动选择</option>
                                    <option value="enhance-prompt">PromptPilot</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="enhancement-style">增强风格：</label>
                                <select id="enhancement-style">
                                    <option value="detailed">详细描述</option>
                                    <option value="creative">创意导向</option>
                                    <option value="professional">专业规范</option>
                                    <option value="technical">技术精确</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button id="enhance-prompt-btn" class="primary-btn">
                                <span class="btn-text">增强提示词</span>
                                <span class="btn-loading" style="display: none;">增强中...</span>
                            </button>
                            <button id="clear-enhancement-btn" class="secondary-btn">清空</button>
                        </div>

                        <div id="enhancement-result" class="result-container" style="display: none;">
                            <h3>增强结果</h3>
                            <div class="enhancement-comparison">
                                <div class="original-prompt">
                                    <h4>原始提示词：</h4>
                                    <div id="original-prompt-content"></div>
                                </div>
                                <div class="enhanced-prompt">
                                    <h4>增强后提示词：</h4>
                                    <div id="enhanced-prompt-content"></div>
                                </div>
                            </div>
                            <div class="result-actions">
                                <button id="copy-enhanced-prompt-btn" class="secondary-btn">复制增强版</button>
                                <button id="use-enhanced-prompt-btn" class="primary-btn">使用此提示词</button>
                                <button id="regenerate-prompt-btn" class="secondary-btn">重新生成</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 语音合成 -->
                <div id="speech-synthesis" class="tab-content">
                    <div class="tool-container">
                        <h2>🎤 语音合成</h2>
                        <p class="description">使用ElevenLabs AI技术将文本转换为高质量的语音文件</p>

                        <!-- 文本输入区域 -->
                        <div class="form-group">
                            <label for="speech-text">输入文本内容：</label>
                            <div class="prompt-input-container">
                                <textarea id="speech-text" placeholder="请输入需要合成语音的文本..." rows="4"
                                    maxlength="5000"></textarea>
                                <div class="char-counter">
                                    <span id="char-count">0/5000</span>
                                </div>
                            </div>
                            <small class="help-text">支持多语言文本，最多5000个字符</small>
                        </div>

                        <!-- 服务提供商和语音选择 - 居中布局 -->
                        <div
                            style="display: flex; flex-direction: column; align-items: center; gap: 20px; margin-bottom: 30px;">
                            <!-- 服务提供商选择 -->
                            <div style="width: 100%; max-width: 500px; text-align: center;">
                                <label for="speech-provider"
                                    style="display: block; font-weight: bold; font-size: 16px; margin-bottom: 8px; color: var(--text-primary);">
                                    🔧 服务提供商：
                                </label>
                                <select id="speech-provider"
                                    style="width: 100%; padding: 12px; font-size: 14px; border-radius: 6px; border: 1px solid var(--border-color); background-color: var(--bg-tertiary); color: var(--text-primary);">
                                    <option value="elevenlabs" selected>🎤 ElevenLabs (推荐)</option>
                                    <option value="minimax">🔗 MiniMax (MCP)</option>
                                </select>
                                <small class="help-text" id="provider-help-text"
                                    style="display: block; margin-top: 6px; color: var(--text-secondary);">ElevenLabs提供最高质量的AI语音合成</small>
                            </div>

                            <!-- 语音选择 -->
                            <div style="width: 100%; max-width: 500px; text-align: center;">
                                <label for="voice-selector"
                                    style="display: block; font-weight: bold; font-size: 16px; margin-bottom: 8px; color: var(--text-primary);">
                                    🎤 选择语音：
                                </label>
                                <select id="voice-selector"
                                    style="width: 100%; padding: 12px; font-size: 14px; border-radius: 6px; border: 1px solid var(--border-color); background-color: var(--bg-tertiary); color: var(--text-primary);">
                                    <option value="">选择语音...</option>
                                    <optgroup id="clone-voices-group" label="🎭 我的声音克隆" style="display: none;">
                                        <!-- 克隆声音选项将通过JavaScript动态加载 -->
                                    </optgroup>
                                    <optgroup id="preset-voices-group" label="🎤 预设语音">
                                        <!-- 预设语音选项将通过JavaScript动态加载 -->
                                    </optgroup>
                                </select>
                                <small class="help-text"
                                    style="display: block; margin-top: 6px; color: var(--text-secondary);">显示您的语音和预制语音（100%可用）</small>
                                <div style="margin-top: 10px; text-align: center;">
                                    <button id="manage-clone-voices-btn" class="secondary-btn"
                                        style="font-size: 12px; padding: 6px 12px; margin-right: 8px;">
                                        <i class="fas fa-cog"></i> 管理克隆
                                    </button>
                                    <button id="create-clone-voices-btn" class="btn btn-primary"
                                        style="font-size: 12px; padding: 6px 12px;">
                                        <i class="fas fa-plus"></i> 创建克隆
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 其他选项区域 -->
                        <div class="form-row">
                            <div class="form-group" style="display: none;">
                                <!-- 这个div用于保持原有的语音搜索面板结构 -->
                            </div>

                            <!-- 废弃的语音搜索面板已移除 -->

                        </div>



                        <!-- ElevenLabs 语音设置参数 - 居中布局 -->
                        <div id="elevenlabs-voice-settings" class="form-section" style="display: none;">
                            <h3 style="text-align: center; margin-bottom: 30px; color: var(--text-primary);">🎛️
                                ElevenLabs 语音设置</h3>

                            <!-- 第一行：语音模型和音频质量 -->
                            <div
                                style="display: flex; gap: 20px; width: 100%; max-width: 800px; justify-content: center; flex-wrap: wrap; margin-bottom: 20px;">
                                <div class="form-group" style="flex: 1; min-width: 250px; text-align: center;">
                                    <label for="voice-model"
                                        style="display: block; font-weight: bold; font-size: 14px; margin-bottom: 8px; color: var(--text-primary);">
                                        🎵 语音模型：
                                    </label>
                                    <select id="voice-model"
                                        style="width: 100%; padding: 10px; font-size: 14px; border-radius: 6px; border: 1px solid var(--border-color); background-color: var(--bg-tertiary); color: var(--text-primary);">
                                        <option value="eleven_multilingual_v2">多语言模型 v2 (推荐)</option>
                                        <option value="eleven_turbo_v2_5">快速模型 v2.5</option>
                                        <option value="eleven_flash_v2_5">闪电模型 v2.5</option>
                                        <option value="eleven_multilingual_v1">多语言模型 v1</option>
                                    </select>
                                    <small class="help-text"
                                        style="display: block; margin-top: 6px; color: var(--text-secondary);">不同模型在质量和速度上有所差异</small>
                                </div>

                                <div class="form-group" style="flex: 1; min-width: 250px; text-align: center;">
                                    <label for="audio-quality"
                                        style="display: block; font-weight: bold; font-size: 14px; margin-bottom: 8px; color: var(--text-primary);">
                                        🔊 音频质量：
                                    </label>
                                    <select id="audio-quality"
                                        style="width: 100%; padding: 10px; font-size: 14px; border-radius: 6px; border: 1px solid var(--border-color); background-color: var(--bg-tertiary); color: var(--text-primary);">
                                        <option value="mp3_44100_128">标准质量 (44.1kHz, 128kbps)</option>
                                        <option value="mp3_44100_192">高质量 (44.1kHz, 192kbps)</option>
                                        <option value="mp3_22050_32">低质量 (22.05kHz, 32kbps)</option>
                                        <option value="pcm_16000">PCM格式 (16kHz)</option>
                                        <option value="pcm_22050">PCM格式 (22.05kHz)</option>
                                        <option value="pcm_24000">PCM格式 (24kHz)</option>
                                        <option value="pcm_44100">PCM格式 (44.1kHz)</option>
                                    </select>
                                    <small class="help-text"
                                        style="display: block; margin-top: 6px; color: var(--text-secondary);">选择适合您项目需求的音频格式和质量</small>
                                </div>
                            </div>

                            <!-- 第二行：稳定性和相似度增强 -->
                            <div
                                style="display: flex; gap: 20px; width: 100%; max-width: 800px; justify-content: center; flex-wrap: wrap; margin-bottom: 20px;">
                                <div class="form-group" style="flex: 1; min-width: 250px; text-align: center;">
                                    <label for="elevenlabs-stability"
                                        style="display: block; font-weight: bold; font-size: 14px; margin-bottom: 8px; color: var(--text-primary);">
                                        🎯 稳定性：<span id="stability-value" style="color: #007bff;">0.5</span>
                                    </label>
                                    <input type="range" id="elevenlabs-stability" min="0" max="1" step="0.1" value="0.5"
                                        style="width: 100%; margin-bottom: 6px;">
                                    <small class="help-text"
                                        style="display: block; color: var(--text-secondary);">控制语音稳定性，较低值增加情感变化</small>
                                </div>

                                <div class="form-group" style="flex: 1; min-width: 250px; text-align: center;">
                                    <label for="elevenlabs-similarity-boost"
                                        style="display: block; font-weight: bold; font-size: 14px; margin-bottom: 8px; color: var(--text-primary);">
                                        🔍 相似度增强：<span id="similarity-boost-value" style="color: #007bff;">0.8</span>
                                    </label>
                                    <input type="range" id="elevenlabs-similarity-boost" min="0" max="1" step="0.1"
                                        value="0.8" style="width: 100%; margin-bottom: 6px;">
                                    <small class="help-text"
                                        style="display: block; color: var(--text-secondary);">控制与原始语音的相似度</small>
                                </div>
                            </div>

                            <!-- 第三行：风格强度和语速 -->
                            <div
                                style="display: flex; gap: 20px; width: 100%; max-width: 800px; justify-content: center; flex-wrap: wrap; margin-bottom: 20px;">
                                <div class="form-group" style="flex: 1; min-width: 250px; text-align: center;">
                                    <label for="elevenlabs-style"
                                        style="display: block; font-weight: bold; font-size: 14px; margin-bottom: 8px; color: var(--text-primary);">
                                        🎭 风格强度：<span id="style-value" style="color: #007bff;">0.0</span>
                                    </label>
                                    <input type="range" id="elevenlabs-style" min="0" max="1" step="0.1" value="0.0"
                                        style="width: 100%; margin-bottom: 6px;">
                                    <small class="help-text"
                                        style="display: block; color: var(--text-secondary);">放大原始说话者的风格特征</small>
                                </div>

                                <div class="form-group" style="flex: 1; min-width: 250px; text-align: center;">
                                    <label for="elevenlabs-speed"
                                        style="display: block; font-weight: bold; font-size: 14px; margin-bottom: 8px; color: var(--text-primary);">
                                        ⚡ 语速：<span id="elevenlabs-speed-value" style="color: #007bff;">1.0</span>
                                    </label>
                                    <input type="range" id="elevenlabs-speed" min="0.25" max="4.0" step="0.25"
                                        value="1.0" style="width: 100%; margin-bottom: 6px;">
                                    <small class="help-text"
                                        style="display: block; color: var(--text-secondary);">调整语音播放速度 (0.25x -
                                        4.0x)</small>
                                </div>
                            </div>

                            <!-- 第四行：扬声器增强 -->
                            <div
                                style="display: flex; gap: 20px; width: 100%; max-width: 800px; justify-content: center; flex-wrap: wrap; margin-bottom: 20px;">
                                <div class="form-group" style="flex: 1; min-width: 250px; text-align: center;">
                                    <label
                                        style="display: flex; align-items: center; justify-content: center; gap: 8px; font-weight: bold; font-size: 14px; color: var(--text-primary);">
                                        <input type="checkbox" id="elevenlabs-use-speaker-boost" checked>
                                        🔊 扬声器增强
                                    </label>
                                    <small class="help-text"
                                        style="display: block; color: var(--text-secondary); margin-top: 6px;">提升与原始说话者的相似度（增加延迟）</small>
                                </div>
                            </div>
                        </div>

                        <!-- MiniMax 特有参数 - 居中布局 -->
                        <div id="minimax-params" class="form-section" style="display: none;">
                            <h3 style="text-align: center; margin-bottom: 30px; color: var(--text-primary);">🎛️ MiniMax
                                语音参数</h3>

                            <!-- 第一行：模型和情感 -->
                            <div
                                style="display: flex; flex-direction: column; align-items: center; gap: 20px; margin-bottom: 30px;">
                                <div
                                    style="display: flex; gap: 20px; width: 100%; max-width: 800px; justify-content: center; flex-wrap: wrap;">
                                    <div class="form-group" style="flex: 1; min-width: 250px; text-align: center;">
                                        <label for="minimax-model"
                                            style="display: block; font-weight: bold; font-size: 14px; margin-bottom: 8px; color: var(--text-primary);">🤖
                                            MiniMax 模型：</label>
                                        <select id="minimax-model"
                                            style="width: 100%; padding: 10px; font-size: 14px; border-radius: 6px; border: 1px solid var(--border-color); background-color: var(--bg-tertiary); color: var(--text-primary);">
                                            <option value="speech-02-hd">高清语音模型 (推荐)</option>
                                            <option value="speech-01">标准语音模型</option>
                                        </select>
                                        <small class="help-text"
                                            style="display: block; margin-top: 6px; color: var(--text-secondary);">选择
                                            MiniMax
                                            语音合成模型</small>
                                    </div>

                                    <div class="form-group" style="flex: 1; min-width: 250px; text-align: center;">
                                        <label for="minimax-emotion"
                                            style="display: block; font-weight: bold; font-size: 14px; margin-bottom: 8px; color: var(--text-primary);">😊
                                            情感风格：</label>
                                        <select id="minimax-emotion"
                                            style="width: 100%; padding: 10px; font-size: 14px; border-radius: 6px; border: 1px solid var(--border-color); background-color: var(--bg-tertiary); color: var(--text-primary);">
                                            <option value="neutral">中性 (默认)</option>
                                            <option value="happy">开心</option>
                                            <option value="sad">悲伤</option>
                                            <option value="angry">愤怒</option>
                                            <option value="excited">兴奋</option>
                                            <option value="calm">平静</option>
                                        </select>
                                        <small class="help-text"
                                            style="display: block; margin-top: 6px; color: var(--text-secondary);">选择语音的情感表达风格</small>
                                    </div>
                                </div>
                            </div>

                            <!-- 第二行：语速和音量滑块 -->
                            <div
                                style="display: flex; gap: 20px; width: 100%; max-width: 800px; justify-content: center; flex-wrap: wrap; margin-bottom: 20px;">
                                <div class="form-group" style="flex: 1; min-width: 250px; text-align: center;">
                                    <label for="minimax-speed"
                                        style="display: block; font-weight: bold; font-size: 14px; margin-bottom: 8px; color: var(--text-primary);">
                                        🏃 语速：<span id="speed-value" style="color: #007bff;">1.0</span>
                                    </label>
                                    <input type="range" id="minimax-speed" min="0.5" max="2.0" step="0.1" value="1.0"
                                        style="width: 100%; margin-bottom: 6px;">
                                    <small class="help-text"
                                        style="display: block; color: var(--text-secondary);">调整语音播放速度 (0.5x -
                                        2.0x)</small>
                                </div>

                                <div class="form-group" style="flex: 1; min-width: 250px; text-align: center;">
                                    <label for="minimax-volume"
                                        style="display: block; font-weight: bold; font-size: 14px; margin-bottom: 8px; color: var(--text-primary);">
                                        🔊 音量：<span id="volume-value" style="color: #007bff;">1.0</span>
                                    </label>
                                    <input type="range" id="minimax-volume" min="0.1" max="2.0" step="0.1" value="1.0"
                                        style="width: 100%; margin-bottom: 6px;">
                                    <small class="help-text"
                                        style="display: block; color: var(--text-secondary);">调整语音音量 (0.1x -
                                        2.0x)</small>
                                </div>
                            </div>

                            <!-- 第三行：音调和采样率 -->
                            <div
                                style="display: flex; gap: 20px; width: 100%; max-width: 800px; justify-content: center; flex-wrap: wrap; margin-bottom: 20px;">
                                <div class="form-group" style="flex: 1; min-width: 250px; text-align: center;">
                                    <label for="minimax-pitch"
                                        style="display: block; font-weight: bold; font-size: 14px; margin-bottom: 8px; color: var(--text-primary);">
                                        🎵 音调：<span id="pitch-value" style="color: #007bff;">0</span>
                                    </label>
                                    <input type="range" id="minimax-pitch" min="-20" max="20" step="1" value="0"
                                        style="width: 100%; margin-bottom: 6px;">
                                    <small class="help-text"
                                        style="display: block; color: var(--text-secondary);">调整语音音调 (-20 到
                                        +20)</small>
                                </div>

                                <div class="form-group" style="flex: 1; min-width: 250px; text-align: center;">
                                    <label for="minimax-sample-rate"
                                        style="display: block; font-weight: bold; font-size: 14px; margin-bottom: 8px; color: var(--text-primary);">📊
                                        采样率：</label>
                                    <select id="minimax-sample-rate"
                                        style="width: 100%; padding: 10px; font-size: 14px; border-radius: 6px; border: 1px solid var(--border-color); background-color: var(--bg-tertiary); color: var(--text-primary);">
                                        <option value="16000">16kHz</option>
                                        <option value="22050">22.05kHz</option>
                                        <option value="24000" selected>24kHz (推荐)</option>
                                        <option value="44100">44.1kHz</option>
                                        <option value="48000">48kHz</option>
                                    </select>
                                    <small class="help-text"
                                        style="display: block; margin-top: 6px; color: var(--text-secondary);">选择音频采样率</small>
                                </div>
                            </div>

                            <!-- 第四行：音频格式和SSML -->
                            <div
                                style="display: flex; gap: 20px; width: 100%; max-width: 800px; justify-content: center; flex-wrap: wrap; margin-bottom: 20px;">
                                <div class="form-group" style="flex: 1; min-width: 250px; text-align: center;">
                                    <label for="minimax-format"
                                        style="display: block; font-weight: bold; font-size: 14px; margin-bottom: 8px; color: var(--text-primary);">🎧
                                        音频格式：</label>
                                    <select id="minimax-format"
                                        style="width: 100%; padding: 10px; font-size: 14px; border-radius: 6px; border: 1px solid var(--border-color); background-color: var(--bg-tertiary); color: var(--text-primary);">
                                        <option value="mp3" selected>MP3</option>
                                        <option value="wav">WAV</option>
                                        <option value="flac">FLAC</option>
                                    </select>
                                    <small class="help-text"
                                        style="display: block; margin-top: 6px; color: var(--text-secondary);">选择输出音频格式</small>
                                </div>

                                <div class="form-group"
                                    style="flex: 1; min-width: 250px; text-align: center; display: flex; flex-direction: column; justify-content: center;">
                                    <label for="minimax-ssml"
                                        style="display: flex; align-items: center; justify-content: center; font-weight: bold; font-size: 14px; color: var(--text-primary); margin-bottom: 8px;">
                                        <input type="checkbox" id="minimax-ssml" style="margin-right: 8px;">
                                        📝 启用 SSML
                                    </label>
                                    <small class="help-text"
                                        style="display: block; color: var(--text-secondary);">启用语音合成标记语言支持</small>
                                </div>
                            </div>
                        </div>

                        <!-- 操作按钮 -->
                        <div class="form-actions">
                            <button id="synthesize-btn" class="primary-btn" disabled>
                                <i class="fas fa-microphone"></i>
                                <span class="btn-text">生成语音</span>
                                <span class="btn-loading" style="display: none;">生成中...</span>
                            </button>
                            <button id="clear-btn" class="secondary-btn">
                                <i class="fas fa-eraser"></i>
                                清空
                            </button>
                        </div>

                        <!-- 进度显示 -->
                        <div id="synthesis-progress" class="progress-container" style="display: none;">
                            <div class="progress-bar">
                                <div class="progress-fill"></div>
                            </div>
                            <div class="progress-text">正在生成语音...</div>
                        </div>

                        <!-- 音频播放器容器 -->
                        <div id="audio-player-container" class="audio-output" style="display: none;">
                            <!-- 音频播放器将在这里动态生成 -->
                        </div>

                        <!-- 声音克隆创建界面 -->
                        <div id="voice-clone-creator" class="voice-clone-section"
                            style="display: none; margin-top: 30px; padding: 20px; border: 2px dashed #007bff; border-radius: 8px; background-color: var(--bg-secondary);">
                            <h3 style="text-align: center; margin-bottom: 20px; color: var(--text-primary);">
                                🎭 创建声音克隆
                            </h3>

                            <!-- 音频来源选择 -->
                            <div class="form-group" style="margin-bottom: 20px;">
                                <label style="font-weight: bold; margin-bottom: 10px; display: block;">音频来源：</label>
                                <div style="display: flex; gap: 15px; flex-wrap: wrap;">
                                    <label style="display: flex; align-items: center; cursor: pointer;">
                                        <input type="radio" name="audio-source" value="davinci" checked
                                            style="margin-right: 8px;">
                                        <span>🎬 从达芬奇提取音频</span>
                                    </label>
                                    <label style="display: flex; align-items: center; cursor: pointer;">
                                        <input type="radio" name="audio-source" value="upload"
                                            style="margin-right: 8px;">
                                        <span>📁 上传音频文件</span>
                                    </label>
                                </div>
                            </div>

                            <!-- 达芬奇音频提取区域 -->
                            <div id="davinci-audio-section" class="audio-source-section">
                                <div class="form-group">
                                    <label for="audio-track-index">选择音轨：</label>
                                    <select id="audio-track-index" style="width: 200px; padding: 8px;">
                                        <option value="1">音轨 1</option>
                                        <option value="2">音轨 2</option>
                                        <option value="3">音轨 3</option>
                                        <option value="4">音轨 4</option>
                                    </select>
                                </div>
                                <button id="extract-davinci-audio-btn" class="btn btn-primary"
                                    style="margin-bottom: 15px;">
                                    <i class="fas fa-download"></i> 从达芬奇提取音频
                                </button>
                            </div>

                            <!-- 文件上传区域 -->
                            <div id="upload-audio-section" class="audio-source-section" style="display: none;">
                                <div class="form-group">
                                    <label for="clone-audio-files">上传音频文件：</label>
                                    <input type="file" id="clone-audio-files" multiple accept="audio/*"
                                        style="width: 100%; padding: 10px; border: 1px solid var(--border-color); border-radius: 4px;">
                                    <small class="help-text">支持 MP3, WAV, M4A 等格式，建议上传多个样本以获得更好的克隆效果</small>
                                </div>
                            </div>

                            <!-- 克隆参数设置 -->
                            <div id="clone-parameters" class="form-section" style="display: none; margin-top: 20px;">
                                <h4 style="margin-bottom: 15px;">克隆参数设置</h4>

                                <div class="form-row" style="display: flex; gap: 20px; flex-wrap: wrap;">
                                    <div class="form-group" style="flex: 1; min-width: 200px;">
                                        <label for="clone-name">克隆名称：</label>
                                        <input type="text" id="clone-name" placeholder="为您的声音克隆命名"
                                            style="width: 100%; padding: 8px; border: 1px solid var(--border-color); border-radius: 4px;">
                                    </div>

                                    <div class="form-group" style="flex: 1; min-width: 200px;">
                                        <label for="clone-provider">克隆提供商：</label>
                                        <select id="clone-provider" style="width: 100%; padding: 8px;">
                                            <option value="elevenlabs">ElevenLabs (推荐)</option>
                                            <option value="minimax">MiniMax</option>
                                        </select>
                                    </div>
                                </div>

                                <!-- 创建按钮 -->
                                <div class="form-actions" style="margin-top: 20px; text-align: center;">
                                    <button id="create-voice-clone-btn" class="btn btn-success" disabled>
                                        <i class="fas fa-magic"></i> 创建声音克隆
                                    </button>
                                    <button id="cancel-clone-creation-btn" class="btn btn-secondary"
                                        style="margin-left: 10px;">
                                        <i class="fas fa-times"></i> 取消
                                    </button>
                                </div>
                            </div>

                            <!-- 克隆进度显示 -->
                            <div id="clone-progress" class="progress-container"
                                style="display: none; margin-top: 20px;">
                                <div class="progress-bar">
                                    <div class="progress-fill"></div>
                                </div>
                                <div class="progress-text">正在创建声音克隆...</div>
                                <div class="progress-details"
                                    style="margin-top: 10px; font-size: 12px; color: var(--text-secondary);">
                                    <!-- 详细进度信息 -->
                                </div>
                            </div>
                        </div>

                        <!-- DaVinci集成功能 -->
                        <div class="form-section">
                            <h3>🎬 DaVinci Resolve集成</h3>
                            <div class="integration-actions">
                                <button class="integration-btn" onclick="speechSynthesis.extractTimelineText()">
                                    <i class="fas fa-file-text"></i>
                                    从时间线提取文本
                                </button>
                                <button class="integration-btn" onclick="speechSynthesis.generateSubtitleVoiceover()">
                                    <i class="fas fa-closed-captioning"></i>
                                    基于字幕生成配音
                                </button>
                            </div>
                            <small class="help-text">快速从DaVinci Resolve项目中提取文本或基于现有字幕生成配音</small>
                        </div>

                        <!-- 使用提示 -->
                        <div class="tips-section">
                            <h3>💡 使用提示</h3>
                            <ul class="tips-list">
                                <li>选择合适的语音模型：多语言模型适合中英文混合，快速模型适合实时应用</li>
                                <li>文本长度建议控制在1000字符以内，以获得最佳合成效果</li>
                                <li>生成的音频可直接导入到DaVinci Resolve时间线中</li>
                                <li>支持多种音频格式，建议使用MP3格式以获得最佳兼容性</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- 视频生成 -->
                <div id="video-generation" class="tab-content">
                    <div class="tool-container">
                        <h2>🎥 视频生成</h2>
                        <p class="description">根据描述生成高质量的视频内容</p>

                        <div class="form-group">
                            <label for="video-prompt">视频描述：</label>
                            <div class="prompt-input-container">
                                <textarea id="video-prompt" placeholder="请详细描述您想要生成的视频内容..." rows="4"></textarea>
                                <button id="enhance-video-prompt-btn" class="enhance-btn" title="优化视频描述">
                                    <span class="enhance-icon">🎥</span>
                                    <span class="enhance-text">优化</span>
                                </button>
                                <button class="translate-btn" onclick="autoTranslateTextarea('video-prompt')"
                                    title="智能翻译">
                                    <span class="translate-icon">🌐</span>
                                    <span class="translate-text">翻译</span>
                                </button>
                            </div>
                            <div id="video-prompt-preview" class="prompt-preview" style="display: none;">
                                <div class="preview-header">
                                    <span class="preview-title">优化后的视频描述：</span>
                                    <div class="preview-actions">
                                        <button id="apply-video-prompt-btn" class="apply-btn">使用优化版本</button>
                                        <button id="cancel-video-prompt-btn" class="cancel-btn">保持原版</button>
                                    </div>
                                </div>
                                <div id="video-prompt-enhanced" class="enhanced-prompt"></div>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="video-provider">AI服务商：</label>
                                <select id="video-provider">
                                    <option value="">自动选择</option>
                                    <option value="minimax">🔗 Minimax (MCP)</option>
                                    <option value="volcano">火山引擎</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="video-model">模型选择：</label>
                                <select id="video-model">
                                    <option value="MiniMax-Hailuo-02" selected>MiniMax Hailuo 02 (1080p原生，SOTA指令遵循)
                                    </option>
                                    <option value="T2V-01-Director">T2V-01-Director (文生视频导演版)</option>
                                    <option value="I2V-01-Director">I2V-01-Director (图生视频导演版)</option>
                                    <option value="I2V-01-live">I2V-01-live (卡通漫画风格增强)</option>
                                    <option value="S2V-01">S2V-01 (主体参考视频生成)</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="video-duration">视频时长：</label>
                                <select id="video-duration">
                                    <option value="6">6秒 (Hailuo-02)</option>
                                    <option value="10" selected>10秒 (Hailuo-02)</option>
                                    <option value="5">5秒 (其他模型)</option>
                                    <option value="15">15秒 (其他模型)</option>
                                    <option value="30">30秒 (其他模型)</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="video-resolution">分辨率：</label>
                                <select id="video-resolution">
                                    <option value="768P">768P (标清)</option>
                                    <option value="1080P" selected>1080P (高清)</option>
                                    <option value="720P">720P (标清)</option>
                                    <option value="1440P">1440P (2K)</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="video-aspect-ratio">宽高比：</label>
                                <select id="video-aspect-ratio">
                                    <option value="16:9" selected>16:9 (横屏)</option>
                                    <option value="9:16">9:16 (竖屏)</option>
                                    <option value="1:1">1:1 (正方形)</option>
                                    <option value="4:3">4:3 (标准)</option>
                                    <option value="21:9">21:9 (超宽屏)</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label>
                                    <input type="checkbox" id="video-async-mode" checked>
                                    异步模式
                                </label>
                                <small class="help-text">启用异步模式可以避免长时间等待，生成完成后会通知</small>
                            </div>
                        </div>

                        <!-- 首帧图片上传 -->
                        <div class="form-section">
                            <h3>🖼️ 首帧图片</h3>

                            <div class="form-group">
                                <label for="video-first-frame-upload">首帧参考图片：</label>
                                <input type="file" id="video-first-frame-upload" accept="image/*">
                                <small class="help-text">上传首帧图片用于I2V模型，将基于此图片生成视频</small>
                            </div>

                            <div id="video-first-frame-preview" class="reference-preview" style="display: none;">
                                <h4>首帧预览：</h4>
                                <div class="preview-single" id="video-frame-preview"></div>
                                <button type="button" id="clear-video-first-frame" class="secondary-btn">清除首帧图片</button>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="video-frame-strength">首帧影响强度：<span
                                            id="video-frame-strength-value">0.8</span></label>
                                    <input type="range" id="video-frame-strength" min="0.1" max="1.0" step="0.1"
                                        value="0.8">
                                    <div class="range-labels">
                                        <span>弱 (0.1)</span>
                                        <span>中等 (0.8)</span>
                                        <span>强 (1.0)</span>
                                    </div>
                                    <small class="help-text">控制首帧图片对视频生成的影响程度</small>
                                </div>

                                <div class="form-group">
                                    <label for="video-motion-intensity">运动强度：<span
                                            id="video-motion-intensity-value">0.5</span></label>
                                    <input type="range" id="video-motion-intensity" min="0.1" max="1.0" step="0.1"
                                        value="0.5">
                                    <div class="range-labels">
                                        <span>静态 (0.1)</span>
                                        <span>中等 (0.5)</span>
                                        <span>动感 (1.0)</span>
                                    </div>
                                    <small class="help-text">控制视频中的运动幅度和变化程度</small>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="video-seed">随机种子：</label>
                                    <input type="number" id="video-seed" placeholder="留空为随机" min="0" max="4294967295">
                                    <small class="help-text">使用相同种子可以生成相似的视频</small>
                                </div>

                                <div class="form-group">
                                    <label for="video-style">视频风格：</label>
                                    <select id="video-style">
                                        <option value="">默认</option>
                                        <option value="cinematic">电影风格</option>
                                        <option value="anime">动漫风格</option>
                                        <option value="realistic">写实风格</option>
                                        <option value="artistic">艺术风格</option>
                                        <option value="documentary">纪录片风格</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button id="generate-video-btn" class="primary-btn">
                                <span class="btn-text">开始生成</span>
                                <span class="btn-loading" style="display: none;">生成中...</span>
                            </button>
                            <button id="clear-video-btn" class="secondary-btn">清空</button>
                        </div>

                        <div id="video-result" class="result-container" style="display: none;">
                            <h3>生成结果</h3>
                            <div id="video-content"></div>
                        </div>
                    </div>
                </div>

                <!-- 图像生成 -->
                <div id="image-generation" class="tab-content">
                    <div class="tool-container">
                        <h2>🎨 图像生成</h2>
                        <p class="description">根据描述生成高质量的图像内容</p>

                        <div class="form-group">
                            <label for="image-prompt">图像描述：</label>
                            <div class="prompt-input-container">
                                <textarea id="image-prompt" placeholder="请详细描述您想要生成的图像内容..." rows="4"></textarea>
                                <button id="enhance-image-prompt-btn" class="enhance-btn" title="优化图像描述">
                                    <span class="enhance-icon">🎨</span>
                                    <span class="enhance-text">优化</span>
                                </button>
                                <button class="translate-btn" onclick="autoTranslateTextarea('image-prompt')"
                                    title="智能翻译">
                                    <span class="translate-icon">🌐</span>
                                    <span class="translate-text">翻译</span>
                                </button>
                            </div>
                            <div id="image-prompt-preview" class="prompt-preview" style="display: none;">
                                <div class="preview-header">
                                    <span class="preview-title">优化后的图像描述：</span>
                                    <div class="preview-actions">
                                        <button id="apply-image-prompt-btn" class="apply-btn">使用优化版本</button>
                                        <button id="cancel-image-prompt-btn" class="cancel-btn">保持原版</button>
                                    </div>
                                </div>
                                <div id="image-prompt-enhanced" class="enhanced-prompt"></div>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="image-provider">AI服务商：</label>
                                <select id="image-provider">
                                    <option value="">自动选择</option>
                                    <option value="minimax">🔗 Minimax (MCP)</option>
                                    <option value="volcano">火山引擎</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="image-model">模型选择：</label>
                                <select id="image-model">
                                    <option value="image-01">image-01 (标准画面细腻)</option>
                                    <option value="image-01-live">image-01-live (手绘卡通增强)</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="image-aspect-ratio">宽高比：</label>
                                <select id="image-aspect-ratio">
                                    <option value="1:1">1:1 (正方形)</option>
                                    <option value="16:9">16:9 (宽屏)</option>
                                    <option value="4:3">4:3 (标准)</option>
                                    <option value="3:2">3:2 (相机)</option>
                                    <option value="2:3">2:3 (竖版相机)</option>
                                    <option value="3:4">3:4 (竖版标准)</option>
                                    <option value="9:16">9:16 (竖屏)</option>
                                    <option value="21:9">21:9 (超宽屏)</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="image-count">生成数量：</label>
                                <select id="image-count">
                                    <option value="1" selected>1张</option>
                                    <option value="2">2张</option>
                                    <option value="3">3张</option>
                                    <option value="4">4张</option>
                                    <option value="6">6张</option>
                                    <option value="9">9张</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-row">


                            <div class="form-group">
                                <label for="image-style">图像风格：</label>
                                <select id="image-style">
                                    <option value="">默认</option>
                                    <option value="realistic">写实风格</option>
                                    <option value="anime">动漫风格</option>
                                    <option value="cartoon">卡通风格</option>
                                    <option value="oil_painting">油画风格</option>
                                    <option value="watercolor">水彩风格</option>
                                    <option value="sketch">素描风格</option>
                                </select>
                            </div>
                        </div>

                        <!-- 参考图片上传 -->
                        <div class="form-section">
                            <h3>📸 参考图片</h3>

                            <div class="form-group">
                                <label for="image-reference-upload">角色参考图片：</label>
                                <input type="file" id="image-reference-upload" accept="image/*" multiple>
                                <small class="help-text">上传参考图片以生成相似风格或角色的图像（支持多张图片）</small>
                            </div>

                            <div id="image-reference-preview" class="reference-preview" style="display: none;">
                                <h4>已上传的参考图片：</h4>
                                <div class="preview-grid" id="image-preview-grid"></div>
                                <button type="button" id="clear-image-references"
                                    class="secondary-btn">清除所有参考图片</button>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="image-reference-strength">参考强度：<span
                                            id="image-reference-strength-value">0.7</span></label>
                                    <input type="range" id="image-reference-strength" min="0.1" max="1.0" step="0.1"
                                        value="0.7">
                                    <div class="range-labels">
                                        <span>弱 (0.1)</span>
                                        <span>中等 (0.7)</span>
                                        <span>强 (1.0)</span>
                                    </div>
                                    <small class="help-text">控制参考图片对生成结果的影响程度</small>
                                </div>

                                <div class="form-group">
                                    <label for="image-seed">随机种子：</label>
                                    <input type="number" id="image-seed" placeholder="留空为随机" min="0" max="4294967295">
                                    <small class="help-text">使用相同种子可以生成相似的图像</small>
                                </div>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button id="generate-image-btn" class="primary-btn">
                                <span class="btn-text">开始生成</span>
                                <span class="btn-loading" style="display: none;">生成中...</span>
                            </button>
                            <button id="clear-image-btn" class="secondary-btn">清空</button>
                        </div>

                        <div id="image-result" class="result-container" style="display: none;">
                            <h3>生成结果</h3>
                            <div id="image-content"></div>
                            <div class="result-actions">
                                <button id="download-image-btn" class="secondary-btn">下载图像</button>
                                <button id="save-image-btn" class="secondary-btn">保存到项目</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 媒体库 -->
                <div id="media-library" class="tab-content">
                    <!-- 媒体库内容将通过JavaScript动态生成 -->
                </div>

                <!-- DaVinci集成 -->
                <div id="davinci-integration" class="tab-content">
                    <div class="tool-container">
                        <h2>🎬 DaVinci集成</h2>
                        <p class="description">与DaVinci Resolve进行深度集成，实现自动化视频制作</p>

                        <div class="card">
                            <h3>🔗 连接状态</h3>
                            <div id="davinci-status">
                                <div class="loading">检查连接中...</div>
                            </div>
                            <div class="form-actions">
                                <button id="connect-davinci-btn" class="primary-btn">连接DaVinci</button>
                                <button id="refresh-davinci-btn" class="secondary-btn">刷新状态</button>
                            </div>
                        </div>

                        <!-- 项目管理功能已移除 - 不是核心功能，且API不稳定 -->

                        <div class="card">
                            <h3>🎞️ 媒体管理</h3>
                            <div class="form-group">
                                <label for="media-import">导入媒体文件：</label>
                                <input type="file" id="media-import" multiple accept="video/*,audio/*,image/*">
                            </div>
                            <div class="form-actions">
                                <button id="import-media-btn" class="primary-btn">导入媒体</button>
                                <button id="organize-media-btn" class="secondary-btn">整理媒体</button>
                            </div>
                        </div>

                        <div class="card">
                            <h3>✂️ 自动剪辑</h3>
                            <div class="form-group">
                                <label for="auto-edit-script">剪辑脚本：</label>
                                <textarea id="auto-edit-script" placeholder="输入自动剪辑指令..." rows="4"></textarea>
                            </div>
                            <div class="form-actions">
                                <button id="auto-edit-btn" class="primary-btn">开始自动剪辑</button>
                                <button id="preview-edit-btn" class="secondary-btn">预览效果</button>
                            </div>
                        </div>

                        <!-- 静帧捕获功能 -->
                        <div class="card">
                            <h3>📸 静帧捕获</h3>
                            <div id="frame-capture-status">
                                <div class="loading">检查静帧功能状态...</div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="frame-export-format">导出格式：</label>
                                    <select id="frame-export-format">
                                        <option value="PNG" selected>PNG (推荐)</option>
                                        <option value="JPEG">JPEG</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="frame-export-quality">导出质量：</label>
                                    <select id="frame-export-quality">
                                        <option value="high" selected>高质量</option>
                                        <option value="medium">中等质量</option>
                                        <option value="low">低质量</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="frame-output-dir">输出目录：</label>
                                <input type="text" id="frame-output-dir" placeholder="留空使用默认目录" value="">
                            </div>
                            <div class="form-actions">
                                <button id="capture-frame-btn" class="primary-btn">捕获当前帧</button>
                                <button id="export-for-ai-btn" class="secondary-btn">导出给AI使用</button>
                                <button id="refresh-playhead-btn" class="secondary-btn">刷新播放头信息</button>
                            </div>
                            <div id="playhead-info" class="info-display" style="display: none;">
                                <h4>播放头信息</h4>
                                <div class="info-grid">
                                    <div class="info-item">
                                        <span class="info-label">当前帧:</span>
                                        <span class="info-value" id="current-frame">-</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">时间码:</span>
                                        <span class="info-value" id="current-timecode">-</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">时间线长度:</span>
                                        <span class="info-value" id="timeline-duration">-</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- AI深度集成功能 -->
                        <div class="card">
                            <h3>🤖 AI深度集成</h3>
                            <div class="ai-integration-tabs">
                                <button class="ai-tab-btn active" data-ai-tab="content-analysis">内容分析</button>
                                <button class="ai-tab-btn" data-ai-tab="subtitle-generation">字幕生成</button>
                                <button class="ai-tab-btn" data-ai-tab="audio-extraction">音频提取</button>
                                <button class="ai-tab-btn" data-ai-tab="smart-markers">智能标记</button>
                            </div>

                            <!-- 内容分析 -->
                            <div id="content-analysis" class="ai-tab-content active">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="analysis-type">分析类型：</label>
                                        <select id="analysis-type">
                                            <option value="comprehensive">综合分析</option>
                                            <option value="scene_detection">场景检测</option>
                                            <option value="object_detection">物体识别</option>
                                            <option value="emotion_analysis">情感分析</option>
                                            <option value="quality_check">质量检查</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label for="confidence-threshold">置信度阈值：<span
                                                id="confidence-value">0.7</span></label>
                                        <input type="range" id="confidence-threshold" min="0.1" max="1.0" step="0.1"
                                            value="0.7">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label>
                                        <input type="checkbox" id="auto-apply-analysis" checked>
                                        自动应用分析结果
                                    </label>
                                </div>
                                <div class="form-actions">
                                    <button id="analyze-content-btn" class="primary-btn">开始分析</button>
                                </div>
                            </div>

                            <!-- 字幕生成 -->
                            <div id="subtitle-generation" class="ai-tab-content">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="subtitle-track">音轨选择：</label>
                                        <select id="subtitle-track">
                                            <option value="1" selected>音轨 1</option>
                                            <option value="2">音轨 2</option>
                                            <option value="3">音轨 3</option>
                                            <option value="4">音轨 4</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label for="subtitle-language">目标语言：</label>
                                        <select id="subtitle-language">
                                            <option value="zh-CN" selected>中文 (简体)</option>
                                            <option value="en-US">英语</option>
                                            <option value="ja-JP">日语</option>
                                            <option value="ko-KR">韩语</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="subtitle-ai-service">AI服务：</label>
                                    <select id="subtitle-ai-service">
                                        <option value="deepseek" selected>DeepSeek</option>
                                        <option value="openai">OpenAI</option>
                                        <option value="azure">Azure</option>
                                    </select>
                                </div>
                                <div class="form-actions">
                                    <button id="generate-subtitles-btn" class="primary-btn">生成字幕</button>
                                </div>
                            </div>

                            <!-- 音频提取 -->
                            <div id="audio-extraction" class="ai-tab-content">
                                <div class="form-group">
                                    <label for="audio-track-index">音轨选择：</label>
                                    <select id="audio-track-index">
                                        <option value="1" selected>音轨 1</option>
                                        <option value="2">音轨 2</option>
                                        <option value="3">音轨 3</option>
                                        <option value="4">音轨 4</option>
                                    </select>
                                </div>
                                <div class="form-actions">
                                    <button id="extract-audio-btn" class="primary-btn">提取音频</button>
                                </div>
                            </div>

                            <!-- 智能标记 -->
                            <div id="smart-markers" class="ai-tab-content">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="marker-type">标记类型：</label>
                                        <select id="marker-type">
                                            <option value="content">内容标记</option>
                                            <option value="scene_change">场景切换</option>
                                            <option value="quality_issue">质量问题</option>
                                            <option value="person">人物识别</option>
                                            <option value="object">物体识别</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label for="marker-confidence">置信度：<span
                                                id="marker-confidence-value">0.8</span></label>
                                        <input type="range" id="marker-confidence" min="0.5" max="1.0" step="0.1"
                                            value="0.8">
                                    </div>
                                </div>
                                <div class="form-actions">
                                    <button id="add-smart-markers-btn" class="primary-btn">添加智能标记</button>
                                </div>
                            </div>
                        </div>

                        <!-- 渲染管理 -->
                        <div class="card">
                            <h3>🎬 渲染管理</h3>
                            <div id="render-status">
                                <div class="loading">检查渲染状态...</div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="export-path">导出路径：</label>
                                    <input type="text" id="export-path" placeholder="输入导出路径">
                                </div>
                                <div class="form-group">
                                    <label for="export-filename">文件名：</label>
                                    <input type="text" id="export-filename" placeholder="输入文件名">
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="export-format">导出格式：</label>
                                    <select id="export-format">
                                        <option value="mp4" selected>MP4</option>
                                        <option value="mov">MOV</option>
                                        <option value="avi">AVI</option>
                                        <option value="mkv">MKV</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="export-quality">导出质量：</label>
                                    <select id="export-quality">
                                        <option value="high" selected>高质量</option>
                                        <option value="medium">中等质量</option>
                                        <option value="low">低质量</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-actions">
                                <button id="start-render-btn" class="primary-btn">开始渲染</button>
                                <button id="check-render-status-btn" class="secondary-btn">检查状态</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 设置 -->
                <div id="settings" class="tab-content">
                    <div class="tool-container">
                        <h2>⚙️ 设置</h2>
                        <p class="description">配置AI服务和应用参数</p>

                        <div class="settings-section">
                            <h3>AI服务配置</h3>
                            <div class="form-group">
                                <label for="deepseek-key">DeepSeek API Key：</label>
                                <input type="password" id="deepseek-key" placeholder="sk-...">
                            </div>
                            <div class="form-group">
                                <label for="minimax-key">Minimax API Key：</label>
                                <input type="password" id="minimax-key" placeholder="输入Minimax API Key">
                            </div>
                            <div class="form-group">
                                <label for="volcano-key">火山引擎 Access Key：</label>
                                <input type="password" id="volcano-key" placeholder="输入火山引擎Access Key">
                            </div>
                        </div>

                        <div class="settings-section">
                            <h3>应用设置</h3>
                            <div class="form-group">
                                <label for="theme-select">主题：</label>
                                <select id="theme-select">
                                    <option value="dark">深色主题</option>
                                    <option value="light">浅色主题</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="auto-refresh">自动刷新：</label>
                                <input type="checkbox" id="auto-refresh" checked>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button id="save-settings-btn" class="primary-btn">保存设置</button>
                            <button id="reset-settings-btn" class="secondary-btn">重置</button>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- 底部状态栏 -->
        <footer class="footer">
            <div class="container">
                <div class="footer-info">
                    <span>DaVinci AI Co-pilot PRO v0.1.0</span>
                    <span>|</span>
                    <span id="connection-status">WebSocket: 连接中...</span>
                </div>
                <div class="footer-links">
                    <a href="/docs" target="_blank">API文档</a>
                    <a href="/health" target="_blank">健康检查</a>
                </div>
            </div>
        </footer>
    </div>

    <!-- JavaScript -->
    <script src="/static/js/components.js"></script>
    <script src="/static/js/media-library.js"></script>
    <!-- 使用简化的统一AI客户端 -->
    <script src="/static/js/unified-ai-client.js"></script>
    <script src="/static/js/modules/speech-synthesis.js"></script>
    <script src="/static/js/app.js"></script>

    <!-- 加载状态样式 -->
    <style>
        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid var(--border-color);
            border-top: 4px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        .text-center {
            text-align: center;
        }

        .mt-3 {
            margin-top: 1rem;
        }

        /* 提示词增强样式 */
        .enhancement-comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin: 1rem 0;
        }

        .original-prompt,
        .enhanced-prompt {
            padding: 1rem;
            border-radius: 8px;
            border: 1px solid var(--border-color);
        }

        .original-prompt {
            background-color: var(--card-bg);
        }

        .enhanced-prompt {
            background-color: var(--success-bg, #f0f9ff);
            border-color: var(--success-color, #10b981);
        }

        .enhancement-comparison h4 {
            margin: 0 0 0.5rem 0;
            font-size: 0.9rem;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .enhancement-comparison div[id$="-content"] {
            font-size: 0.95rem;
            line-height: 1.5;
            color: var(--text-primary);
        }

        /* MCP服务标识 */
        .mcp-indicator {
            display: inline-block;
            background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-size: 0.7rem;
            padding: 2px 6px;
            border-radius: 4px;
            margin-left: 4px;
            font-weight: 500;
        }

        /* 服务状态指示器 */
        .service-status {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 6px;
        }

        .service-status.available {
            background-color: #10b981;
        }

        .service-status.unavailable {
            background-color: #ef4444;
        }

        .service-status.loading {
            background-color: #f59e0b;
            animation: pulse 1.5s ease-in-out infinite;
        }

        @keyframes pulse {

            0%,
            100% {
                opacity: 1;
            }

            50% {
                opacity: 0.5;
            }
        }

        /* 服务项样式增强 */
        .service-item {
            margin-bottom: 0.75rem;
            padding: 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            background: var(--card-bg);
        }

        .service-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
        }

        .service-name {
            display: flex;
            align-items: center;
            font-weight: 500;
        }

        .service-details {
            display: flex;
            gap: 1rem;
            font-size: 0.85rem;
            color: var(--text-secondary);
        }

        .mcp-label {
            color: var(--primary-color);
            font-weight: 500;
        }

        /* 能力项样式 */
        .capability-item {
            display: flex;
            justify-content: space-between;
            padding: 0.5rem 0;
            border-bottom: 1px solid var(--border-color);
        }

        .capability-item:last-child {
            border-bottom: none;
        }

        .capability-name {
            font-weight: 500;
            color: var(--text-primary);
        }

        .capability-providers {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        /* 使用提示词模态框样式 */
        .use-prompt-options {
            text-align: center;
        }

        .option-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 0.5rem;
            margin-top: 1rem;
        }

        .option-btn {
            padding: 0.75rem 1rem;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            background: var(--card-bg);
            color: var(--text-primary);
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .option-btn:hover {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .enhancement-comparison {
                grid-template-columns: 1fr;
            }

            .option-buttons {
                grid-template-columns: 1fr;
            }
        }
    </style>
</body>

</html>