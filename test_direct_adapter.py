#!/usr/bin/env python3
"""
直接测试DirectServiceAdapter的MiniMax处理
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def test_direct_adapter():
    """测试DirectServiceAdapter"""
    print("🚀 测试DirectServiceAdapter的MiniMax处理")
    print("=" * 60)
    
    try:
        from src.services.direct_service_adapter import direct_service_adapter
        from src.services.simplified_types import ServiceRequest, ServiceCapability, ServiceProvider
        
        # 创建语音合成请求
        request = ServiceRequest(
            capability=ServiceCapability.SPEECH_SYNTHESIS,
            provider=ServiceProvider.MINIMAX,
            content="你好，这是DirectServiceAdapter测试。",
            parameters={
                "voice_id": "female-tianmei",
                "output_format": "mp3"
            }
        )
        
        print("📝 创建的请求:")
        print(f"  能力: {request.capability}")
        print(f"  提供商: {request.provider}")
        print(f"  内容: {request.content}")
        print(f"  参数: {request.parameters}")
        print()
        
        print("🔄 执行请求...")
        response = await direct_service_adapter.execute_request(request)
        
        print("📊 响应结果:")
        print(f"  成功: {response.success}")
        print(f"  数据: {response.data}")
        print(f"  错误: {response.error}")
        
        if response.success and response.data:
            audio_url = response.data.get('audio_url')
            print(f"🎵 提取的音频URL: {audio_url}")
            
            # 验证URL是否是有效的HTTP URL
            if audio_url and audio_url.startswith('http'):
                print("✅ 音频URL格式正确")
            else:
                print("❌ 音频URL格式不正确")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

async def main():
    """主函数"""
    await test_direct_adapter()
    print("\n" + "=" * 60)
    print("🏁 测试完成")

if __name__ == "__main__":
    asyncio.run(main())
