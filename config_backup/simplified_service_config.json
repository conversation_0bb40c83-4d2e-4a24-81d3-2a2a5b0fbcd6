{"description": "简化的服务配置 - 移除复杂映射，使用代码处理", "version": "2.0", "providers": {"deepseek": {"type": "mcp", "server_name": "deepseek", "display_name": "DeepSeek", "description": "DeepSeek AI服务", "capabilities": ["text_generation", "text_analysis", "translation", "chat"], "default_parameters": {"max_tokens": 2000, "temperature": 0.7}}, "minimax": {"type": "mcp", "server_name": "minimax", "display_name": "MiniMax", "description": "MiniMax多模态AI服务", "capabilities": ["text_generation", "speech_synthesis", "image_generation", "video_generation"], "default_parameters": {"voice_id": "male-qn-qingse"}}, "elevenlabs": {"type": "mcp", "server_name": "elevenlabs", "display_name": "ElevenLabs", "description": "ElevenLabs语音合成服务", "capabilities": ["speech_synthesis", "voice_cloning"], "default_parameters": {"model_id": "eleven_multilingual_v2", "stability": 0.5, "similarity_boost": 0.5}}, "doubao": {"type": "mcp", "server_name": "do<PERSON>o", "display_name": "豆包", "description": "字节跳动豆包AI服务", "capabilities": ["image_generation", "video_generation"], "default_parameters": {}}, "vidu": {"type": "mcp", "server_name": "vidu", "display_name": "Vidu", "description": "Vidu视频生成服务", "capabilities": ["video_generation"], "default_parameters": {}}}, "capabilities": {"text_generation": {"display_name": "文本生成", "description": "生成文本内容", "common_parameters": ["max_tokens", "temperature", "top_p"]}, "text_analysis": {"display_name": "文本分析", "description": "分析文本内容或优化提示词", "common_parameters": ["task", "target_type", "max_tokens"]}, "translation": {"display_name": "翻译", "description": "文本翻译服务", "common_parameters": ["source_language", "target_language"]}, "speech_synthesis": {"display_name": "语音合成", "description": "文本转语音", "common_parameters": ["voice_id", "model_id", "stability"]}, "image_generation": {"display_name": "图像生成", "description": "根据文本生成图像", "common_parameters": ["width", "height", "steps", "guidance_scale"]}, "video_generation": {"display_name": "视频生成", "description": "根据文本或图像生成视频", "common_parameters": ["duration", "fps", "resolution"]}, "chat": {"display_name": "对话", "description": "智能对话服务", "common_parameters": ["max_tokens", "temperature", "context_length"]}, "voice_cloning": {"display_name": "声音克隆", "description": "克隆指定声音", "common_parameters": ["voice_sample", "quality"]}}, "notes": {"architecture": "简化的3层架构：Frontend → DirectServiceAdapter → MCP", "configuration": "参数映射和响应处理在代码中完成，不依赖复杂的JSON配置", "extensibility": "新增服务商只需在simplified_types.py中添加枚举和映射", "debugging": "每层都有清晰的日志边界，便于问题定位"}}