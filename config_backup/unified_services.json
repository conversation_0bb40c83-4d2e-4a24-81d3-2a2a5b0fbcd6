{"version": "3.0", "services": {"minimax": {"metadata": {"display_name": "MiniMax", "description": "MiniMax多模态AI服务", "version": "1.0.0", "homepage": "https://api.minimax.chat", "icon": "🎭", "tags": ["ai", "multimodal", "chinese"], "priority": 10}, "capabilities": {"speech_synthesis": {"enabled": true, "models": ["speech-01", "speech-02-hd"], "supported_formats": ["mp3", "wav"], "max_text_length": 1000, "languages": ["zh", "en"]}, "image_generation": {"enabled": true, "models": ["abab6.5-chat"], "supported_formats": ["png", "jpg"], "max_resolution": "1024x1024", "styles": ["realistic", "anime", "cartoon"]}, "video_generation": {"enabled": true, "models": ["video-01"], "supported_formats": ["mp4"], "max_duration": 10, "resolutions": ["720p", "1080p"]}}, "mcp_config": {"transport": "stdio", "command": "uvx", "args": ["minimax-mcp"], "env": {"MINIMAX_API_KEY": "${MINIMAX_API_KEY}", "MINIMAX_GROUP_ID": "${MINIMAX_GROUP_ID}", "MINIMAX_API_HOST": "${MINIMAX_API_HOST}", "MINIMAX_MCP_BASE_PATH": "${OUTPUT_DIR}"}, "timeout": 60, "retry_count": 3}, "api_config": {"base_url": "https://api.minimax.chat/v1", "api_key": "${MINIMAX_API_KEY}", "timeout": 60, "max_retries": 3}, "tools": {"text_to_audio": {"capability": "speech_synthesis", "parameters": {"text": {"type": "string", "required": true, "max_length": 1000}, "voice_id": {"type": "string", "required": true, "default": "male-qn-qingse"}, "model_id": {"type": "string", "required": false, "default": "speech-01"}, "output_format": {"type": "string", "required": false, "default": "mp3", "options": ["mp3", "wav"]}}}, "text_to_image": {"capability": "image_generation", "parameters": {"prompt": {"type": "string", "required": true, "max_length": 500}, "model": {"type": "string", "required": false, "default": "abab6.5-chat"}, "width": {"type": "integer", "required": false, "default": 1024, "min": 256, "max": 1024}, "height": {"type": "integer", "required": false, "default": 1024, "min": 256, "max": 1024}}}, "generate_video": {"capability": "video_generation", "parameters": {"prompt": {"type": "string", "required": true, "max_length": 500}, "duration": {"type": "integer", "required": false, "default": 5, "min": 1, "max": 10}, "resolution": {"type": "string", "required": false, "default": "1080p", "options": ["720p", "1080p"]}}}}, "ui_config": {"provider_selector": {"label": "🎭 MiniMax (MCP)", "description": "多模态AI服务，支持语音、图像、视频生成"}, "parameter_panels": {"speech_synthesis": {"title": "MiniMax语音合成参数", "groups": [{"title": "基础设置", "parameters": [{"key": "voice_id", "label": "语音选择", "type": "select", "options_api": "/api/speech/voices?provider=minimax"}, {"key": "model_id", "label": "模型选择", "type": "select", "options": [{"value": "speech-01", "label": "标准版"}, {"value": "speech-02-hd", "label": "高清版"}]}]}]}, "image_generation": {"title": "MiniMax图像生成参数", "groups": [{"title": "图像设置", "parameters": [{"key": "width", "label": "宽度", "type": "range", "min": 256, "max": 1024, "step": 64, "default": 1024}, {"key": "height", "label": "高度", "type": "range", "min": 256, "max": 1024, "step": 64, "default": 1024}]}]}}}, "result_parsing": {"speech_synthesis": {"success_patterns": [{"pattern": "语音合成成功！音频文件:\\s*([^.]+\\.mp3)", "extract": "file_path", "priority": 10}, {"pattern": "Success.*Audio.*URL:\\s*(https?://[^\\s]+)", "extract": "url", "priority": 8}], "error_patterns": [{"pattern": "余额不足|insufficient balance", "type": "balance_error", "message": "账户余额不足，请充值后重试"}, {"pattern": "参数错误|invalid params", "type": "parameter_error", "message": "参数错误，请检查输入"}]}}, "media_integration": {"speech_synthesis": {"category": "ai_audio", "file_naming": "minimax_audio_{timestamp}_{voice_id}", "metadata_fields": ["voice_id", "model_id", "text_length"]}, "image_generation": {"category": "ai_image", "file_naming": "minimax_image_{timestamp}_{width}x{height}", "metadata_fields": ["model", "width", "height", "prompt_hash"]}}}, "elevenlabs": {"metadata": {"display_name": "ElevenLabs", "description": "ElevenLabs高质量语音合成服务", "version": "1.0.0", "homepage": "https://elevenlabs.io", "icon": "🎙️", "tags": ["voice", "tts", "cloning"], "priority": 9}, "capabilities": {"speech_synthesis": {"enabled": true, "models": ["eleven_multilingual_v2", "eleven_turbo_v2"], "supported_formats": ["mp3", "wav"], "max_text_length": 2500, "languages": ["en", "zh", "es", "fr", "de", "it", "pt", "pl", "tr", "ru", "nl", "cs", "ar", "hi", "ko", "ja"]}, "voice_cloning": {"enabled": true, "types": ["instant", "professional"], "max_samples": 25, "supported_formats": ["mp3", "wav", "m4a"]}}, "mcp_config": {"transport": "stdio", "command": "uvx", "args": ["elevenlabs-mcp"], "env": {"ELEVENLABS_API_KEY": "${ELEVENLABS_API_KEY}", "ELEVENLABS_MCP_BASE_PATH": "${OUTPUT_DIR}"}, "timeout": 60, "retry_count": 3}, "tools": {"text_to_speech": {"capability": "speech_synthesis", "parameters": {"text": {"type": "string", "required": true, "max_length": 2500}, "voice_id": {"type": "string", "required": true}, "model_id": {"type": "string", "required": false, "default": "eleven_multilingual_v2"}, "stability": {"type": "float", "required": false, "default": 0.5, "min": 0, "max": 1}, "similarity_boost": {"type": "float", "required": false, "default": 0.5, "min": 0, "max": 1}}}}, "ui_config": {"provider_selector": {"label": "🎙️ ElevenLabs (MCP)", "description": "高质量语音合成和克隆服务"}, "parameter_panels": {"speech_synthesis": {"title": "ElevenLabs语音合成参数", "groups": [{"title": "语音设置", "parameters": [{"key": "voice_id", "label": "语音选择", "type": "select", "options_api": "/api/speech/voices?provider=elevenlabs"}, {"key": "stability", "label": "稳定性", "type": "range", "min": 0, "max": 1, "step": 0.1, "default": 0.5}, {"key": "similarity_boost", "label": "相似度增强", "type": "range", "min": 0, "max": 1, "step": 0.1, "default": 0.5}]}]}}}}, "doubao": {"metadata": {"display_name": "豆包", "description": "字节跳动豆包AI对话服务", "version": "1.0.0", "homepage": "https://console.volcengine.com/ark/", "icon": "🫘", "tags": ["text", "chat", "chinese"], "priority": 8}, "capabilities": {"text_generation": {"enabled": true, "models": ["ep-20241226140932-xxxxx"], "max_tokens": 2000, "languages": ["zh", "en"]}, "chat": {"enabled": true, "models": ["ep-20241226140932-xxxxx"], "max_tokens": 2000, "context_length": 4000}}, "mcp_config": {"transport": "stdio", "command": "uvx", "args": ["doubao-mcp-server"], "env": {"DOUBAO_API_KEY": "${DOUBAO_API_KEY}", "DOUBAO_BASE_URL": "${DOUBAO_BASE_URL}", "DOUBAO_MODEL": "${DOUBAO_MODEL}"}, "timeout": 30, "retry_count": 3}, "tools": {"chat": {"capability": "text_generation", "parameters": {"message": {"type": "string", "required": true, "max_length": 2000}, "max_tokens": {"type": "integer", "required": false, "default": 500, "min": 100, "max": 2000}, "temperature": {"type": "float", "required": false, "default": 0.7, "min": 0, "max": 1}}}}, "ui_config": {"provider_selector": {"label": "🫘 豆包 (MCP)", "description": "字节跳动豆包AI对话服务"}, "parameter_panels": {"text_generation": {"title": "豆包文本生成参数", "groups": [{"title": "生成设置", "parameters": [{"key": "max_tokens", "label": "最大输出长度", "type": "range", "min": 100, "max": 2000, "step": 100, "default": 500}, {"key": "temperature", "label": "创造性", "type": "range", "min": 0, "max": 1, "step": 0.1, "default": 0.7}]}]}}}}, "vidu": {"metadata": {"display_name": "Vidu", "description": "Vidu高质量视频生成服务", "version": "1.0.0", "homepage": "https://platform.vidu.cn/", "icon": "🎬", "tags": ["video", "generation", "creative"], "priority": 7}, "capabilities": {"video_generation": {"enabled": true, "models": ["vidu-1.0", "vidu-1.5"], "supported_formats": ["mp4"], "max_duration": 10, "resolutions": ["720p", "1080p", "4k"]}}, "mcp_config": {"transport": "stdio", "command": "uvx", "args": ["vidu-mcp"], "env": {"VIDU_API_KEY": "${VIDU_API_KEY}", "VIDU_BASE_URL": "${VIDU_BASE_URL}", "VIDU_OUTPUT_DIR": "${OUTPUT_DIR}"}, "timeout": 120, "retry_count": 2}, "tools": {"generate_video": {"capability": "video_generation", "parameters": {"prompt": {"type": "string", "required": true, "max_length": 500}, "duration": {"type": "integer", "required": false, "default": 5, "min": 1, "max": 10}, "resolution": {"type": "string", "required": false, "default": "1080p", "options": ["720p", "1080p", "4k"]}}}}, "ui_config": {"provider_selector": {"label": "🎬 <PERSON><PERSON><PERSON> (MCP)", "description": "高质量视频生成服务"}, "parameter_panels": {"video_generation": {"title": "Vidu视频生成参数", "groups": [{"title": "视频设置", "parameters": [{"key": "duration", "label": "视频时长 (秒)", "type": "range", "min": 1, "max": 10, "step": 1, "default": 5}, {"key": "resolution", "label": "分辨率", "type": "select", "options": [{"value": "720p", "label": "720p"}, {"value": "1080p", "label": "1080p"}, {"value": "4k", "label": "4K"}]}]}]}}}}}, "global_settings": {"default_timeout": 30, "max_concurrent_connections": 5, "output_directory": "./output", "log_level": "INFO", "auto_discovery": true, "health_check_enabled": true, "circuit_breaker": {"enabled": true, "failure_threshold": 5, "recovery_timeout": 300}, "load_balancer": {"strategy": "smart", "weights": {}}}, "environment_mapping": {"MINIMAX_API_KEY": "ai_services.minimax.api_key", "MINIMAX_GROUP_ID": "ai_services.minimax.group_id", "MINIMAX_API_HOST": "ai_services.minimax.api_host", "ELEVENLABS_API_KEY": "ai_services.elevenlabs.api_key", "DOUBAO_API_KEY": "ai_services.doubao.api_key", "DOUBAO_BASE_URL": "ai_services.doubao.base_url", "DOUBAO_MODEL": "ai_services.doubao.model", "VIDU_API_KEY": "ai_services.vidu.api_key", "VIDU_BASE_URL": "ai_services.vidu.base_url", "OUTPUT_DIR": "storage.output_dir"}}