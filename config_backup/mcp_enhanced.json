{"version": "2.0", "mcpServers": {"minimax": {"provider": "minimax", "transport": "stdio", "command": "uvx", "args": ["minimax-mcp"], "env": {"MINIMAX_API_KEY": "${MINIMAX_API_KEY}", "MINIMAX_GROUP_ID": "${MINIMAX_GROUP_ID}", "MINIMAX_API_HOST": "https://api.minimax.chat", "MINIMAX_MCP_BASE_PATH": "${OUTPUT_DIR}", "MINIMAX_API_RESOURCE_MODE": "url"}, "capabilities": ["speech_synthesis", "voice_cloning", "image_generation", "video_generation"], "tools": {"text_to_audio": "speech_synthesis", "text_to_image": "image_generation", "generate_video": "video_generation", "list_voices": "utility", "voice_clone": "voice_cloning", "music_generation": "audio_generation", "voice_design": "speech_synthesis"}, "enabled": true, "timeout": 60, "retry_count": 3, "health_check_interval": 300, "metadata": {"display_name": "MiniMax AI", "description": "MiniMax多模态AI服务", "version": "1.0.0", "tags": ["ai", "multimodal", "chinese"]}}, "elevenlabs": {"provider": "elevenlabs", "transport": "stdio", "command": "uvx", "args": ["elevenlabs-mcp"], "env": {"ELEVENLABS_API_KEY": "${ELEVENLABS_API_KEY}", "ELEVENLABS_MCP_BASE_PATH": "${OUTPUT_DIR}"}, "capabilities": ["speech_synthesis", "voice_cloning", "speech_to_text", "voice_conversion"], "tools": {"text_to_speech": "speech_synthesis", "voice_clone": "voice_cloning", "speech_to_text": "text_analysis", "voice_conversion": "speech_synthesis", "list_voices": "utility", "search_voices": "utility", "search_voice_library": "utility"}, "enabled": true, "timeout": 60, "retry_count": 3, "health_check_interval": 300, "metadata": {"display_name": "ElevenLabs", "description": "ElevenLabs语音合成和克隆服务", "version": "1.0.0", "tags": ["voice", "tts", "cloning"]}}, "doubao": {"provider": "do<PERSON>o", "transport": "stdio", "command": "uvx", "args": ["doubao-mcp-server"], "env": {"DOUBAO_API_KEY": "${DOUBAO_API_KEY}"}, "capabilities": ["image_generation", "video_generation", "multimodal"], "tools": {"text_to_image": "image_generation", "image_to_video": "video_generation", "text_to_video": "video_generation", "encode_image_to_base64": "image_processing"}, "enabled": true, "timeout": 30, "retry_count": 3, "health_check_interval": 300, "metadata": {"display_name": "豆包", "description": "字节跳动豆包AI多模态生成服务，支持文生图、文生视频、图生视频", "version": "1.0.0", "homepage": "https://github.com/wwwzhouhui/doubao_mcp_server", "tags": ["image", "video", "multimodal", "generation"]}}, "deepseek": {"provider": "deepseek", "transport": "stdio", "command": "npx", "args": ["-y", "deepseek-mcp-server"], "env": {"DEEPSEEK_API_KEY": "${DEEPSEEK_API_KEY}"}, "capabilities": ["text_generation", "chat", "text_analysis", "translation"], "tools": {"chat_completion": "text_generation", "multi_turn_chat": "chat"}, "enabled": true, "timeout": 60, "retry_count": 3, "health_check_interval": 300, "metadata": {"display_name": "DeepSeek", "description": "DeepSeek AI文本生成和对话服务，默认使用V3模型", "version": "0.2.1", "homepage": "https://github.com/DMontgomery40/deepseek-mcp-server", "tags": ["text", "chat", "v3", "reasoning"]}}, "vidu": {"provider": "vidu", "transport": "stdio", "command": "uvx", "args": ["vidu-mcp"], "env": {"VIDU_API_KEY": "${VIDU_API_KEY}", "VIDU_API_HOST": "https://api.vidu.cn", "VIDU_OUTPUT_DIR": "${OUTPUT_DIR}"}, "capabilities": ["video_generation"], "tools": {"generate_video": "video_generation", "text_to_video": "video_generation", "image_to_video": "video_generation", "get_video_status": "utility"}, "enabled": true, "timeout": 120, "retry_count": 2, "health_check_interval": 600, "metadata": {"display_name": "Vidu", "description": "Vidu高质量视频生成服务", "version": "1.0.0", "homepage": "https://platform.vidu.cn/docs/mcp-stdio", "tags": ["video", "generation", "creative"]}}}, "global_settings": {"default_timeout": 30, "max_concurrent_connections": 5, "output_directory": "./output", "log_level": "INFO", "auto_discovery": true, "health_check_enabled": true, "circuit_breaker": {"enabled": true, "failure_threshold": 5, "recovery_timeout": 300}, "load_balancer": {"strategy": "smart", "weights": {}}, "comment": "环境变量现在直接从.env文件读取，无需映射"}, "service_discovery": {"enabled": true, "cache_ttl": 300, "auto_refresh": true, "capability_probing": true}, "compatibility_matrix": {"speech_synthesis": {"input_formats": ["text", "ssml"], "output_formats": ["mp3", "wav", "ogg"], "chaining": {"can_follow": ["text_generation", "text_analysis"], "can_precede": ["media_library"]}}, "video_generation": {"input_formats": ["text", "image"], "output_formats": ["mp4", "webm"], "chaining": {"can_follow": ["image_generation", "text_generation"], "can_precede": ["media_library"]}}}}