#!/bin/bash

# DaVinci AI Co-pilot PRO 开发环境设置脚本

echo "🚀 Setting up DaVinci AI Co-pilot PRO development environment..."

# 检查Python版本
echo "📋 Checking Python version..."
python3 --version

# 激活虚拟环境
echo "🔧 Activating virtual environment..."
if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" ]]; then
    # Windows
    source venv/Scripts/activate
else
    # macOS/Linux
    source venv/bin/activate
fi

# 升级pip
echo "⬆️ Upgrading pip..."
pip install --upgrade pip

# 安装依赖
echo "📦 Installing dependencies..."
pip install -r requirements.txt

# 创建配置文件
echo "⚙️ Setting up configuration..."
if [ ! -f "config/config.json" ]; then
    cp config/config.example.json config/config.json
    echo "✅ Created config/config.json from example"
    echo "⚠️  Please edit config/config.json and add your API keys"
else
    echo "✅ config/config.json already exists"
fi

# 创建必要的目录
echo "📁 Creating directories..."
mkdir -p temp output cache logs

# 设置权限（仅Unix系统）
if [[ "$OSTYPE" != "msys" && "$OSTYPE" != "win32" ]]; then
    chmod +x setup.sh
    chmod +x run.sh
fi

echo ""
echo "🎉 Setup completed successfully!"
echo ""
echo "📝 Next steps:"
echo "1. Edit config/config.json and add your API keys"
echo "2. Run: ./run.sh to start the application"
echo "3. Open http://127.0.0.1:8000 in your browser"
echo ""
echo "🔧 Development commands:"
echo "- Start development server: python src/main.py"
echo "- Run tests: pytest tests/"
echo "- Format code: black src/"
echo "- Check code: flake8 src/"
echo ""
