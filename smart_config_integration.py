#!/usr/bin/env python3
"""
智能配置整合评估
考虑服务映射关系的配置整合分析
"""

import json
import os
from typing import Dict, Any, Set

def load_json_config(file_path: str) -> Dict[str, Any]:
    """加载JSON配置文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"  ❌ 加载配置文件失败 {file_path}: {e}")
        return {}

def smart_coverage_analysis():
    """智能覆盖度分析"""
    print('🧠 智能配置覆盖度分析')
    print('=' * 50)

    # 加载配置文件
    legacy_config = load_json_config('config/config.json')
    unified_config = load_json_config('config/unified_services.json')

    # 获取传统服务列表
    legacy_services = set()
    if 'ai_services' in legacy_config:
        legacy_services = set(legacy_config['ai_services'].keys())

    # 获取统一服务列表
    unified_services = set()
    if 'services' in unified_config:
        unified_services = set(unified_config['services'].keys())

    # 获取服务映射关系
    service_mapping = {}
    if 'global_settings' in unified_config and 'legacy_service_mapping' in unified_config['global_settings']:
        service_mapping = unified_config['global_settings']['legacy_service_mapping']
        # 移除note字段
        service_mapping = {k: v for k, v in service_mapping.items() if k != 'note'}

    print(f'  📊 传统服务: {legacy_services}')
    print(f'  📊 统一服务: {unified_services}')
    print(f'  🔗 服务映射: {service_mapping}')

    # 计算智能覆盖度
    covered_services = set()
    uncovered_services = set()

    for legacy_service in legacy_services:
        if legacy_service in unified_services:
            # 直接覆盖
            covered_services.add(legacy_service)
            print(f'    ✅ {legacy_service}: 直接覆盖')
        elif legacy_service in service_mapping:
            # 通过映射覆盖
            mapped_service = service_mapping[legacy_service]
            if mapped_service in unified_services:
                covered_services.add(legacy_service)
                print(f'    ✅ {legacy_service}: 映射到 {mapped_service}')
            else:
                uncovered_services.add(legacy_service)
                print(f'    ❌ {legacy_service}: 映射到 {mapped_service}，但目标服务不存在')
        else:
            uncovered_services.add(legacy_service)
            print(f'    ❌ {legacy_service}: 无覆盖')

    # 计算覆盖率
    if legacy_services:
        coverage_rate = len(covered_services) / len(legacy_services)
        print(f'\n  📈 智能覆盖率: {coverage_rate:.1%} ({len(covered_services)}/{len(legacy_services)})')

        if coverage_rate >= 1.0:
            print('  🎉 完全覆盖！')
            return True, coverage_rate
        elif coverage_rate >= 0.8:
            print('  ✅ 覆盖度充分')
            return True, coverage_rate
        else:
            print('  ⚠️ 覆盖度不足')
            return False, coverage_rate
    else:
        print('  ❌ 没有传统服务需要覆盖')
        return True, 1.0

def test_service_functionality():
    """测试服务功能性"""
    print('\n🧪 服务功能性测试:')

    try:
        import requests

        # 测试服务发现
        response = requests.get('http://127.0.0.1:8000/api/services/capabilities', timeout=5)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                capabilities = data.get('data', {})
                print(f'  ✅ 服务发现正常: {len(capabilities)} 种能力')

                # 检查关键服务能力
                key_capabilities = ['speech_synthesis', 'image_generation', 'video_generation', 'text_generation']
                missing_capabilities = []

                for capability in key_capabilities:
                    if capability in capabilities and capabilities[capability]:
                        providers = capabilities[capability]
                        print(f'    ✅ {capability}: {len(providers)} 个提供商')
                    else:
                        missing_capabilities.append(capability)
                        print(f'    ❌ {capability}: 无提供商')

                return len(missing_capabilities) == 0
            else:
                print('  ❌ 服务发现API返回失败')
                return False
        else:
            print(f'  ❌ 服务发现API失败: {response.status_code}')
            return False

    except Exception as e:
        print(f'  ❌ 服务功能性测试失败: {str(e)}')
        return False

def evaluate_migration_readiness():
    """评估迁移准备度"""
    print('\n🎯 迁移准备度评估:')

    # 智能覆盖度分析
    coverage_ok, coverage_rate = smart_coverage_analysis()

    # 服务功能性测试
    functionality_ok = test_service_functionality()

    # 配置文件完整性检查
    print('\n  ⚙️ 配置文件完整性:')
    unified_config = load_json_config('config/unified_services.json')

    required_keys = ['version', 'services', 'global_settings', 'environment_mapping']
    missing_keys = [key for key in required_keys if key not in unified_config]

    if missing_keys:
        print(f'    ❌ 缺少必需键: {missing_keys}')
        config_complete = False
    else:
        print('    ✅ 配置结构完整')
        config_complete = True

    # 环境变量映射检查
    env_mapping = unified_config.get('environment_mapping', {})
    if len(env_mapping) >= 6:  # 至少6个服务的环境变量
        print(f'    ✅ 环境变量映射充分: {len(env_mapping)} 个映射')
        env_mapping_ok = True
    else:
        print(f'    ⚠️ 环境变量映射不足: {len(env_mapping)} 个映射')
        env_mapping_ok = False

    # 综合评估
    print(f'\n  📊 评估结果:')
    print(f'    - 服务覆盖度: {"✅" if coverage_ok else "❌"} {coverage_rate:.1%}')
    print(f'    - 功能性测试: {"✅" if functionality_ok else "❌"}')
    print(f'    - 配置完整性: {"✅" if config_complete else "❌"}')
    print(f'    - 环境变量映射: {"✅" if env_mapping_ok else "❌"}')

    # 计算总体准备度
    checks = [coverage_ok, functionality_ok, config_complete, env_mapping_ok]
    readiness_score = sum(checks) / len(checks)

    print(f'\n  🎯 总体准备度: {readiness_score:.1%}')

    if readiness_score >= 1.0:
        print('  🎉 完全准备就绪！')
        return 'ready'
    elif readiness_score >= 0.75:
        print('  ✅ 基本准备就绪')
        return 'mostly_ready'
    elif readiness_score >= 0.5:
        print('  ⚠️ 部分准备就绪')
        return 'partially_ready'
    else:
        print('  ❌ 准备不足')
        return 'not_ready'

def generate_action_plan(readiness_status: str):
    """生成行动计划"""
    print(f'\n📋 行动计划 ({readiness_status}):')

    if readiness_status == 'ready':
        print('  🚀 立即可执行的行动:')
        print('    1. ✅ 开始逐步停用旧配置文件')
        print('    2. ✅ 更新代码以优先使用统一配置')
        print('    3. ✅ 监控系统稳定性')
        print('    4. ✅ 完全移除旧配置依赖')

    elif readiness_status == 'mostly_ready':
        print('  ⚡ 近期可执行的行动:')
        print('    1. ✅ 完善剩余的配置项')
        print('    2. ✅ 进行更全面的功能测试')
        print('    3. ✅ 开始部分迁移')
        print('    4. ⚠️ 保留旧配置作为备份')

    elif readiness_status == 'partially_ready':
        print('  🔧 需要完成的工作:')
        print('    1. ⚠️ 提高服务覆盖度')
        print('    2. ⚠️ 修复功能性问题')
        print('    3. ⚠️ 完善配置结构')
        print('    4. ❌ 暂不建议迁移')

    else:
        print('  ❌ 必须完成的工作:')
        print('    1. ❌ 大幅改进配置覆盖度')
        print('    2. ❌ 解决功能性问题')
        print('    3. ❌ 重新设计配置结构')
        print('    4. ❌ 迁移不可行')

def main():
    """主函数"""
    print('🧠 DaVinci AI Co-pilot Pro 智能配置整合评估')
    print('=' * 60)

    # 评估迁移准备度
    readiness_status = evaluate_migration_readiness()

    # 生成行动计划
    generate_action_plan(readiness_status)

    print('\n' + '=' * 60)
    print('🎯 智能配置整合评估完成')

    if readiness_status in ['ready', 'mostly_ready']:
        print('\n🎉 好消息：配置整合基本可行！')
        print('  建议：可以开始执行迁移计划')
    else:
        print('\n⚠️ 需要更多工作才能完成配置整合')
        print('  建议：按照行动计划逐步改进')

if __name__ == '__main__':
    main()
