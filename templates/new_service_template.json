{"service_name": "new_service", "template": {"metadata": {"display_name": "新服务名称", "description": "服务描述", "version": "1.0.0", "homepage": "https://service-homepage.com", "icon": "🤖", "tags": ["ai", "service", "tag1", "tag2"], "priority": 5}, "capabilities": {"text_generation": {"enabled": true, "models": ["model-1", "model-2"], "max_tokens": 2000, "languages": ["zh", "en"]}, "image_generation": {"enabled": false, "models": ["image-model-1"], "supported_formats": ["png", "jpg"], "max_resolution": "1024x1024"}, "video_generation": {"enabled": false, "models": ["video-model-1"], "supported_formats": ["mp4"], "max_duration": 10}, "speech_synthesis": {"enabled": false, "models": ["speech-model-1"], "supported_formats": ["mp3", "wav"], "max_text_length": 1000}}, "mcp_config": {"transport": "stdio", "command": "uvx", "args": ["new-service-mcp"], "env": {"NEW_SERVICE_API_KEY": "${NEW_SERVICE_API_KEY}", "NEW_SERVICE_BASE_URL": "${NEW_SERVICE_BASE_URL}", "NEW_SERVICE_OUTPUT_DIR": "${OUTPUT_DIR}"}, "timeout": 60, "retry_count": 3}, "api_config": {"base_url": "https://api.newservice.com/v1", "api_key": "${NEW_SERVICE_API_KEY}", "timeout": 60, "max_retries": 3}, "tools": {"generate_text": {"capability": "text_generation", "parameters": {"prompt": {"type": "string", "required": true, "max_length": 500, "description": "生成提示词"}, "max_tokens": {"type": "integer", "required": false, "default": 500, "min": 100, "max": 2000, "description": "最大输出长度"}, "temperature": {"type": "float", "required": false, "default": 0.7, "min": 0, "max": 1, "description": "创造性参数"}}}, "generate_image": {"capability": "image_generation", "parameters": {"prompt": {"type": "string", "required": true, "max_length": 500, "description": "图像描述"}, "width": {"type": "integer", "required": false, "default": 1024, "min": 256, "max": 1024, "description": "图像宽度"}, "height": {"type": "integer", "required": false, "default": 1024, "min": 256, "max": 1024, "description": "图像高度"}, "style": {"type": "string", "required": false, "default": "realistic", "options": ["realistic", "anime", "cartoon", "abstract"], "description": "图像风格"}}}}, "ui_config": {"provider_selector": {"label": "🤖 新服务", "description": "新服务的详细描述"}, "parameter_panels": {"text_generation": {"title": "新服务文本生成参数", "groups": [{"title": "基础设置", "parameters": [{"key": "max_tokens", "label": "最大输出长度", "type": "range", "min": 100, "max": 2000, "step": 100, "default": 500, "description": "控制生成文本的最大长度"}, {"key": "temperature", "label": "创造性", "type": "range", "min": 0, "max": 1, "step": 0.1, "default": 0.7, "description": "控制输出的随机性和创造性"}]}]}, "image_generation": {"title": "新服务图像生成参数", "groups": [{"title": "图像设置", "parameters": [{"key": "width", "label": "宽度", "type": "range", "min": 256, "max": 1024, "step": 64, "default": 1024}, {"key": "height", "label": "高度", "type": "range", "min": 256, "max": 1024, "step": 64, "default": 1024}, {"key": "style", "label": "风格", "type": "select", "options": [{"value": "realistic", "label": "写实风格"}, {"value": "anime", "label": "动漫风格"}, {"value": "cartoon", "label": "卡通风格"}, {"value": "abstract", "label": "抽象风格"}], "default": "realistic"}]}]}}}, "result_parsing": {"text_generation": {"success_patterns": [{"pattern": "生成成功.*?结果:\\s*(.+)", "extract": "text", "priority": 10, "transform": "strip_quotes"}, {"pattern": "Success.*?Text:\\s*(.+)", "extract": "text", "priority": 8}], "error_patterns": [{"pattern": "余额不足|insufficient balance", "type": "balance_error", "message": "账户余额不足，请充值后重试", "priority": 10}, {"pattern": "参数错误|invalid params", "type": "parameter_error", "message": "参数错误，请检查输入", "priority": 8}]}, "image_generation": {"success_patterns": [{"pattern": "图像生成成功.*?文件:\\s*([^\\s]+\\.(png|jpg|jpeg))", "extract": "file_path", "priority": 10}, {"pattern": "Success.*?Image.*?URL:\\s*(https?://[^\\s]+)", "extract": "url", "priority": 8}], "error_patterns": [{"pattern": "生成失败|generation failed", "type": "generation_error", "message": "图像生成失败，请重试", "priority": 8}]}}, "media_integration": {"text_generation": {"category": "ai_text", "file_naming": "new_service_text_{timestamp}", "metadata_fields": ["max_tokens", "temperature", "model"], "auto_tags": ["text_generation", "ai_generated"]}, "image_generation": {"category": "ai_image", "file_naming": "new_service_image_{timestamp}_{width}x{height}", "metadata_fields": ["width", "height", "style", "model"], "auto_tags": ["image_generation", "ai_generated"], "thumbnail_generation": true}, "video_generation": {"category": "ai_video", "file_naming": "new_service_video_{timestamp}_{duration}s", "metadata_fields": ["duration", "resolution", "model"], "auto_tags": ["video_generation", "ai_generated"], "thumbnail_generation": true}, "speech_synthesis": {"category": "ai_audio", "file_naming": "new_service_audio_{timestamp}_{voice_id}", "metadata_fields": ["voice_id", "model", "text_length"], "auto_tags": ["speech_synthesis", "ai_generated"]}}, "error_handling": {"retry_config": {"max_retries": 3, "backoff_factor": 2.0, "backoff_type": "exponential"}, "circuit_breaker": {"enabled": true, "failure_threshold": 5, "recovery_timeout": 300}}, "monitoring": {"health_check": {"enabled": true, "interval": 300, "timeout": 30}, "metrics": {"enabled": true, "track_usage": true, "track_performance": true}}}, "environment_variables": {"NEW_SERVICE_API_KEY": "ai_services.new_service.api_key", "NEW_SERVICE_BASE_URL": "ai_services.new_service.base_url", "NEW_SERVICE_MODEL": "ai_services.new_service.model"}, "installation_notes": ["1. 安装MCP服务器: uvx install new-service-mcp", "2. 配置环境变量: NEW_SERVICE_API_KEY", "3. 更新config/config.json添加服务配置", "4. 重启DaVinci AI Co-pilot Pro服务"], "validation_checklist": ["✅ MCP服务器正确安装", "✅ API密钥配置正确", "✅ 服务在capabilities API中显示", "✅ 前端界面正确显示提供商选项", "✅ 参数控制面板正确生成", "✅ 测试请求成功处理", "✅ 结果解析正确工作", "✅ 媒体库自动集成正常"]}