#!/bin/bash

# DaVinci AI Co-pilot PRO 启动脚本

echo "🎬 Starting DaVinci AI Co-pilot PRO..."

# 激活虚拟环境
if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" ]]; then
    # Windows
    source venv/Scripts/activate
else
    # macOS/Linux
    source venv/bin/activate
fi

# 检查配置文件
if [ ! -f "config/config.json" ]; then
    echo "❌ Configuration file not found!"
    echo "Please run ./setup.sh first or copy config.example.json to config.json"
    exit 1
fi

# 创建日志目录
mkdir -p logs

# 启动应用
echo "🚀 Starting server on http://127.0.0.1:8000"
echo "Press Ctrl+C to stop the server"
echo ""

python src/main.py
