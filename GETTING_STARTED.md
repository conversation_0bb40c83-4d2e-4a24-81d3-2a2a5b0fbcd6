# 🎬 DaVinci AI Co-pilot Pro - 快速开始

## ⚡ 一键启动 (推荐)

```bash
# 1. 自动部署到DaVinci Resolve
python scripts/deploy_to_davinci.py

# 2. 一键启动所有服务 (包含自动清理)
chmod +x scripts/start_davinci_ai.sh
./scripts/start_davinci_ai.sh

### 🧹 自动清理功能

启动脚本现在包含自动清理功能，会在启动前自动：
- ✅ 清理Python缓存文件 (`__pycache__`, `*.pyc`, `*.pyo`)
- ✅ 清理系统临时文件 (`.DS_Store`, `Thumbs.db`, `*.tmp`)
- ✅ 重建运行时目录 (`logs`, `temp`, `cache`, `output`)
- ✅ 自动清理占用的端口和进程
- ✅ 删除旧的进程ID文件

## 📋 环境要求

- **DaVinci Resolve**: 18.x 或 19.x (必须支持Python API)
- **Python**: 3.8+ (推荐 3.9-3.11)
- **操作系统**: macOS, Windows, Linux

## 🎯 核心功能

### 📸 静帧捕获
- 高质量静帧导出 (PNG/JPEG)
- AI优化格式导出
- 实时播放头信息显示

### 🤖 AI深度集成
- **内容分析**: 综合分析、场景检测、物体识别、情感分析
- **智能字幕生成**: 多语言支持，多AI服务
- **音频提取**: 智能音频提取和处理
- **智能标记**: 自动识别场景切换和质量问题

### 🎬 项目管理
- 项目创建、加载、状态监控
- 批量处理多个项目
- 渲染管理和状态监控

## 🔧 故障排除

### 连接问题
```bash
# 运行连接测试
python scripts/test_connection.py

# 清理端口占用
./scripts/cleanup_ports.sh
```

### 常见错误
1. **"DaVinci Resolve Python API not available"**
   - 确认DaVinci Resolve版本 >= 18.0
   - 重新安装DaVinci Resolve并选择完整安装

2. **端口占用**
   - 运行: `lsof -ti :8000 | xargs kill -9`

3. **权限问题 (macOS)**
   - 系统偏好设置 > 安全性与隐私 > 完全磁盘访问权限
   - 添加: DaVinci Resolve.app

## 📚 详细文档

- **[完整部署指南](docs/DAVINCI_DEPLOYMENT_GUIDE.md)**
- **[生产环境总结](docs/PRODUCTION_DEPLOYMENT_SUMMARY.md)**
- **[快速参考](QUICK_REFERENCE.md)**

## 🎉 验证成功

启动成功后，您应该看到：
- ✅ Web界面: http://127.0.0.1:8000
- ✅ DaVinci连接状态: "已连接"
- ✅ 静帧功能: "功能可用"
- ✅ 当前项目信息正常显示

**🎬 开始您的AI辅助视频创作之旅！**
