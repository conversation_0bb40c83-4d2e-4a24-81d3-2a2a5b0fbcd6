#!/usr/bin/env python3
"""
配置调试测试脚本
用于调试MiniMax MCP服务器未启动的问题
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_env_vars():
    """测试环境变量加载"""
    print("🔍 测试环境变量加载...")
    
    # 手动加载.env文件
    from dotenv import load_dotenv
    env_file = project_root / ".env"
    if env_file.exists():
        load_dotenv(env_file)
        print(f"✅ 加载环境变量文件: {env_file}")
    else:
        print(f"❌ 环境变量文件不存在: {env_file}")
    
    # 检查关键环境变量
    required_vars = [
        'MINIMAX_API_KEY',
        'MINIMAX_GROUP_ID', 
        'MINIMAX_API_HOST',
        'OUTPUT_DIR'
    ]
    
    print("\n🔑 检查关键环境变量:")
    for var in required_vars:
        value = os.getenv(var)
        if value:
            # 对于敏感信息，只显示前后几位
            if 'KEY' in var or 'TOKEN' in var:
                display_value = f"{value[:10]}...{value[-4:]}" if len(value) > 14 else value
            else:
                display_value = value
            print(f"  ✅ {var} = {display_value}")
        else:
            print(f"  ❌ {var} = 未设置")

def test_unified_config():
    """测试统一配置管理器"""
    print("\n🔍 测试统一配置管理器...")
    
    try:
        from src.core.unified_config import config_manager
        
        # 测试MCP配置加载
        mcp_config = config_manager.get('mcp', {})
        print(f"✅ MCP配置加载成功: {bool(mcp_config)}")
        
        if mcp_config:
            servers = mcp_config.get('servers', {})
            print(f"  📡 MCP服务器数量: {len(servers)}")
            print(f"  📡 MCP服务器列表: {list(servers.keys())}")
            
            # 检查MiniMax配置
            minimax_config = servers.get('minimax', {})
            if minimax_config:
                print(f"  ✅ MiniMax配置存在")
                print(f"    - 启用状态: {minimax_config.get('enabled', False)}")
                print(f"    - 命令: {minimax_config.get('command')}")
                print(f"    - 参数: {minimax_config.get('args', [])}")
                
                env_config = minimax_config.get('env', {})
                print(f"    - 环境变量配置: {list(env_config.keys())}")
                
                # 检查环境变量解析
                for key, value in env_config.items():
                    if isinstance(value, str) and value.startswith('${') and value.endswith('}'):
                        env_var = value[2:-1]
                        resolved_value = os.getenv(env_var)
                        status = "✅" if resolved_value else "❌"
                        print(f"      {status} {key}: {value} -> {'已解析' if resolved_value else '未解析'}")
                    else:
                        print(f"      ✅ {key}: {value} (静态值)")
            else:
                print(f"  ❌ MiniMax配置不存在")
        
    except Exception as e:
        print(f"❌ 统一配置管理器测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_mcp_command():
    """测试MCP命令是否可执行"""
    print("\n🔍 测试MCP命令可执行性...")
    
    # 设置环境变量
    os.environ['MINIMAX_API_KEY'] = os.getenv('MINIMAX_API_KEY', '')
    os.environ['MINIMAX_API_HOST'] = os.getenv('MINIMAX_API_HOST', '')
    os.environ['MINIMAX_GROUP_ID'] = os.getenv('MINIMAX_GROUP_ID', '')
    
    import subprocess
    try:
        # 测试uvx命令
        result = subprocess.run(['which', 'uvx'], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"✅ uvx命令可用: {result.stdout.strip()}")
            
            # 测试MiniMax MCP服务器
            print("🔍 测试MiniMax MCP服务器启动...")
            result = subprocess.run(
                ['uvx', 'minimax-mcp', '--help'], 
                capture_output=True, 
                text=True, 
                timeout=30,
                env=os.environ
            )
            
            if result.returncode == 0:
                print("✅ MiniMax MCP服务器可以启动")
                print(f"输出: {result.stdout[:200]}...")
            else:
                print(f"❌ MiniMax MCP服务器启动失败")
                print(f"错误: {result.stderr}")
        else:
            print("❌ uvx命令不可用")
            
    except subprocess.TimeoutExpired:
        print("⏰ 命令执行超时")
    except Exception as e:
        print(f"❌ 命令测试失败: {e}")

def main():
    """主函数"""
    print("🚀 DaVinci AI Co-pilot Pro - MCP配置调试")
    print("=" * 50)
    
    test_env_vars()
    test_unified_config()
    test_mcp_command()
    
    print("\n" + "=" * 50)
    print("🏁 调试完成")

if __name__ == "__main__":
    main()
