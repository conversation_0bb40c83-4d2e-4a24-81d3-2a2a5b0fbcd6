#!/usr/bin/env python3
"""
配置文件整合脚本
评估和执行配置文件的整合迁移
"""

import json
import os
from pathlib import Path
from typing import Dict, Any

def load_json_config(file_path: str) -> Dict[str, Any]:
    """加载JSON配置文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"  ❌ 加载配置文件失败 {file_path}: {e}")
        return {}

def analyze_config_overlap():
    """分析配置文件重叠情况"""
    print('🔍 配置文件重叠分析')
    print('=' * 50)

    # 配置文件路径
    config_files = {
        'legacy_config': 'config/config.json',
        'mcp_config': 'config/mcp_enhanced.json',
        'unified_config': 'config/unified_services.json'
    }

    configs = {}
    for name, path in config_files.items():
        if os.path.exists(path):
            configs[name] = load_json_config(path)
            print(f'  ✅ {name}: {path} 已加载')
        else:
            print(f'  ❌ {name}: {path} 不存在')
            configs[name] = {}

    # 分析配置内容
    print('\n📊 配置内容分析:')

    # 分析legacy_config
    if configs['legacy_config']:
        legacy = configs['legacy_config']
        print(f'\n  📄 legacy_config (config.json):')
        print(f'    - 顶级键: {list(legacy.keys())}')

        if 'ai_services' in legacy:
            ai_services = legacy['ai_services']
            print(f'    - AI服务: {list(ai_services.keys())}')

            # 检查API密钥配置
            for service, config in ai_services.items():
                if isinstance(config, dict):
                    keys = [k for k in config.keys() if 'key' in k.lower()]
                    if keys:
                        print(f'      - {service}: {len(keys)} 个密钥配置')

    # 分析mcp_config
    if configs['mcp_config']:
        mcp = configs['mcp_config']
        print(f'\n  📄 mcp_config (mcp_enhanced.json):')
        print(f'    - 顶级键: {list(mcp.keys())}')

        if 'mcpServers' in mcp:
            servers = mcp['mcpServers']
            print(f'    - MCP服务器: {list(servers.keys())}')

            for server, config in servers.items():
                capabilities = config.get('capabilities', [])
                print(f'      - {server}: {len(capabilities)} 个能力')

    # 分析unified_config
    if configs['unified_config']:
        unified = configs['unified_config']
        print(f'\n  📄 unified_config (unified_services.json):')
        print(f'    - 顶级键: {list(unified.keys())}')

        if 'services' in unified:
            services = unified['services']
            print(f'    - 统一服务: {list(services.keys())}')

            for service, config in services.items():
                tools = config.get('tools', {})
                print(f'      - {service}: {len(tools)} 个工具')

    return configs

def evaluate_migration_feasibility(configs: Dict[str, Dict[str, Any]]):
    """评估迁移可行性"""
    print('\n🎯 迁移可行性评估:')

    legacy = configs.get('legacy_config', {})
    mcp = configs.get('mcp_config', {})
    unified = configs.get('unified_config', {})

    # 检查功能覆盖度
    print('\n  📋 功能覆盖度检查:')

    # 检查AI服务覆盖
    legacy_services = set()
    if 'ai_services' in legacy:
        legacy_services = set(legacy['ai_services'].keys())

    mcp_servers = set()
    if 'mcpServers' in mcp:
        mcp_servers = set(mcp['mcpServers'].keys())

    unified_services = set()
    if 'services' in unified:
        unified_services = set(unified['services'].keys())

    print(f'    - 传统服务: {legacy_services}')
    print(f'    - MCP服务器: {mcp_servers}')
    print(f'    - 统一服务: {unified_services}')

    # 检查覆盖情况
    if unified_services:
        legacy_covered = len(legacy_services & unified_services) / len(legacy_services) if legacy_services else 1
        mcp_covered = len(mcp_servers & unified_services) / len(mcp_servers) if mcp_servers else 1

        print(f'    - 传统服务覆盖度: {legacy_covered:.1%}')
        print(f'    - MCP服务覆盖度: {mcp_covered:.1%}')

        if legacy_covered >= 0.8 and mcp_covered >= 0.8:
            print('    ✅ 统一配置覆盖度充分')
            return True
        else:
            print('    ⚠️ 统一配置覆盖度不足')
            return False
    else:
        print('    ❌ 统一配置为空')
        return False

def create_backup_strategy():
    """创建备份策略"""
    print('\n💾 备份策略:')

    backup_dir = Path('config/backup')
    backup_dir.mkdir(exist_ok=True)

    config_files = [
        'config/config.json',
        'config/mcp_enhanced.json'
    ]

    backed_up = []
    for config_file in config_files:
        if os.path.exists(config_file):
            try:
                source = Path(config_file)
                backup_name = f"{source.stem}_backup_{source.suffix}"
                backup_path = backup_dir / backup_name

                # 复制到备份目录
                import shutil
                shutil.copy2(source, backup_path)
                backed_up.append(str(backup_path))
                print(f'  ✅ 备份: {config_file} -> {backup_path}')
            except Exception as e:
                print(f'  ❌ 备份失败 {config_file}: {e}')

    return backed_up

def test_unified_config_completeness():
    """测试统一配置的完整性"""
    print('\n🧪 统一配置完整性测试:')

    try:
        # 测试配置加载
        unified_config = load_json_config('config/unified_services.json')
        if not unified_config:
            print('  ❌ 统一配置文件加载失败')
            return False

        # 检查必需的顶级键
        required_keys = ['version', 'services', 'global_settings']
        missing_keys = [key for key in required_keys if key not in unified_config]

        if missing_keys:
            print(f'  ❌ 缺少必需键: {missing_keys}')
            return False
        else:
            print('  ✅ 所有必需键存在')

        # 检查服务配置完整性
        services = unified_config.get('services', {})
        if not services:
            print('  ❌ 没有配置任何服务')
            return False

        incomplete_services = []
        for service_name, service_config in services.items():
            required_service_keys = ['metadata', 'capabilities', 'tools', 'ui_config']
            missing_service_keys = [key for key in required_service_keys if key not in service_config]

            if missing_service_keys:
                incomplete_services.append(f"{service_name}: {missing_service_keys}")

        if incomplete_services:
            print(f'  ⚠️ 不完整的服务配置:')
            for incomplete in incomplete_services:
                print(f'    - {incomplete}')
        else:
            print(f'  ✅ 所有 {len(services)} 个服务配置完整')

        # 检查环境变量映射
        env_mapping = unified_config.get('environment_mapping', {})
        if env_mapping:
            print(f'  ✅ 环境变量映射: {len(env_mapping)} 个映射')
        else:
            print('  ⚠️ 没有环境变量映射')

        return len(incomplete_services) == 0

    except Exception as e:
        print(f'  ❌ 测试失败: {e}')
        return False

def generate_migration_plan(configs: Dict[str, Dict[str, Any]], is_feasible: bool):
    """生成迁移计划"""
    print('\n📋 迁移计划:')

    if is_feasible:
        print('  ✅ 迁移可行，建议执行以下步骤:')
        print('    1. 创建配置文件备份 ✅')
        print('    2. 验证统一配置完整性 ✅')
        print('    3. 测试新配置的功能性')
        print('    4. 逐步停用旧配置文件')
        print('    5. 监控系统稳定性')

        # 检查是否可以立即执行
        unified_complete = test_unified_config_completeness()
        if unified_complete:
            print('\n  🚀 可以立即执行迁移!')
            return 'ready'
        else:
            print('\n  ⚠️ 需要完善统一配置后再迁移')
            return 'needs_improvement'
    else:
        print('  ❌ 迁移不可行，需要先完善统一配置:')
        print('    1. 增加缺失的服务配置')
        print('    2. 完善环境变量映射')
        print('    3. 添加必要的解析规则')
        print('    4. 重新评估迁移可行性')
        return 'not_ready'

def main():
    """主函数"""
    print('🔧 DaVinci AI Co-pilot Pro 配置文件整合')
    print('=' * 60)

    # 分析配置重叠
    configs = analyze_config_overlap()

    # 评估迁移可行性
    is_feasible = evaluate_migration_feasibility(configs)

    # 创建备份
    backed_up = create_backup_strategy()

    # 生成迁移计划
    migration_status = generate_migration_plan(configs, is_feasible)

    print('\n' + '=' * 60)
    print('🎯 配置整合总结:')
    print(f'  📊 迁移可行性: {"✅ 可行" if is_feasible else "❌ 不可行"}')
    print(f'  💾 备份文件: {len(backed_up)} 个')
    print(f'  🚀 迁移状态: {migration_status}')

    if migration_status == 'ready':
        print('\n🎉 配置整合准备就绪！')
        print('  建议: 可以开始逐步停用旧配置文件')
    elif migration_status == 'needs_improvement':
        print('\n⚠️ 需要完善统一配置')
        print('  建议: 补充缺失的配置项后重新评估')
    else:
        print('\n❌ 需要更多工作')
        print('  建议: 完善统一配置的功能覆盖度')

if __name__ == '__main__':
    main()
