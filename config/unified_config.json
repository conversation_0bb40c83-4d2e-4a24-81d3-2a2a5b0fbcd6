{"_metadata": {"version": "2.0.0", "created": "2025-07-27T19:22:35.460868", "description": "DaVinci AI Co-pilot Pro 统一配置文件", "migration_from": ["config.json", "mcp_enhanced.json", "simplified_service_config.json", "dynamic_types.json"]}, "core": {"app_name": "DaVinci AI Co-pilot <PERSON>", "version": "0.1.0", "debug": true, "log_level": "INFO", "name": "DaVinci AI Co-pilot <PERSON><PERSON>", "host": "127.0.0.1", "port": 8000}, "providers": {"deepseek": {"enabled": true, "api_base": "https://api.deepseek.com", "model": "deepseek-chat", "capabilities": ["TEXT_GENERATION", "TRANSLATION", "TEXT_ANALYSIS"]}, "minimax": {"enabled": true, "api_base": "https://api.minimax.chat", "capabilities": ["SPEECH_SYNTHESIS", "IMAGE_GENERATION", "VIDEO_GENERATION"]}, "elevenlabs": {"enabled": true, "api_base": "https://api.elevenlabs.io", "capabilities": ["VOICE_SYNTHESIS"]}, "doubao": {"enabled": true, "api_base": "https://ark.cn-beijing.volces.com", "capabilities": ["IMAGE_GENERATION"]}, "vidu": {"enabled": true, "api_base": "https://api.vidu.ai", "capabilities": ["VIDEO_GENERATION"]}}, "mcp": {"enabled": true, "timeout": 30, "servers": {"minimax": {"provider": "minimax", "transport": "stdio", "command": "uvx", "args": ["minimax-mcp"], "env": {"MINIMAX_API_KEY": "${MINIMAX_API_KEY}", "MINIMAX_GROUP_ID": "${MINIMAX_GROUP_ID}", "MINIMAX_API_HOST": "${MINIMAX_API_HOST}", "MINIMAX_MCP_BASE_PATH": "${OUTPUT_DIR}", "MINIMAX_RESOURCE_MODE": "url"}, "capabilities": ["speech_synthesis", "voice_cloning", "image_generation", "video_generation"], "tools": {"text_to_audio": "speech_synthesis", "text_to_image": "image_generation", "generate_video": "video_generation", "list_voices": "utility", "voice_clone": "voice_cloning", "music_generation": "audio_generation", "voice_design": "speech_synthesis"}, "enabled": true, "timeout": 60, "retry_count": 3, "health_check_interval": 300, "metadata": {"display_name": "MiniMax AI", "description": "MiniMax多模态AI服务", "version": "1.0.0", "tags": ["ai", "multimodal", "chinese"]}}, "elevenlabs": {"provider": "elevenlabs", "transport": "stdio", "command": "uvx", "args": ["elevenlabs-mcp"], "env": {"ELEVENLABS_API_KEY": "${ELEVENLABS_API_KEY}", "ELEVENLABS_MCP_BASE_PATH": "${OUTPUT_DIR}"}, "capabilities": ["speech_synthesis", "voice_cloning", "speech_to_text", "voice_conversion"], "tools": {"text_to_speech": "speech_synthesis", "voice_clone": "voice_cloning", "speech_to_text": "text_analysis", "voice_conversion": "speech_synthesis", "list_voices": "utility", "search_voices": "utility", "search_voice_library": "utility"}, "enabled": true, "timeout": 60, "retry_count": 3, "health_check_interval": 300, "metadata": {"display_name": "ElevenLabs", "description": "ElevenLabs语音合成和克隆服务", "version": "1.0.0", "tags": ["voice", "tts", "cloning"]}}, "doubao": {"provider": "do<PERSON>o", "transport": "stdio", "command": "uvx", "args": ["doubao-mcp-server"], "env": {"DOUBAO_API_KEY": "${DOUBAO_API_KEY}"}, "capabilities": ["image_generation", "video_generation", "multimodal"], "tools": {"text_to_image": "image_generation", "image_to_video": "video_generation", "text_to_video": "video_generation", "encode_image_to_base64": "image_processing"}, "enabled": true, "timeout": 30, "retry_count": 3, "health_check_interval": 300, "metadata": {"display_name": "豆包", "description": "字节跳动豆包AI多模态生成服务，支持文生图、文生视频、图生视频", "version": "1.0.0", "homepage": "https://github.com/wwwzhouhui/doubao_mcp_server", "tags": ["image", "video", "multimodal", "generation"]}}, "deepseek": {"provider": "deepseek", "transport": "stdio", "command": "npx", "args": ["-y", "deepseek-mcp-server"], "env": {"DEEPSEEK_API_KEY": "${DEEPSEEK_API_KEY}"}, "capabilities": ["text_generation", "chat", "text_analysis", "translation"], "tools": {"chat_completion": "text_generation", "multi_turn_chat": "chat"}, "enabled": true, "timeout": 60, "retry_count": 3, "health_check_interval": 300, "metadata": {"display_name": "DeepSeek", "description": "DeepSeek AI文本生成和对话服务，默认使用V3模型", "version": "0.2.1", "homepage": "https://github.com/DMontgomery40/deepseek-mcp-server", "tags": ["text", "chat", "v3", "reasoning"]}}, "vidu": {"provider": "vidu", "transport": "stdio", "command": "uvx", "args": ["vidu-mcp"], "env": {"VIDU_API_KEY": "${VIDU_API_KEY}", "VIDU_API_HOST": "https://api.vidu.cn", "VIDU_OUTPUT_DIR": "${OUTPUT_DIR}"}, "capabilities": ["video_generation"], "tools": {"generate_video": "video_generation", "text_to_video": "video_generation", "image_to_video": "video_generation", "get_video_status": "utility"}, "enabled": true, "timeout": 120, "retry_count": 2, "health_check_interval": 600, "metadata": {"display_name": "Vidu", "description": "Vidu高质量视频生成服务", "version": "1.0.0", "homepage": "https://platform.vidu.cn/docs/mcp-stdio", "tags": ["video", "generation", "creative"]}}}}, "api": {"host": "127.0.0.1", "port": 8000, "cors_origins": ["*"], "rate_limit": {"enabled": true, "requests_per_minute": 60}}, "services": {"direct_adapter": {"enabled": true, "fallback_to_mcp": true}, "load_balancer": {"enabled": false, "strategy": "round_robin"}, "capabilities": {"text_generation": {"name": "文本生成", "description": "生成各种类型的文本内容", "providers": ["deepseek"], "parameters": {"max_tokens": {"type": "number", "default": 1000, "min": 1, "max": 4000}, "temperature": {"type": "number", "default": 0.7, "min": 0.0, "max": 2.0}}}, "translation": {"name": "翻译", "description": "文本翻译服务", "providers": ["deepseek"], "parameters": {"target_language": {"type": "string", "default": "en", "choices": ["en", "zh", "ja", "ko", "fr", "de", "es"]}, "source_language": {"type": "string", "default": "auto"}}}, "text_analysis": {"name": "文本分析", "description": "分析文本内容，提供洞察和建议", "providers": ["deepseek"], "parameters": {"analysis_type": {"type": "string", "default": "general", "choices": ["general", "sentiment", "keywords", "summary"]}}}, "speech_synthesis": {"name": "语音合成", "description": "将文本转换为语音", "providers": ["elevenlabs", "minimax"], "parameters": {"voice_id": {"type": "string", "default": "auto"}, "stability": {"type": "number", "default": 0.5, "min": 0.0, "max": 1.0}, "similarity_boost": {"type": "number", "default": 0.5, "min": 0.0, "max": 1.0}}}, "image_generation": {"name": "图像生成", "description": "根据文本描述生成图像", "providers": ["minimax", "do<PERSON>o"], "parameters": {"width": {"type": "number", "default": 1024, "choices": [512, 768, 1024, 1280]}, "height": {"type": "number", "default": 1024, "choices": [512, 768, 1024, 1280]}, "style": {"type": "string", "default": "realistic", "choices": ["realistic", "artistic", "cartoon", "abstract"]}}}, "video_generation": {"name": "视频生成", "description": "根据文本或图像生成视频", "providers": ["minimax", "do<PERSON>o", "vidu"], "parameters": {"duration": {"type": "number", "default": 5, "min": 1, "max": 30}, "fps": {"type": "number", "default": 24, "choices": [12, 24, 30, 60]}, "resolution": {"type": "string", "default": "1080p", "choices": ["720p", "1080p", "4k"]}}}}}}