aiofiles==23.2.1
aiohappyeyeballs==2.4.6
aiohttp==3.9.1
aiohttp-cors==0.8.1
aiosignal==1.3.2
annotated-types==0.7.0
anthropic==0.49.0
antlr4-python3-runtime==4.9.3
anyio==4.8.0
argcomplete==3.6.2
asyncio-mqtt==0.16.1
attrs==25.1.0
-e git+https://github.com/double2tea/AudioTranslator.git@6ce540dca65211101ac8ec70f3fb778849176ae6#egg=audio_translator
augment-tools-core @ file:///Users/<USER>/AugmentCode-Free/AugmentCode-Free
azure-cognitiveservices-speech==1.42.0
backoff==2.2.1
black==25.1.0
boto3==1.38.26
botocore==1.38.26
cachetools==5.3.2
certifi==2025.6.15
cffi==1.17.1
cfgv==3.4.0
charset-normalizer==3.4.1
click==8.2.1
colorama==0.4.6
coloredlogs==15.0.1
colorlog==6.9.0
contourpy==1.3.2
cryptography==45.0.5
cycler==0.12.1
decorator==4.4.2
dependency-groups==1.3.1
distlib==0.3.9
distro==1.9.0
ecdsa==0.19.1
edge-tts==7.0.0
ffmpeg-python==0.2.0
filelock==3.17.0
flake8==7.2.0
flake8-copyright==0.2.4
flatbuffers==25.2.10
fonttools==4.58.1
frozenlist==1.5.0
fsspec==2025.5.1
future==1.0.0
google-ai-generativelanguage==0.6.15
google-api-core==2.24.1
google-api-python-client==2.162.0
google-auth==2.38.0
google-auth-httplib2==0.2.0
google-generativeai==0.8.4
googleapis-common-protos==1.69.0
grpcio==1.71.0rc2
grpcio-status==1.71.0rc2
h11==0.14.0
hf-xet==1.1.2
httpcore==1.0.7
httplib2==0.22.0
httpx==0.25.2
huggingface-hub==0.32.3
humanfriendly==10.0
hydra-core==1.3.2
identify==2.6.8
idna==3.10
imageio==2.37.0
imageio-ffmpeg==0.6.0
isort==6.0.1
jieba==0.42.1
Jinja2==3.1.6
jiter==0.8.2
jmespath==1.0.1
kiwisolver==1.4.8
lightning-utilities==0.14.3
markdown-it-py==3.0.0
MarkupSafe==3.0.2
marshmallow==3.20.1
matplotlib==3.10.3
mccabe==0.7.0
mdurl==0.1.2
moviepy==1.0.3
mpmath==1.3.0
msgpack==1.0.7
multidict==6.1.0
mypy_extensions==1.1.0
networkx==3.5
nodeenv==1.9.1
nox==2025.5.1
numpy==2.2.3
omegaconf==2.3.0
onnx==1.18.0
onnxruntime==1.22.0
openai==1.65.3
opencv-python==4.11.0.86
orjson==3.9.10
outcome==1.3.0.post0
packaging==25.0
paho-mqtt==2.1.0
pandas==2.2.3
pathspec==0.12.1
Pillow==10.1.0
platformdirs==4.3.6
pre_commit==4.1.0
proglog==0.1.12
propcache==0.3.0
proto-plus==1.26.0
protobuf==5.29.3
psutil==5.9.6
pyasn1==0.6.1
pyasn1_modules==0.4.1
pycodestyle==2.13.0
pycparser==2.22
pydantic==2.5.2
pydantic_core==2.14.5
pydub==0.25.1
pyflakes==3.3.2
pygame==2.6.1
Pygments==2.19.2
pyparsing==3.2.1
pypinyin==0.53.0
PySocks==1.7.1
python-dateutil==2.8.2
python-dotenv==1.0.0
python-jose==3.3.0
pytorch-lightning==2.5.1.post0
pytouch==0.4.0
pytz==2025.1
PyYAML==6.0.1
regex==2024.11.6
requests==2.31.0
rich==13.7.0
rsa==4.9
s3transfer==0.13.0
safetensors==0.5.3
selenium==4.34.1
sentencepiece==0.2.0
setuptools==80.9.0
six==1.17.0
sniffio==1.3.1
sortedcontainers==2.4.0
srt==3.5.3
structlog==23.2.0
sympy==1.14.0
tabulate==0.9.0
tenacity==8.2.3
tokenizers==0.21.1
torch==2.7.0
torchmetrics==1.7.2
torchvision==0.22.0
tqdm==4.66.1
transformers==4.52.4
trio==0.30.0
trio-websocket==0.12.2
typing_extensions==4.14.1
tzdata==2025.1
uritemplate==4.1.1
urllib3==2.5.0
uuid==1.30
virtualenv==20.29.2
websocket-client==1.8.0
websockets==12.0
wsproto==1.2.0
yarl==1.18.3
