#!/usr/bin/env python3
"""
MCP服务器状态测试脚本
用于检查MCP服务器的启动状态和连接情况
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

async def test_mcp_manager():
    """测试MCP管理器"""
    print("🔍 测试MCP管理器状态...")
    
    try:
        from src.services.fastmcp_server import SimpleMCPManager
        from src.core.unified_config import config_manager
        
        # 创建MCP管理器实例
        mcp_manager = SimpleMCPManager()
        
        # 初始化MCP管理器
        print("🚀 初始化MCP管理器...")
        success = await mcp_manager.initialize()
        
        if success:
            print("✅ MCP管理器初始化成功")
            
            # 检查客户端状态
            clients = mcp_manager.clients
            print(f"📡 MCP客户端数量: {len(clients)}")
            
            for server_name, client in clients.items():
                print(f"  🔗 {server_name}: {'已连接' if client else '未连接'}")
                
                if client:
                    try:
                        # 测试客户端连接
                        tools = await client.list_tools()
                        print(f"    🛠️  工具数量: {len(tools.tools) if tools else 0}")
                        
                        if tools and tools.tools:
                            for tool in tools.tools[:3]:  # 只显示前3个工具
                                print(f"      - {tool.name}: {tool.description[:50]}...")
                    except Exception as e:
                        print(f"    ❌ 工具列表获取失败: {e}")
            
            # 测试MiniMax特定功能
            if 'minimax' in clients and clients['minimax']:
                print("\n🎯 测试MiniMax MCP服务器...")
                try:
                    minimax_client = clients['minimax']
                    tools = await minimax_client.list_tools()
                    
                    if tools and tools.tools:
                        print(f"✅ MiniMax工具列表获取成功，共{len(tools.tools)}个工具:")
                        for tool in tools.tools:
                            print(f"  - {tool.name}: {tool.description}")
                    else:
                        print("❌ MiniMax工具列表为空")
                        
                except Exception as e:
                    print(f"❌ MiniMax测试失败: {e}")
            else:
                print("❌ MiniMax客户端未连接")
                
        else:
            print("❌ MCP管理器初始化失败")
            
    except Exception as e:
        print(f"❌ MCP管理器测试失败: {e}")
        import traceback
        traceback.print_exc()

async def test_direct_mcp_connection():
    """直接测试MCP连接"""
    print("\n🔍 直接测试MCP连接...")
    
    try:
        from fastmcp import FastMCP
        import os
        from dotenv import load_dotenv
        
        # 加载环境变量
        env_file = project_root / ".env"
        if env_file.exists():
            load_dotenv(env_file)
        
        # 设置环境变量
        os.environ['MINIMAX_API_KEY'] = os.getenv('MINIMAX_API_KEY', '')
        os.environ['MINIMAX_API_HOST'] = os.getenv('MINIMAX_API_HOST', '')
        os.environ['MINIMAX_GROUP_ID'] = os.getenv('MINIMAX_GROUP_ID', '')
        os.environ['MINIMAX_MCP_BASE_PATH'] = os.getenv('OUTPUT_DIR', '')
        os.environ['MINIMAX_RESOURCE_MODE'] = 'url'
        
        # 创建FastMCP配置
        config = {
            'mcpServers': {
                'minimax': {
                    'command': 'uvx',
                    'args': ['minimax-mcp'],
                    'env': {
                        'MINIMAX_API_KEY': os.getenv('MINIMAX_API_KEY'),
                        'MINIMAX_API_HOST': os.getenv('MINIMAX_API_HOST'),
                        'MINIMAX_GROUP_ID': os.getenv('MINIMAX_GROUP_ID'),
                        'MINIMAX_MCP_BASE_PATH': os.getenv('OUTPUT_DIR'),
                        'MINIMAX_RESOURCE_MODE': 'url'
                    }
                }
            }
        }
        
        print("🚀 创建FastMCP实例...")
        mcp = FastMCP(config)
        
        print("🔗 连接到MiniMax MCP服务器...")
        await mcp.initialize()
        
        # 测试工具列表
        tools = await mcp.list_tools()
        print(f"✅ 工具列表获取成功，共{len(tools)}个工具:")
        for tool_name, tool_info in tools.items():
            print(f"  - {tool_name}: {tool_info.get('description', 'No description')}")
            
        # 清理
        await mcp.close()
        print("✅ 连接测试完成")
        
    except Exception as e:
        print(f"❌ 直接MCP连接测试失败: {e}")
        import traceback
        traceback.print_exc()

async def main():
    """主函数"""
    print("🚀 DaVinci AI Co-pilot Pro - MCP状态测试")
    print("=" * 50)
    
    await test_mcp_manager()
    await test_direct_mcp_connection()
    
    print("\n" + "=" * 50)
    print("🏁 测试完成")

if __name__ == "__main__":
    asyncio.run(main())
