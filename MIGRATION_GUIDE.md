
# DaVinci AI Co-pilot Pro 新架构迁移指南

## 🎯 新架构特性

### 1. 统一服务配置
- 所有服务配置集中在 `config/unified_services.json`
- 支持服务元数据、UI配置、解析规则的统一管理
- 配置驱动的服务注册和发现

### 2. 动态路由系统
- 基于配置自动生成API路由
- 消除硬编码的服务提供商判断
- 支持运行时动态添加新服务

### 3. 通用前端组件
- 自动生成服务提供商选择器
- 基于配置的参数控制面板
- 统一的事件处理和参数管理

### 4. 统一结果解析
- 配置驱动的解析规则
- 支持多种数据提取模式
- 自动错误检测和处理

### 5. 自动媒体库集成
- 新生成的文件自动添加到媒体库
- 智能元数据提取和标签生成
- 支持缩略图自动生成

## 🚀 添加新服务的步骤

### 1. 更新统一配置
在 `config/unified_services.json` 中添加新服务配置：

```json
{
  "services": {
    "new_service": {
      "metadata": {
        "display_name": "新服务",
        "description": "服务描述",
        "icon": "🤖",
        "priority": 5
      },
      "capabilities": {
        "text_generation": {
          "enabled": true,
          "models": ["model-1", "model-2"]
        }
      },
      "mcp_config": {
        "transport": "stdio",
        "command": "uvx",
        "args": ["new-service-mcp"]
      },
      "tools": {
        "generate_text": {
          "capability": "text_generation",
          "parameters": {
            "prompt": {"type": "string", "required": true}
          }
        }
      },
      "ui_config": {
        "provider_selector": {
          "label": "🤖 新服务",
          "description": "服务描述"
        },
        "parameter_panels": {
          "text_generation": {
            "title": "新服务参数",
            "groups": [...]
          }
        }
      }
    }
  }
}
```

### 2. 重启服务
重启DaVinci AI Co-pilot Pro服务，新服务将自动：
- 注册到动态路由系统
- 在前端界面显示
- 支持参数控制
- 集成结果解析
- 自动媒体库集成

## 🔧 配置热重载

支持运行时重新加载配置：
```bash
curl -X POST http://127.0.0.1:8000/api/config/reload
```

## 📊 监控和调试

### 查看已注册路由
```bash
curl http://127.0.0.1:8000/api/routes/registered
```

### 查看服务能力
```bash
curl http://127.0.0.1:8000/api/services/capabilities
```

### 查看服务统计
```bash
curl http://127.0.0.1:8000/api/services/stats
```

## ⚠️ 注意事项

1. **向后兼容**：现有MiniMax和ElevenLabs服务功能完全保留
2. **配置验证**：修改配置前请备份原文件
3. **性能监控**：新架构包含完整的性能监控和错误处理
4. **扩展性**：支持无限制添加新的MCP服务

## 🆘 故障排除

### 服务未显示
1. 检查 `config/unified_services.json` 配置
2. 确认MCP服务器正确安装
3. 查看服务日志排查错误

### 参数面板未生成
1. 检查 `ui_config.parameter_panels` 配置
2. 确认前端JavaScript模块正确加载
3. 检查浏览器控制台错误

### 结果解析失败
1. 检查 `result_parsing` 配置
2. 测试正则表达式模式
3. 查看解析器日志

## 📞 技术支持

如遇问题，请查看：
1. 服务日志：`logs/` 目录
2. 配置备份：`config_backup/` 目录
3. 测试脚本：`test_new_architecture.py`
