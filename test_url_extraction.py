#!/usr/bin/env python3
"""
测试MiniMax URL提取逻辑
"""

import re
import ast

def extract_minimax_audio_url(text_content: str) -> str:
    """从MiniMax响应中提取音频URL"""
    print(f"🔍 输入内容: {text_content}")
    
    try:
        # 尝试解析为Python字典字符串
        if text_content.startswith("{'") and text_content.endswith("'}"):
            try:
                print("📝 尝试解析为Python字典...")
                data_dict = ast.literal_eval(text_content)
                print(f"📊 解析结果: {data_dict}")
                
                if isinstance(data_dict, dict) and 'audio_url' in data_dict:
                    audio_url_text = data_dict['audio_url']
                    print(f"🎵 音频URL文本: {audio_url_text}")
                    
                    # 从 "Success. Audio URL: https://..." 中提取URL
                    url_match = re.search(r'https?://[^\s]+\.(?:mp3|wav|flac)[^\s]*', audio_url_text)
                    if url_match:
                        extracted_url = url_match.group(0)
                        print(f"✅ 提取的URL: {extracted_url}")
                        return extracted_url
            except (ValueError, SyntaxError) as e:
                print(f"❌ 字典解析失败: {e}")
        
        # 直接从文本中提取URL
        print("🔍 直接从文本中提取URL...")
        url_match = re.search(r'https?://[^\s]+\.(?:mp3|wav|flac)[^\s]*', text_content)
        if url_match:
            extracted_url = url_match.group(0)
            print(f"✅ 直接提取的URL: {extracted_url}")
            return extracted_url
        
        # 如果都失败了，返回原始内容
        print(f"⚠️ 无法提取音频URL，返回原始内容")
        return text_content
        
    except Exception as e:
        print(f"❌ 提取过程出错: {e}")
        return text_content

def main():
    """测试URL提取"""
    print("🚀 测试MiniMax URL提取逻辑")
    print("=" * 60)
    
    # 测试用例1：从HTTP测试中获得的实际响应
    test_response = "{'audio_url': 'Success. Audio URL: https://minimax-algeng-chat-tts.oss-cn-wulanchabu.aliyuncs.com/audio%2Ftts-20250727232618-UGAIIrtTbrAtYhlw.mp3?Expires=1753716378&OSSAccessKeyId=LTAI5tGLnRTkBjLuYPjNcKQ8&Signature=dS1VDsC3bsspEG9Yx8FT8nXMaog%3D'}"
    
    print("📋 测试用例1: 实际MiniMax响应")
    result = extract_minimax_audio_url(test_response)
    print(f"🎯 最终结果: {result}")
    print()
    
    # 测试用例2：直接URL
    test_url = "https://minimax-algeng-chat-tts.oss-cn-wulanchabu.aliyuncs.com/audio%2Ftts-test.mp3"
    print("📋 测试用例2: 直接URL")
    result2 = extract_minimax_audio_url(test_url)
    print(f"🎯 最终结果: {result2}")
    print()
    
    print("=" * 60)
    print("🏁 测试完成")

if __name__ == "__main__":
    main()
