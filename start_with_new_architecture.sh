#!/bin/bash

echo "🚀 启动DaVinci AI Co-pilot Pro (新架构)"
echo "=========================================="

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3未安装"
    exit 1
fi

# 检查uv是否安装
if ! command -v uv &> /dev/null; then
    echo "❌ uv未安装，请先安装uv"
    echo "安装命令: curl -LsSf https://astral.sh/uv/install.sh | sh"
    exit 1
fi

# 设置环境变量
export PYTHONPATH="${PWD}:${PYTHONPATH}"

echo "📦 检查依赖..."

# 检查必要的Python包
python3 -c "import fastapi, uvicorn, aiohttp" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "❌ 缺少必要的Python包，请安装依赖"
    echo "安装命令: pip install fastapi uvicorn aiohttp"
    exit 1
fi

echo "✅ 依赖检查通过"

# 检查配置文件
if [ ! -f "config/mcp_enhanced.json" ]; then
    echo "❌ 缺少MCP配置文件: config/mcp_enhanced.json"
    exit 1
fi

if [ ! -f "config/config.json" ]; then
    echo "❌ 缺少主配置文件: config/config.json"
    exit 1
fi

echo "✅ 配置文件检查通过"

# 创建输出目录
mkdir -p output/ai_frames
mkdir -p output/media_library
mkdir -p logs

echo "✅ 目录结构检查通过"

# 启动服务器
echo "🌟 启动服务器..."
echo "访问地址: http://127.0.0.1:8000"
echo "按 Ctrl+C 停止服务"
echo ""

# 启动主服务
python3 -m uvicorn src.main:app --host 127.0.0.1 --port 8000 --reload &
SERVER_PID=$!

# 等待服务启动
sleep 5

# 运行测试
echo "🧪 运行架构测试..."
python3 test_new_architecture.py

# 保持服务运行
echo ""
echo "✅ 测试完成，服务继续运行..."
echo "按 Ctrl+C 停止服务"

# 等待用户中断
wait $SERVER_PID
