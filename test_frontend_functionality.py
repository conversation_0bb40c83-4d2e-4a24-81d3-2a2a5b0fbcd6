#!/usr/bin/env python3
"""
前端功能测试脚本
验证前端代码去冗余后的功能完整性
"""

import asyncio
import aiohttp
import json
import sys
import time

class FrontendTester:
    def __init__(self, base_url="http://127.0.0.1:8000"):
        self.base_url = base_url
        self.session = None
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def test_health_check(self):
        """测试健康检查端点"""
        try:
            async with self.session.get(f"{self.base_url}/health") as response:
                if response.status == 200:
                    print("✅ 健康检查通过")
                    return True
                else:
                    print(f"❌ 健康检查失败: {response.status}")
                    return False
        except Exception as e:
            print(f"❌ 健康检查异常: {e}")
            return False
    
    async def test_unified_service_endpoint(self):
        """测试统一服务端点"""
        test_data = {
            "service_type": "text_generation",
            "capability": "text_generation", 
            "provider": "deepseek",
            "content": "你好，请简单介绍一下自己",
            "parameters": {
                "max_tokens": 50
            },
            "metadata": {}
        }
        
        try:
            async with self.session.post(
                f"{self.base_url}/api/services/unified",
                json=test_data,
                headers={"Content-Type": "application/json"}
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    if result.get("success"):
                        print("✅ 统一服务端点测试通过")
                        print(f"   响应数据: {result.get('data', {}).get('generated_text', '')[:50]}...")
                        return True
                    else:
                        print(f"❌ 统一服务端点返回失败: {result.get('error')}")
                        return False
                else:
                    error_text = await response.text()
                    print(f"❌ 统一服务端点HTTP错误: {response.status} - {error_text}")
                    return False
        except Exception as e:
            print(f"❌ 统一服务端点异常: {e}")
            return False
    
    async def test_config_endpoints(self):
        """测试配置端点"""
        endpoints = [
            "/api/config/capabilities",
            "/api/config/providers", 
            "/api/config/services"
        ]
        
        results = []
        for endpoint in endpoints:
            try:
                async with self.session.get(f"{self.base_url}{endpoint}") as response:
                    if response.status == 200:
                        data = await response.json()
                        print(f"✅ 配置端点 {endpoint} 正常")
                        results.append(True)
                    else:
                        print(f"❌ 配置端点 {endpoint} 失败: {response.status}")
                        results.append(False)
            except Exception as e:
                print(f"❌ 配置端点 {endpoint} 异常: {e}")
                results.append(False)
        
        return all(results)
    
    async def test_static_files(self):
        """测试静态文件访问"""
        static_files = [
            "/static/js/unified-ai-client.js",
            "/static/js/app.js",
            "/static/js/components.js",
            "/static/js/modules/dynamic-config-loader.js"
        ]
        
        results = []
        for file_path in static_files:
            try:
                async with self.session.get(f"{self.base_url}{file_path}") as response:
                    if response.status == 200:
                        print(f"✅ 静态文件 {file_path} 可访问")
                        results.append(True)
                    else:
                        print(f"❌ 静态文件 {file_path} 不可访问: {response.status}")
                        results.append(False)
            except Exception as e:
                print(f"❌ 静态文件 {file_path} 异常: {e}")
                results.append(False)
        
        return all(results)
    
    async def run_all_tests(self):
        """运行所有测试"""
        print("🧪 开始前端功能完整性测试...")
        print("=" * 50)
        
        tests = [
            ("健康检查", self.test_health_check),
            ("统一服务端点", self.test_unified_service_endpoint),
            ("配置端点", self.test_config_endpoints),
            ("静态文件访问", self.test_static_files)
        ]
        
        results = []
        for test_name, test_func in tests:
            print(f"\n🔍 测试: {test_name}")
            result = await test_func()
            results.append(result)
            
        print("\n" + "=" * 50)
        print("📊 测试结果汇总:")
        passed = sum(results)
        total = len(results)
        print(f"   通过: {passed}/{total}")
        print(f"   成功率: {passed/total*100:.1f}%")
        
        if passed == total:
            print("🎉 所有测试通过！前端功能完整性验证成功")
            return True
        else:
            print("⚠️  部分测试失败，需要进一步检查")
            return False

async def main():
    """主函数"""
    print("🚀 DaVinci AI Co-pilot Pro 前端功能测试")
    
    # 等待服务器启动
    print("⏳ 等待服务器启动...")
    await asyncio.sleep(3)
    
    async with FrontendTester() as tester:
        success = await tester.run_all_tests()
        
    if success:
        print("\n✅ 前端代码去冗余完成，功能验证通过！")
        sys.exit(0)
    else:
        print("\n❌ 前端功能验证失败，需要修复问题")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
