#!/bin/bash
# 环境检查脚本

echo "🔍 检查重构环境..."

PROJECT_ROOT="/Library/Application Support/Blackmagic Design/DaVinci Resolve/Fusion/Scripts/Utility/DaVinci AI Co-pilot Pro"

# 检查Python环境
echo "🐍 检查Python环境..."
python3 --version || echo "❌ Python3 未安装"
pip3 --version || echo "❌ pip3 未安装"

# 检查必要的Python包
echo "📦 检查Python依赖..."
cd "$PROJECT_ROOT"
if [ -f "requirements.txt" ]; then
    pip3 check || echo "⚠️  依赖包可能有问题"
else
    echo "⚠️  未找到requirements.txt"
fi

# 检查配置文件
echo "⚙️  检查配置文件..."
[ -f "config/config.json" ] && echo "✅ config.json 存在" || echo "❌ config.json 缺失"
[ -f ".env" ] && echo "✅ .env 存在" || echo "❌ .env 缺失"
[ -f "config/mcp_enhanced.json" ] && echo "✅ mcp_enhanced.json 存在" || echo "❌ mcp_enhanced.json 缺失"

# 检查关键目录
echo "📁 检查目录结构..."
[ -d "src" ] && echo "✅ src/ 目录存在" || echo "❌ src/ 目录缺失"
[ -d "web" ] && echo "✅ web/ 目录存在" || echo "❌ web/ 目录缺失"
[ -d "config" ] && echo "✅ config/ 目录存在" || echo "❌ config/ 目录缺失"

echo "🎉 环境检查完成"
