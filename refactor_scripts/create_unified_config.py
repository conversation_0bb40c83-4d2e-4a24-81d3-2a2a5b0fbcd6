#!/usr/bin/env python3
"""
创建统一的配置文件
"""

import json
import os
from pathlib import Path
from datetime import datetime

def create_unified_config():
    """创建统一配置"""
    project_root = Path('/Library/Application Support/Blackmagic Design/DaVinci Resolve/Fusion/Scripts/Utility/DaVinci AI Co-pilot Pro')
    config_dir = project_root / 'config'

    # 读取现有配置
    configs = {}
    config_files = ['config.json', 'mcp_enhanced.json', 'simplified_service_config.json', 'dynamic_types.json']

    for config_file in config_files:
        config_path = config_dir / config_file
        if config_path.exists():
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    configs[config_file] = json.load(f)
                print(f"  ✅ 读取配置: {config_file}")
            except Exception as e:
                print(f"  ❌ 读取失败: {config_file} - {e}")

    # 创建统一配置结构
    unified_config = {
        "_metadata": {
            "version": "2.0.0",
            "created": datetime.now().isoformat(),
            "description": "DaVinci AI Co-pilot Pro 统一配置文件",
            "migration_from": list(configs.keys())
        },

        # 核心配置
        "core": {
            "app_name": "DaVinci AI Co-pilot Pro",
            "version": "2.0.0",
            "debug": False,
            "log_level": "INFO"
        },

        # 服务提供商配置
        "providers": {
            "deepseek": {
                "enabled": True,
                "api_base": "https://api.deepseek.com",
                "model": "deepseek-chat",
                "capabilities": ["TEXT_GENERATION", "TRANSLATION", "TEXT_ANALYSIS"]
            },
            "minimax": {
                "enabled": True,
                "api_base": "https://api.minimax.chat",
                "capabilities": ["VOICE_SYNTHESIS"]
            },
            "elevenlabs": {
                "enabled": True,
                "api_base": "https://api.elevenlabs.io",
                "capabilities": ["VOICE_SYNTHESIS"]
            },
            "doubao": {
                "enabled": True,
                "api_base": "https://ark.cn-beijing.volces.com",
                "capabilities": ["IMAGE_GENERATION"]
            },
            "vidu": {
                "enabled": True,
                "api_base": "https://api.vidu.ai",
                "capabilities": ["VIDEO_GENERATION"]
            }
        },

        # MCP服务器配置
        "mcp": {
            "enabled": True,
            "timeout": 30,
            "servers": {}
        },

        # API配置
        "api": {
            "host": "127.0.0.1",
            "port": 8000,
            "cors_origins": ["*"],
            "rate_limit": {
                "enabled": True,
                "requests_per_minute": 60
            }
        },

        # 服务配置
        "services": {
            "direct_adapter": {
                "enabled": True,
                "fallback_to_mcp": True
            },
            "load_balancer": {
                "enabled": False,
                "strategy": "round_robin"
            }
        }
    }

    # 合并现有配置
    for config_name, config_data in configs.items():
        print(f"  🔄 合并配置: {config_name}")

        if config_name == 'config.json' and isinstance(config_data, dict):
            # 合并核心配置
            if 'app' in config_data:
                unified_config['core'].update(config_data['app'])
            if 'api' in config_data:
                unified_config['api'].update(config_data['api'])

        elif config_name == 'mcp_enhanced.json' and isinstance(config_data, dict):
            # 合并MCP配置
            if 'mcpServers' in config_data:
                unified_config['mcp']['servers'] = config_data['mcpServers']
            if 'timeout' in config_data:
                unified_config['mcp']['timeout'] = config_data['timeout']

        elif config_name == 'simplified_service_config.json' and isinstance(config_data, dict):
            # 合并服务配置
            if 'services' in config_data:
                unified_config['services'].update(config_data['services'])

    # 保存统一配置
    unified_path = config_dir / 'unified_config.json'
    with open(unified_path, 'w', encoding='utf-8') as f:
        json.dump(unified_config, f, indent=2, ensure_ascii=False)

    print(f"  ✅ 统一配置已创建: {unified_path}")
    print(f"  📊 配置项统计: {len(unified_config)} 个顶级分类")

    return unified_config

if __name__ == "__main__":
    create_unified_config()
