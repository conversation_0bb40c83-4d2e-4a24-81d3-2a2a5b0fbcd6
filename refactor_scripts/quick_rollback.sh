#!/bin/bash
# 快速回滚脚本 - 紧急情况使用

set -e

PROJECT_ROOT="/Library/Application Support/Blackmagic Design/DaVinci Resolve/Fusion/Scripts/Utility/DaVinci AI Co-pilot Pro"
BACKUP_DIR=$(ls -td "${PROJECT_ROOT}_BACKUP_"* | head -1)

echo "🚨 执行紧急回滚..."
echo "📁 项目路径: $PROJECT_ROOT"  
echo "💾 备份路径: $BACKUP_DIR"

# 停止可能运行的服务
echo "🛑 停止服务..."
pkill -f "DaVinci AI Co-pilot Pro" || echo "⚠️  没有运行的服务需要停止"

# 删除当前项目并恢复备份
echo "🔄 恢复备份..."
rm -rf "$PROJECT_ROOT"
cp -r "$BACKUP_DIR" "$PROJECT_ROOT"

echo "✅ 回滚完成！系统已恢复到重构前状态"
echo "📝 请检查功能是否正常，然后重新开始重构"
