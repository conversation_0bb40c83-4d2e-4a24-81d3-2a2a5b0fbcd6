#!/bin/bash

# DaVinci AI Co-pilot Pro 重构准备脚本
# 阶段0：环境准备和备份

set -e  # 遇到错误立即退出

echo "🚀 开始DaVinci AI Co-pilot Pro重构准备..."

# 定义路径
PROJECT_ROOT="/Library/Application Support/Blackmagic Design/DaVinci Resolve/Fusion/Scripts/Utility/DaVinci AI Co-pilot Pro"
BACKUP_DIR="${PROJECT_ROOT}_BACKUP_$(date +%Y%m%d_%H%M%S)"
REFACTOR_DIR="${PROJECT_ROOT}/refactor_scripts"

# 创建重构脚本目录
mkdir -p "$REFACTOR_DIR"

echo "📁 项目路径: $PROJECT_ROOT"
echo "💾 备份路径: $BACKUP_DIR"

# 1. 创建完整备份
echo "🔄 创建完整备份..."
if [ -d "$PROJECT_ROOT" ]; then
    cp -r "$PROJECT_ROOT" "$BACKUP_DIR"
    echo "✅ 备份完成: $BACKUP_DIR"
else
    echo "❌ 错误: 项目目录不存在: $PROJECT_ROOT"
    exit 1
fi


# 3. 创建Git仓库（如果不存在）
cd "$PROJECT_ROOT"
if [ ! -d ".git" ]; then
    echo "🔧 初始化Git仓库..."
    git init
    git add .
    git commit -m "重构前的初始提交 - $(date)"
    echo "✅ Git仓库初始化完成"
else
    echo "🔧 提交当前状态到Git..."
    git add .
    git commit -m "重构前状态保存 - $(date)" || echo "⚠️  没有新的更改需要提交"
fi

# 4. 创建重构日志
REFACTOR_LOG="$REFACTOR_DIR/refactor_log.txt"
echo "📝 创建重构日志: $REFACTOR_LOG"
cat > "$REFACTOR_LOG" << EOF
DaVinci AI Co-pilot Pro 重构日志
================================

开始时间: $(date)
项目路径: $PROJECT_ROOT
备份路径: $BACKUP_DIR

阶段0: 环境准备和备份 - 完成 ✅
- 创建完整备份
- 验证备份完整性  
- 初始化Git仓库
- 创建重构日志

下一步: 执行依赖关系分析
EOF

# 5. 创建快速回滚脚本
ROLLBACK_SCRIPT="$REFACTOR_DIR/quick_rollback.sh"
echo "🔄 创建快速回滚脚本: $ROLLBACK_SCRIPT"
cat > "$ROLLBACK_SCRIPT" << 'EOF'
#!/bin/bash
# 快速回滚脚本 - 紧急情况使用

set -e

PROJECT_ROOT="/Library/Application Support/Blackmagic Design/DaVinci Resolve/Fusion/Scripts/Utility/DaVinci AI Co-pilot Pro"
BACKUP_DIR=$(ls -td "${PROJECT_ROOT}_BACKUP_"* | head -1)

echo "🚨 执行紧急回滚..."
echo "📁 项目路径: $PROJECT_ROOT"  
echo "💾 备份路径: $BACKUP_DIR"

# 停止可能运行的服务
echo "🛑 停止服务..."
pkill -f "DaVinci AI Co-pilot Pro" || echo "⚠️  没有运行的服务需要停止"

# 删除当前项目并恢复备份
echo "🔄 恢复备份..."
rm -rf "$PROJECT_ROOT"
cp -r "$BACKUP_DIR" "$PROJECT_ROOT"

echo "✅ 回滚完成！系统已恢复到重构前状态"
echo "📝 请检查功能是否正常，然后重新开始重构"
EOF

chmod +x "$ROLLBACK_SCRIPT"

# 6. 创建环境检查脚本
ENV_CHECK_SCRIPT="$REFACTOR_DIR/check_environment.sh"
echo "🔍 创建环境检查脚本: $ENV_CHECK_SCRIPT"
cat > "$ENV_CHECK_SCRIPT" << 'EOF'
#!/bin/bash
# 环境检查脚本

echo "🔍 检查重构环境..."

PROJECT_ROOT="/Library/Application Support/Blackmagic Design/DaVinci Resolve/Fusion/Scripts/Utility/DaVinci AI Co-pilot Pro"

# 检查Python环境
echo "🐍 检查Python环境..."
python3 --version || echo "❌ Python3 未安装"
pip3 --version || echo "❌ pip3 未安装"

# 检查必要的Python包
echo "📦 检查Python依赖..."
cd "$PROJECT_ROOT"
if [ -f "requirements.txt" ]; then
    pip3 check || echo "⚠️  依赖包可能有问题"
else
    echo "⚠️  未找到requirements.txt"
fi

# 检查配置文件
echo "⚙️  检查配置文件..."
[ -f "config/config.json" ] && echo "✅ config.json 存在" || echo "❌ config.json 缺失"
[ -f ".env" ] && echo "✅ .env 存在" || echo "❌ .env 缺失"
[ -f "config/mcp_enhanced.json" ] && echo "✅ mcp_enhanced.json 存在" || echo "❌ mcp_enhanced.json 缺失"

# 检查关键目录
echo "📁 检查目录结构..."
[ -d "src" ] && echo "✅ src/ 目录存在" || echo "❌ src/ 目录缺失"
[ -d "web" ] && echo "✅ web/ 目录存在" || echo "❌ web/ 目录缺失"
[ -d "config" ] && echo "✅ config/ 目录存在" || echo "❌ config/ 目录缺失"

echo "🎉 环境检查完成"
EOF

chmod +x "$ENV_CHECK_SCRIPT"

# 7. 运行环境检查
echo "🔍 运行环境检查..."
bash "$ENV_CHECK_SCRIPT"

echo ""
echo "🎉 阶段0完成！环境准备和备份已就绪"
echo ""
echo "📋 已创建的文件："
echo "  - 完整备份: $BACKUP_DIR"
echo "  - 重构日志: $REFACTOR_LOG"
echo "  - 回滚脚本: $ROLLBACK_SCRIPT"
echo "  - 环境检查: $ENV_CHECK_SCRIPT"
echo ""
echo "🚀 下一步: 执行依赖关系分析"
echo "   运行: bash refactor_scripts/01_analyze_dependencies.sh"
