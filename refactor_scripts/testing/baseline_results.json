{"test_time": "2025-07-28T00:01:52.716951", "tests": {"import_src.services.direct_service_adapter": {"passed": true, "message": "模块导入成功", "timestamp": "2025-07-28T00:01:53.278045"}, "import_src.services.simplified_ai_services": {"passed": true, "message": "模块导入成功", "timestamp": "2025-07-28T00:01:53.278074"}, "import_src.services.fastmcp_server": {"passed": true, "message": "模块导入成功", "timestamp": "2025-07-28T00:01:53.278079"}, "import_src.services.simplified_types": {"passed": true, "message": "模块导入成功", "timestamp": "2025-07-28T00:01:53.278083"}, "import_src.api.routes": {"passed": true, "message": "模块导入成功", "timestamp": "2025-07-28T00:01:53.382789"}, "config_config/unified_config.json": {"passed": true, "message": "配置文件加载成功", "timestamp": "2025-07-28T00:01:53.383025"}, "config_.env": {"passed": true, "message": "环境文件存在，46行", "timestamp": "2025-07-28T00:01:53.383124"}, "direct_service_adapter_init": {"passed": true, "message": "DirectServiceAdapter初始化成功", "timestamp": "2025-07-28T00:01:53.383142"}, "simplified_ai_services_init": {"passed": true, "message": "SimplifiedAIServiceManager初始化成功", "timestamp": "2025-07-28T00:01:53.383148"}, "mcp_manager_init": {"passed": true, "message": "MCP管理器初始化成功", "timestamp": "2025-07-28T00:01:53.383159"}, "mcp_servers_config": {"passed": false, "message": "未找到MCP服务器配置", "timestamp": "2025-07-28T00:01:53.383162"}, "api_routes_import": {"passed": true, "message": "API路由导入成功", "timestamp": "2025-07-28T00:01:53.383185"}, "api_routes_count": {"passed": true, "message": "发现45个路由", "timestamp": "2025-07-28T00:01:53.383196"}, "type_system_simplified": {"passed": true, "message": "简化类型系统: 9个能力, 5个提供商", "timestamp": "2025-07-28T00:01:53.383213"}, "type_system_request": {"passed": true, "message": "ServiceRequest创建成功", "timestamp": "2025-07-28T00:01:53.383223"}}, "summary": {"total": 15, "passed": 14, "failed": 1, "errors": 0}}