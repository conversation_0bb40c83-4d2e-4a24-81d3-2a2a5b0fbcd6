#!/usr/bin/env python3
"""
性能基准测试 - 记录重构前的性能指标
"""

import time
import psutil
import json
import os
from datetime import datetime

def measure_startup_time():
    """测量启动时间"""
    start_time = time.time()

    try:
        # 模拟插件启动过程
        import sys
        sys.path.insert(0, '/Library/Application Support/Blackmagic Design/DaVinci Resolve/Fusion/Scripts/Utility/DaVinci AI Co-pilot Pro')

        from src.services.direct_service_adapter import DirectServiceAdapter
        from src.services.simplified_ai_services import SimplifiedAIServices

        adapter = DirectServiceAdapter()
        services = SimplifiedAIServices()

        startup_time = time.time() - start_time
        return startup_time

    except Exception as e:
        return -1

def measure_memory_usage():
    """测量内存使用"""
    process = psutil.Process()
    memory_info = process.memory_info()

    return {
        'rss': memory_info.rss,  # 物理内存
        'vms': memory_info.vms,  # 虚拟内存
        'percent': process.memory_percent()
    }

def run_performance_baseline():
    """运行性能基准测试"""
    print("⚡ 开始性能基准测试...")

    results = {
        'timestamp': datetime.now().isoformat(),
        'startup_time': measure_startup_time(),
        'memory_usage': measure_memory_usage(),
        'cpu_count': psutil.cpu_count(),
        'system_memory': psutil.virtual_memory().total
    }

    # 保存结果
    output_file = '/Library/Application Support/Blackmagic Design/DaVinci Resolve/Fusion/Scripts/Utility/DaVinci AI Co-pilot Pro/refactor_scripts/testing/performance_baseline.json'

    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)

    print(f"📊 性能基准结果:")
    print(f"  启动时间: {results['startup_time']:.2f}秒")
    print(f"  内存使用: {results['memory_usage']['rss'] / 1024 / 1024:.1f}MB")
    print(f"  CPU使用率: {results['memory_usage']['percent']:.1f}%")
    print(f"💾 结果已保存: {output_file}")

if __name__ == "__main__":
    run_performance_baseline()
