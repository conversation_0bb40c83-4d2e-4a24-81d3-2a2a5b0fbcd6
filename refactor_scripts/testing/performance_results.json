{"import_performance": {"src.services.simplified_types": 550.*************, "src.services.direct_service_adapter": 0.*****************, "src.services.simplified_ai_services": 0.****************, "src.api.unified_routes": 112.**************, "src.core.unified_config": 0.****************}, "type_system_performance": {"enum_access_1000x": 1.****************, "object_creation_100x": 0.*****************}, "service_adapter_performance": {"initialization": 0.001334003172814846, "request_preparation_50x": 0.027040980057790875}, "api_routing_performance": {"route_loading": 0.001333013642579317, "route_count": 4}, "config_loading_performance": {"initialization": 0.****************, "access_100x": 0.0018749851733446121}}