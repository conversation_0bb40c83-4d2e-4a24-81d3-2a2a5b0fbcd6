#!/usr/bin/env python3
"""
DaVinci AI Co-pilot Pro 自动化测试框架
提供统一的测试基础设施和报告功能
"""

import sys
import os
import json
import time
import traceback
import subprocess
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

# 添加项目路径
sys.path.insert(0, '/Library/Application Support/Blackmagic Design/DaVinci Resolve/Fusion/Scripts/Utility/DaVinci AI Co-pilot Pro')

@dataclass
class TestResult:
    """测试结果数据类"""
    name: str
    passed: bool
    message: str
    duration: float
    timestamp: str
    error_details: Optional[str] = None

class TestFramework:
    """自动化测试框架"""

    def __init__(self, test_suite_name: str):
        self.test_suite_name = test_suite_name
        self.results: List[TestResult] = []
        self.start_time = time.time()

    def run_test(self, test_name: str, test_func, *args, **kwargs) -> TestResult:
        """运行单个测试"""
        print(f"🧪 运行测试: {test_name}")

        start_time = time.time()
        timestamp = datetime.now().isoformat()

        try:
            result = test_func(*args, **kwargs)
            duration = time.time() - start_time

            if result is True or (isinstance(result, dict) and result.get('success', False)):
                test_result = TestResult(
                    name=test_name,
                    passed=True,
                    message=result.get('message', '测试通过') if isinstance(result, dict) else '测试通过',
                    duration=duration,
                    timestamp=timestamp
                )
                print(f"  ✅ {test_name} - {duration:.2f}s")
            else:
                test_result = TestResult(
                    name=test_name,
                    passed=False,
                    message=result.get('message', '测试失败') if isinstance(result, dict) else '测试失败',
                    duration=duration,
                    timestamp=timestamp
                )
                print(f"  ❌ {test_name} - {duration:.2f}s")

        except Exception as e:
            duration = time.time() - start_time
            error_details = traceback.format_exc()

            test_result = TestResult(
                name=test_name,
                passed=False,
                message=f"测试异常: {str(e)}",
                duration=duration,
                timestamp=timestamp,
                error_details=error_details
            )
            print(f"  🚨 {test_name} - 异常: {str(e)}")

        self.results.append(test_result)
        return test_result

    def get_summary(self) -> Dict[str, Any]:
        """获取测试摘要"""
        total = len(self.results)
        passed = sum(1 for r in self.results if r.passed)
        failed = total - passed
        total_duration = time.time() - self.start_time

        return {
            'test_suite': self.test_suite_name,
            'total_tests': total,
            'passed': passed,
            'failed': failed,
            'success_rate': (passed / total * 100) if total > 0 else 0,
            'total_duration': total_duration,
            'timestamp': datetime.now().isoformat()
        }

    def save_results(self, output_file: str):
        """保存测试结果"""
        report = {
            'summary': self.get_summary(),
            'results': [
                {
                    'name': r.name,
                    'passed': r.passed,
                    'message': r.message,
                    'duration': r.duration,
                    'timestamp': r.timestamp,
                    'error_details': r.error_details
                }
                for r in self.results
            ]
        }

        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)

        print(f"💾 测试报告已保存: {output_file}")

    def print_summary(self):
        """打印测试摘要"""
        summary = self.get_summary()

        print("\n" + "="*60)
        print(f"📊 {summary['test_suite']} 测试摘要")
        print("="*60)
        print(f"总测试数: {summary['total_tests']}")
        print(f"通过: {summary['passed']} ✅")
        print(f"失败: {summary['failed']} ❌")
        print(f"成功率: {summary['success_rate']:.1f}%")
        print(f"总耗时: {summary['total_duration']:.2f}秒")

        if summary['failed'] > 0:
            print(f"\n❌ 失败的测试:")
            for result in self.results:
                if not result.passed:
                    print(f"  - {result.name}: {result.message}")

        return summary['failed'] == 0

class ModuleTestSuite:
    """模块测试套件"""

    @staticmethod
    def test_import_module(module_name: str) -> Dict[str, Any]:
        """测试模块导入"""
        try:
            __import__(module_name)
            return {'success': True, 'message': f'模块 {module_name} 导入成功'}
        except ImportError as e:
            return {'success': False, 'message': f'模块 {module_name} 导入失败: {str(e)}'}
        except Exception as e:
            return {'success': False, 'message': f'模块 {module_name} 导入异常: {str(e)}'}

    @staticmethod
    def test_class_instantiation(module_name: str, class_name: str) -> Dict[str, Any]:
        """测试类实例化"""
        try:
            module = __import__(module_name, fromlist=[class_name])
            cls = getattr(module, class_name)
            instance = cls()
            return {'success': True, 'message': f'类 {class_name} 实例化成功'}
        except Exception as e:
            return {'success': False, 'message': f'类 {class_name} 实例化失败: {str(e)}'}

class ConfigTestSuite:
    """配置测试套件"""

    @staticmethod
    def test_json_config(config_path: str) -> Dict[str, Any]:
        """测试JSON配置文件"""
        import os

        # 确保使用绝对路径
        if not os.path.isabs(config_path):
            project_root = '/Library/Application Support/Blackmagic Design/DaVinci Resolve/Fusion/Scripts/Utility/DaVinci AI Co-pilot Pro'
            config_path = os.path.join(project_root, config_path)

        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            return {'success': True, 'message': f'配置文件 {config_path} 加载成功，包含 {len(config)} 个配置项'}
        except FileNotFoundError:
            return {'success': False, 'message': f'配置文件 {config_path} 不存在'}
        except json.JSONDecodeError as e:
            return {'success': False, 'message': f'配置文件 {config_path} JSON格式错误: {str(e)}'}
        except Exception as e:
            return {'success': False, 'message': f'配置文件 {config_path} 加载异常: {str(e)}'}

    @staticmethod
    def test_env_file(env_path: str) -> Dict[str, Any]:
        """测试环境变量文件"""
        import os

        # 确保使用绝对路径
        if not os.path.isabs(env_path):
            project_root = '/Library/Application Support/Blackmagic Design/DaVinci Resolve/Fusion/Scripts/Utility/DaVinci AI Co-pilot Pro'
            env_path = os.path.join(project_root, env_path)

        try:
            if not os.path.exists(env_path):
                return {'success': False, 'message': f'环境文件 {env_path} 不存在'}

            with open(env_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            env_vars = [line.strip() for line in lines if line.strip() and not line.startswith('#')]
            return {'success': True, 'message': f'环境文件 {env_path} 包含 {len(env_vars)} 个环境变量'}
        except Exception as e:
            return {'success': False, 'message': f'环境文件 {env_path} 读取异常: {str(e)}'}

class ServiceTestSuite:
    """服务测试套件"""

    @staticmethod
    def test_service_adapter_init() -> Dict[str, Any]:
        """测试服务适配器初始化"""
        try:
            from src.services.direct_service_adapter import DirectServiceAdapter
            adapter = DirectServiceAdapter()
            return {'success': True, 'message': 'DirectServiceAdapter 初始化成功'}
        except Exception as e:
            return {'success': False, 'message': f'DirectServiceAdapter 初始化失败: {str(e)}'}

    @staticmethod
    def test_mcp_manager_init() -> Dict[str, Any]:
        """测试MCP管理器初始化"""
        try:
            from src.services.fastmcp_server import SimpleMCPManager
            manager = SimpleMCPManager()
            return {'success': True, 'message': 'SimpleMCPManager 初始化成功'}
        except Exception as e:
            return {'success': False, 'message': f'SimpleMCPManager 初始化失败: {str(e)}'}
