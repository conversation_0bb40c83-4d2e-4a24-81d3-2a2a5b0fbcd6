#!/bin/bash

# 自动化测试运行脚本

set -e

AUTOMATION_DIR="/Library/Application Support/Blackmagic Design/DaVinci Resolve/Fusion/Scripts/Utility/DaVinci AI Co-pilot Pro/refactor_scripts/testing/automation"

echo "🤖 DaVinci AI Co-pilot Pro 自动化测试"
echo "=================================="

# 检查参数
if [ $# -eq 0 ]; then
    echo "用法: $0 <test_type>"
    echo ""
    echo "可用的测试类型:"
    echo "  quick     - 快速健康检查"
    echo "  stage1    - 阶段1验证（架构清理）"
    echo "  stage2    - 阶段2验证（配置简化）"
    echo "  stage3    - 阶段3验证（API统一）"
    echo "  final     - 最终完整验证"
    echo ""
    exit 1
fi

TEST_TYPE=$1

cd "$AUTOMATION_DIR"

case $TEST_TYPE in
    "quick")
        echo "🚀 执行快速健康检查..."
        python3 quick_test.py
        ;;
    "stage1"|"stage2"|"stage3"|"final")
        echo "🧪 执行 $TEST_TYPE 验证..."
        python3 stage_validator.py $TEST_TYPE
        ;;
    *)
        echo "❌ 未知的测试类型: $TEST_TYPE"
        exit 1
        ;;
esac

echo ""
echo "✅ 测试完成！"
