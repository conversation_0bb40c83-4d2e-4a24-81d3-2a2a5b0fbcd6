#!/bin/bash

# 持续集成管道脚本
# 在每次代码更改后自动运行

set -e

PROJECT_ROOT="/Library/Application Support/Blackmagic Design/DaVinci Resolve/Fusion/Scripts/Utility/DaVinci AI Co-pilot Pro"
AUTOMATION_DIR="${PROJECT_ROOT}/refactor_scripts/testing/automation"

echo "🔄 DaVinci AI Co-pilot Pro 持续集成管道"
echo "======================================="

cd "$PROJECT_ROOT"

# 1. 检查Git状态
echo "📋 检查Git状态..."
if [ -d ".git" ]; then
    git status --porcelain
    if [ $? -ne 0 ]; then
        echo "⚠️  Git状态检查失败"
    fi
else
    echo "⚠️  未找到Git仓库"
fi

# 2. 运行快速测试
echo ""
echo "🚀 运行快速健康检查..."
bash "$AUTOMATION_DIR/run_tests.sh" quick

if [ $? -ne 0 ]; then
    echo "❌ 快速测试失败！停止CI管道"
    exit 1
fi

# 3. 检查代码质量（如果有工具）
echo ""
echo "🔍 检查代码质量..."
if command -v flake8 &> /dev/null; then
    echo "运行flake8检查..."
    flake8 src/ --max-line-length=120 --ignore=E501,W503 || echo "⚠️  代码风格检查发现问题"
else
    echo "⚠️  未安装flake8，跳过代码风格检查"
fi

# 4. 生成测试报告
echo ""
echo "📊 生成测试报告..."
REPORT_FILE="${PROJECT_ROOT}/refactor_scripts/testing/ci_report_$(date +%Y%m%d_%H%M%S).txt"

cat > "$REPORT_FILE" << REPORT_EOF
DaVinci AI Co-pilot Pro CI报告
============================

执行时间: $(date)
Git提交: $(git rev-parse HEAD 2>/dev/null || echo "未知")
Git分支: $(git branch --show-current 2>/dev/null || echo "未知")

测试结果: 通过 ✅

详细测试结果请查看:
- 快速测试: refactor_scripts/testing/quick_test_results.json

下一步建议:
- 继续开发或部署
- 运行更全面的测试套件
REPORT_EOF

echo "📄 CI报告已生成: $REPORT_FILE"

echo ""
echo "🎉 CI管道执行完成！"
