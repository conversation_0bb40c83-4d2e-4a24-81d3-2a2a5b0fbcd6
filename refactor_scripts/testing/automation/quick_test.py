#!/usr/bin/env python3
"""
快速验证脚本 - 用于快速检查系统状态
"""

import sys
import os
from test_framework import TestFramework, ModuleTestSuite, ConfigTestSuite, ServiceTestSuite

def quick_health_check():
    """快速健康检查"""
    framework = TestFramework("快速健康检查")

    print("🚀 执行快速健康检查...")

    # 核心模块检查
    framework.run_test("DirectServiceAdapter", ModuleTestSuite.test_import_module, "src.services.direct_service_adapter")
    framework.run_test("SimplifiedTypes", ModuleTestSuite.test_import_module, "src.types.simplified_types")

    # 配置文件检查
    framework.run_test("主配置文件", ConfigTestSuite.test_json_config, "config/config.json")
    framework.run_test("环境变量", ConfigTestSuite.test_env_file, ".env")

    # 服务初始化检查
    framework.run_test("服务适配器", ServiceTestSuite.test_service_adapter_init)

    # 保存结果
    output_file = '/Library/Application Support/Blackmagic Design/DaVinci Resolve/Fusion/Scripts/Utility/DaVinci AI Co-pilot Pro/refactor_scripts/testing/quick_test_results.json'
    framework.save_results(output_file)

    return framework.print_summary()

if __name__ == "__main__":
    success = quick_health_check()
    sys.exit(0 if success else 1)
