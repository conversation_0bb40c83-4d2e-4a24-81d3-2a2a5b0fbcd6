{"summary": {"test_suite": "阶段1验证 - 架构清理", "total_tests": 6, "passed": 6, "failed": 0, "success_rate": 100.0, "total_duration": 0.7888760566711426, "timestamp": "2025-07-27T19:20:41.247652"}, "results": [{"name": "DirectServiceAdapter导入", "passed": true, "message": "模块 src.services.direct_service_adapter 导入成功", "duration": 0.7870299816131592, "timestamp": "2025-07-27T19:20:40.458799", "error_details": null}, {"name": "SimplifiedTypes导入", "passed": true, "message": "模块 src.types.simplified_types 导入成功", "duration": 0.001650094985961914, "timestamp": "2025-07-27T19:20:41.245855", "error_details": null}, {"name": "FastMCP服务器导入", "passed": true, "message": "模块 src.services.fastmcp_server 导入成功", "duration": 7.152557373046875e-06, "timestamp": "2025-07-27T19:20:41.247529", "error_details": null}, {"name": "DirectServiceAdapter实例化", "passed": true, "message": "DirectServiceAdapter 初始化成功", "duration": 4.291534423828125e-06, "timestamp": "2025-07-27T19:20:41.247542", "error_details": null}, {"name": "MCP管理器实例化", "passed": true, "message": "SimpleMCPManager 初始化成功", "duration": 3.886222839355469e-05, "timestamp": "2025-07-27T19:20:41.247551", "error_details": null}, {"name": "验证删除的模块", "passed": true, "message": "enhanced_ai_services 已成功删除", "duration": 4.601478576660156e-05, "timestamp": "2025-07-27T19:20:41.247597", "error_details": null}]}