{"summary": {"test_suite": "快速健康检查", "total_tests": 5, "passed": 5, "failed": 0, "success_rate": 100.0, "total_duration": 0.5929088592529297, "timestamp": "2025-07-27T19:19:45.441947"}, "results": [{"name": "DirectServiceAdapter", "passed": true, "message": "模块 src.services.direct_service_adapter 导入成功", "duration": 0.5911519527435303, "timestamp": "2025-07-27T19:19:44.849060", "error_details": null}, {"name": "SimplifiedTypes", "passed": true, "message": "模块 src.types.simplified_types 导入成功", "duration": 0.00131988525390625, "timestamp": "2025-07-27T19:19:45.440235", "error_details": null}, {"name": "主配置文件", "passed": true, "message": "配置文件 /Library/Application Support/Blackmagic Design/DaVinci Resolve/Fusion/Scripts/Utility/DaVinci AI Co-pilot Pro/config/config.json 加载成功，包含 8 个配置项", "duration": 7.82012939453125e-05, "timestamp": "2025-07-27T19:19:45.441570", "error_details": null}, {"name": "环境变量", "passed": true, "message": "环境文件 /Library/Application Support/Blackmagic Design/DaVinci Resolve/Fusion/Scripts/Utility/DaVinci AI Co-pilot Pro/.env 包含 10 个环境变量", "duration": 0.00022983551025390625, "timestamp": "2025-07-27T19:19:45.441656", "error_details": null}, {"name": "服务适配器", "passed": true, "message": "DirectServiceAdapter 初始化成功", "duration": 5.7220458984375e-06, "timestamp": "2025-07-27T19:19:45.441931", "error_details": null}]}