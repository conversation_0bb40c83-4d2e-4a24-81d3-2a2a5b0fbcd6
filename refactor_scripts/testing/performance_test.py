#!/usr/bin/env python3
"""
性能测试脚本
测试重构后系统的性能表现
"""

import sys
import time
import asyncio
import json
from pathlib import Path
from typing import Dict, List, Any
import logging

# 添加项目路径
sys.path.insert(0, '.')

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class PerformanceTester:
    def __init__(self):
        self.results = {}
        self.baseline_file = Path('refactor_scripts/testing/performance_results.json')
        
    async def run_performance_tests(self):
        """运行性能测试套件"""
        print('🚀 DaVinci AI Co-pilot Pro 性能测试')
        print('=' * 50)
        
        # 测试1: 模块导入性能
        await self.test_import_performance()
        
        # 测试2: 类型系统性能
        await self.test_type_system_performance()
        
        # 测试3: 服务适配器性能
        await self.test_service_adapter_performance()
        
        # 测试4: API路由性能
        await self.test_api_routing_performance()
        
        # 测试5: 配置加载性能
        await self.test_config_loading_performance()
        
        # 生成性能报告
        self.generate_performance_report()
        
    async def test_import_performance(self):
        """测试模块导入性能"""
        print('\n📦 测试模块导入性能...')
        print('-' * 30)
        
        import_tests = [
            'src.services.simplified_types',
            'src.services.direct_service_adapter',
            'src.services.simplified_ai_services',
            'src.api.unified_routes',
            'src.core.unified_config'
        ]
        
        import_times = {}
        
        for module_name in import_tests:
            start_time = time.perf_counter()
            try:
                # 清除模块缓存以获得真实导入时间
                if module_name in sys.modules:
                    del sys.modules[module_name]
                
                __import__(module_name)
                import_time = (time.perf_counter() - start_time) * 1000
                import_times[module_name] = import_time
                
                print(f'  ✅ {module_name}: {import_time:.2f}ms')
                
            except Exception as e:
                print(f'  ❌ {module_name}: 导入失败 - {e}')
                import_times[module_name] = -1
                
        self.results['import_performance'] = import_times
        
        # 计算总导入时间
        total_time = sum(t for t in import_times.values() if t > 0)
        print(f'\\n📊 总导入时间: {total_time:.2f}ms')
        
    async def test_type_system_performance(self):
        """测试类型系统性能"""
        print('\\n🏷️  测试类型系统性能...')
        print('-' * 30)
        
        try:
            from src.services.simplified_types import ServiceRequest, ServiceCapability, ServiceProvider
            
            # 测试枚举访问性能
            start_time = time.perf_counter()
            
            for _ in range(1000):
                capabilities = list(ServiceCapability)
                providers = list(ServiceProvider)
                
            enum_time = (time.perf_counter() - start_time) * 1000
            
            # 测试对象创建性能
            start_time = time.perf_counter()
            
            for i in range(100):
                request = ServiceRequest(
                    content=f'test input {i}',
                    capability=ServiceCapability.TEXT_GENERATION,
                    provider=ServiceProvider.DEEPSEEK,
                    parameters={'test': True}
                )
                
            creation_time = (time.perf_counter() - start_time) * 1000
            
            self.results['type_system_performance'] = {
                'enum_access_1000x': enum_time,
                'object_creation_100x': creation_time
            }
            
            print(f'  ✅ 枚举访问 (1000次): {enum_time:.2f}ms')
            print(f'  ✅ 对象创建 (100次): {creation_time:.2f}ms')
            
        except Exception as e:
            print(f'  ❌ 类型系统性能测试失败: {e}')
            self.results['type_system_performance'] = {'error': str(e)}
            
    async def test_service_adapter_performance(self):
        """测试服务适配器性能"""
        print('\\n🔧 测试服务适配器性能...')
        print('-' * 35)
        
        try:
            from src.services.direct_service_adapter import DirectServiceAdapter
            from src.services.simplified_types import ServiceRequest, ServiceCapability, ServiceProvider
            
            # 测试适配器初始化性能
            start_time = time.perf_counter()
            adapter = DirectServiceAdapter()
            init_time = (time.perf_counter() - start_time) * 1000
            
            # 测试请求处理准备性能（不实际调用API）
            start_time = time.perf_counter()
            
            for i in range(50):
                request = ServiceRequest(
                    content=f'performance test {i}',
                    capability=ServiceCapability.TEXT_GENERATION,
                    provider=ServiceProvider.DEEPSEEK,
                    parameters={'max_tokens': 100}
                )
                # 只测试请求准备，不实际处理
                
            preparation_time = (time.perf_counter() - start_time) * 1000
            
            self.results['service_adapter_performance'] = {
                'initialization': init_time,
                'request_preparation_50x': preparation_time
            }
            
            print(f'  ✅ 适配器初始化: {init_time:.2f}ms')
            print(f'  ✅ 请求准备 (50次): {preparation_time:.2f}ms')
            
        except Exception as e:
            print(f'  ❌ 服务适配器性能测试失败: {e}')
            self.results['service_adapter_performance'] = {'error': str(e)}
            
    async def test_api_routing_performance(self):
        """测试API路由性能"""
        print('\\n🛣️  测试API路由性能...')
        print('-' * 25)
        
        try:
            from src.api.unified_routes import router
            
            # 测试路由加载性能
            start_time = time.perf_counter()
            
            # 统计路由数量
            route_count = 0
            for route in router.routes:
                if hasattr(route, 'path'):
                    route_count += 1
                    
            routing_time = (time.perf_counter() - start_time) * 1000
            
            self.results['api_routing_performance'] = {
                'route_loading': routing_time,
                'route_count': route_count
            }
            
            print(f'  ✅ 路由加载: {routing_time:.2f}ms')
            print(f'  ✅ 路由数量: {route_count}')
            
        except Exception as e:
            print(f'  ❌ API路由性能测试失败: {e}')
            self.results['api_routing_performance'] = {'error': str(e)}
            
    async def test_config_loading_performance(self):
        """测试配置加载性能"""
        print('\\n⚙️  测试配置加载性能...')
        print('-' * 30)
        
        try:
            from src.core.unified_config import ConfigManager
            
            # 测试配置管理器初始化性能
            start_time = time.perf_counter()
            config_manager = ConfigManager()
            init_time = (time.perf_counter() - start_time) * 1000
            
            # 测试配置访问性能
            start_time = time.perf_counter()
            
            for _ in range(100):
                # 模拟配置访问
                try:
                    config_data = config_manager._config or {}
                except:
                    config_data = {}
                    
            access_time = (time.perf_counter() - start_time) * 1000
            
            self.results['config_loading_performance'] = {
                'initialization': init_time,
                'access_100x': access_time
            }
            
            print(f'  ✅ 配置初始化: {init_time:.2f}ms')
            print(f'  ✅ 配置访问 (100次): {access_time:.2f}ms')
            
        except Exception as e:
            print(f'  ❌ 配置加载性能测试失败: {e}')
            self.results['config_loading_performance'] = {'error': str(e)}
            
    def generate_performance_report(self):
        """生成性能报告"""
        print('\\n📊 性能测试报告')
        print('=' * 30)
        
        # 保存结果到文件
        with open(self.baseline_file, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False)
            
        # 计算总体性能指标
        total_metrics = {}
        
        for category, metrics in self.results.items():
            if isinstance(metrics, dict) and 'error' not in metrics:
                for metric_name, value in metrics.items():
                    if isinstance(value, (int, float)) and value > 0:
                        total_metrics[f'{category}_{metric_name}'] = value
                        
        if total_metrics:
            avg_time = sum(total_metrics.values()) / len(total_metrics)
            max_time = max(total_metrics.values())
            min_time = min(total_metrics.values())
            
            print(f'\\n📈 性能统计:')
            print(f'  - 测试项目: {len(total_metrics)}')
            print(f'  - 平均耗时: {avg_time:.2f}ms')
            print(f'  - 最大耗时: {max_time:.2f}ms')
            print(f'  - 最小耗时: {min_time:.2f}ms')
            
            # 性能评级
            if avg_time < 10:
                grade = '🚀 优秀'
            elif avg_time < 50:
                grade = '✅ 良好'
            elif avg_time < 100:
                grade = '⚠️  一般'
            else:
                grade = '❌ 需要优化'
                
            print(f'  - 性能评级: {grade}')
            
        # 显示详细结果
        print('\\n📋 详细性能数据:')
        for category, metrics in self.results.items():
            print(f'\\n  {category.replace("_", " ").title()}:')
            if isinstance(metrics, dict):
                if 'error' in metrics:
                    print(f'    ❌ 错误: {metrics["error"]}')
                else:
                    for metric, value in metrics.items():
                        if isinstance(value, (int, float)):
                            print(f'    - {metric}: {value:.2f}ms')
                        else:
                            print(f'    - {metric}: {value}')
                            
        print(f'\\n💾 性能数据已保存到: {self.baseline_file}')
        
        # 性能优化建议
        self.generate_optimization_suggestions()
        
    def generate_optimization_suggestions(self):
        """生成性能优化建议"""
        print('\\n💡 性能优化建议:')
        print('-' * 25)
        
        suggestions = []
        
        # 检查导入性能
        if 'import_performance' in self.results:
            import_times = self.results['import_performance']
            slow_imports = [name for name, time in import_times.items() 
                          if isinstance(time, (int, float)) and time > 50]
            
            if slow_imports:
                suggestions.append(f'优化慢速导入模块: {", ".join(slow_imports)}')
                
        # 检查类型系统性能
        if 'type_system_performance' in self.results:
            type_metrics = self.results['type_system_performance']
            if 'object_creation_100x' in type_metrics and type_metrics['object_creation_100x'] > 100:
                suggestions.append('考虑优化对象创建性能，可能使用对象池')
                
        # 检查服务适配器性能
        if 'service_adapter_performance' in self.results:
            adapter_metrics = self.results['service_adapter_performance']
            if 'initialization' in adapter_metrics and adapter_metrics['initialization'] > 100:
                suggestions.append('考虑延迟初始化服务适配器组件')
                
        if suggestions:
            for i, suggestion in enumerate(suggestions, 1):
                print(f'  {i}. {suggestion}')
        else:
            print('  ✅ 当前性能表现良好，无需特别优化')

async def main():
    """主函数"""
    tester = PerformanceTester()
    await tester.run_performance_tests()
    print('\\n🎉 性能测试完成！')

if __name__ == "__main__":
    asyncio.run(main())
