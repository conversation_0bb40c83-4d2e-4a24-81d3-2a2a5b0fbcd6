#!/usr/bin/env python3
"""
DaVinci AI Co-pilot Pro 功能基线测试
测试所有核心功能，记录当前工作状态
"""

import sys
import os
import json
import asyncio
import traceback
from datetime import datetime

# 添加项目路径
sys.path.insert(0, '/Library/Application Support/Blackmagic Design/DaVinci Resolve/Fusion/Scripts/Utility/DaVinci AI Co-pilot Pro')

class BaselineTester:
    def __init__(self):
        self.results = {
            'test_time': datetime.now().isoformat(),
            'tests': {},
            'summary': {
                'total': 0,
                'passed': 0,
                'failed': 0,
                'errors': 0
            }
        }

    def test_import_modules(self):
        """测试核心模块导入"""
        print("🔍 测试模块导入...")

        modules_to_test = [
            'src.services.direct_service_adapter',
            'src.services.simplified_ai_services',
            'src.services.fastmcp_server',
            'src.services.simplified_types',
            'src.api.routes'
        ]

        for module in modules_to_test:
            try:
                __import__(module)
                self.record_test(f"import_{module}", True, "模块导入成功")
                print(f"  ✅ {module}")
            except Exception as e:
                self.record_test(f"import_{module}", False, f"导入失败: {str(e)}")
                print(f"  ❌ {module}: {str(e)}")

    def test_config_loading(self):
        """测试配置加载"""
        print("⚙️  测试配置加载...")

        config_files = [
            'config/unified_config.json',
            '.env'
        ]

        for config_file in config_files:
            try:
                if config_file.endswith('.json'):
                    with open(config_file, 'r', encoding='utf-8') as f:
                        json.load(f)
                    self.record_test(f"config_{config_file}", True, "配置文件加载成功")
                    print(f"  ✅ {config_file}")
                elif config_file == '.env':
                    if os.path.exists(config_file):
                        with open(config_file, 'r') as f:
                            content = f.read()
                        self.record_test(f"config_{config_file}", True, f"环境文件存在，{len(content.splitlines())}行")
                        print(f"  ✅ {config_file}")
                    else:
                        self.record_test(f"config_{config_file}", False, "环境文件不存在")
                        print(f"  ❌ {config_file}: 文件不存在")
            except Exception as e:
                self.record_test(f"config_{config_file}", False, f"加载失败: {str(e)}")
                print(f"  ❌ {config_file}: {str(e)}")

    def test_service_adapters(self):
        """测试服务适配器"""
        print("🔧 测试服务适配器...")

        try:
            from src.services.direct_service_adapter import DirectServiceAdapter
            adapter = DirectServiceAdapter()
            self.record_test("direct_service_adapter_init", True, "DirectServiceAdapter初始化成功")
            print("  ✅ DirectServiceAdapter 初始化")
        except Exception as e:
            self.record_test("direct_service_adapter_init", False, f"初始化失败: {str(e)}")
            print(f"  ❌ DirectServiceAdapter: {str(e)}")

        try:
            from src.services.simplified_ai_services import SimplifiedAIServiceManager
            services = SimplifiedAIServiceManager()
            self.record_test("simplified_ai_services_init", True, "SimplifiedAIServiceManager初始化成功")
            print("  ✅ SimplifiedAIServiceManager 初始化")
        except Exception as e:
            self.record_test("simplified_ai_services_init", False, f"初始化失败: {str(e)}")
            print(f"  ❌ SimplifiedAIServiceManager: {str(e)}")

    def test_mcp_connections(self):
        """测试MCP连接"""
        print("🌐 测试MCP连接...")

        try:
            from src.services.fastmcp_server import SimpleMCPManager
            mcp_manager = SimpleMCPManager()
            self.record_test("mcp_manager_init", True, "MCP管理器初始化成功")
            print("  ✅ MCP管理器 初始化")

            # 测试MCP服务器配置
            if hasattr(mcp_manager, 'servers'):
                server_count = len(mcp_manager.servers)
                self.record_test("mcp_servers_config", True, f"配置了{server_count}个MCP服务器")
                print(f"  ✅ MCP服务器配置: {server_count}个")
            else:
                self.record_test("mcp_servers_config", False, "未找到MCP服务器配置")
                print("  ❌ MCP服务器配置: 未找到")

        except Exception as e:
            self.record_test("mcp_manager_init", False, f"初始化失败: {str(e)}")
            print(f"  ❌ MCP管理器: {str(e)}")

    def test_api_routes(self):
        """测试API路由"""
        print("🛣️  测试API路由...")

        try:
            from src.api.routes import app
            self.record_test("api_routes_import", True, "API路由导入成功")
            print("  ✅ API路由 导入")

            # 检查路由数量
            if hasattr(app, 'routes'):
                route_count = len(app.routes)
                self.record_test("api_routes_count", True, f"发现{route_count}个路由")
                print(f"  ✅ API路由数量: {route_count}")
            else:
                self.record_test("api_routes_count", False, "无法获取路由信息")
                print("  ❌ API路由数量: 无法获取")

        except Exception as e:
            self.record_test("api_routes_import", False, f"导入失败: {str(e)}")
            print(f"  ❌ API路由: {str(e)}")

    def test_type_system(self):
        """测试类型系统"""
        print("📝 测试类型系统...")

        try:
            from src.services.simplified_types import ServiceCapability, ServiceProvider, ServiceRequest, ServiceResponse

            # 测试枚举
            capabilities = list(ServiceCapability)
            providers = list(ServiceProvider)

            self.record_test("type_system_simplified", True,
                           f"简化类型系统: {len(capabilities)}个能力, {len(providers)}个提供商")
            print(f"  ✅ 简化类型系统: {len(capabilities)}个能力, {len(providers)}个提供商")

        except Exception as e:
            self.record_test("type_system_simplified", False, f"简化类型系统失败: {str(e)}")
            print(f"  ❌ 简化类型系统: {str(e)}")

        try:
            # 测试ServiceRequest创建
            request = ServiceRequest(
                capability=ServiceCapability.TEXT_GENERATION,
                provider=ServiceProvider.DEEPSEEK,
                content="test",
                parameters={},
                metadata={}
            )
            self.record_test("type_system_request", True, "ServiceRequest创建成功")
            print("  ✅ ServiceRequest创建")
        except Exception as e:
            self.record_test("type_system_request", False, f"ServiceRequest创建失败: {str(e)}")
            print(f"  ❌ ServiceRequest创建: {str(e)}")

    def record_test(self, test_name, passed, message):
        """记录测试结果"""
        self.results['tests'][test_name] = {
            'passed': passed,
            'message': message,
            'timestamp': datetime.now().isoformat()
        }

        self.results['summary']['total'] += 1
        if passed:
            self.results['summary']['passed'] += 1
        else:
            self.results['summary']['failed'] += 1

    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始功能基线测试...\n")

        try:
            self.test_import_modules()
            print()

            self.test_config_loading()
            print()

            self.test_service_adapters()
            print()

            self.test_mcp_connections()
            print()

            self.test_api_routes()
            print()

            self.test_type_system()
            print()

        except Exception as e:
            print(f"🚨 测试过程中发生错误: {str(e)}")
            traceback.print_exc()
            self.results['summary']['errors'] += 1

        # 保存结果
        self.save_results()
        self.print_summary()

    def save_results(self):
        """保存测试结果"""
        output_file = '/Library/Application Support/Blackmagic Design/DaVinci Resolve/Fusion/Scripts/Utility/DaVinci AI Co-pilot Pro/refactor_scripts/testing/baseline_results.json'

        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False)

        print(f"💾 测试结果已保存: {output_file}")

    def print_summary(self):
        """打印测试摘要"""
        summary = self.results['summary']

        print("\n" + "="*50)
        print("📊 功能基线测试摘要")
        print("="*50)
        print(f"总测试数: {summary['total']}")
        print(f"通过: {summary['passed']} ✅")
        print(f"失败: {summary['failed']} ❌")
        print(f"错误: {summary['errors']} 🚨")

        success_rate = (summary['passed'] / summary['total'] * 100) if summary['total'] > 0 else 0
        print(f"成功率: {success_rate:.1f}%")

        if summary['failed'] > 0 or summary['errors'] > 0:
            print("\n⚠️  发现问题，建议在重构前先修复这些问题")
        else:
            print("\n🎉 所有测试通过，可以安全开始重构！")

if __name__ == "__main__":
    tester = BaselineTester()
    tester.run_all_tests()
