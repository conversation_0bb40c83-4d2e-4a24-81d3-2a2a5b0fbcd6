#!/usr/bin/env python3
"""
分析现有配置文件的结构和重复内容
"""

import json
import os
from pathlib import Path
from collections import defaultdict

def analyze_config_files():
    """分析配置文件"""
    project_root = Path('/Library/Application Support/Blackmagic Design/DaVinci Resolve/Fusion/Scripts/Utility/DaVinci AI Co-pilot Pro')
    config_dir = project_root / 'config'

    configs = {}
    all_keys = defaultdict(list)

    print("📊 配置文件分析结果:")
    print("=" * 50)

    # 读取所有配置文件
    for config_file in config_dir.glob('*.json'):
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)

            configs[config_file.name] = config_data

            # 收集所有键
            def collect_keys(data, prefix=''):
                if isinstance(data, dict):
                    for key, value in data.items():
                        full_key = f"{prefix}.{key}" if prefix else key
                        all_keys[full_key].append(config_file.name)
                        if isinstance(value, dict):
                            collect_keys(value, full_key)

            collect_keys(config_data)

            print(f"✅ {config_file.name}: {len(config_data)} 个顶级配置项")

        except Exception as e:
            print(f"❌ {config_file.name}: 读取失败 - {e}")

    print("\n🔍 重复配置项分析:")
    print("-" * 30)

    # 查找重复的配置项
    duplicates = {k: v for k, v in all_keys.items() if len(v) > 1}

    if duplicates:
        for key, files in duplicates.items():
            print(f"  🔄 {key}: 出现在 {', '.join(files)}")
    else:
        print("  ✅ 未发现重复配置项")

    print(f"\n📈 统计信息:")
    print(f"  - 配置文件总数: {len(configs)}")
    print(f"  - 配置项总数: {len(all_keys)}")
    print(f"  - 重复配置项: {len(duplicates)}")

    # 生成合并建议
    print(f"\n💡 合并建议:")

    # 按功能分类配置文件
    categories = {
        'core': ['config.json'],
        'mcp': ['mcp_enhanced.json'],
        'services': ['simplified_service_config.json'],
        'dynamic': ['dynamic_types.json']
    }

    for category, files in categories.items():
        existing_files = [f for f in files if f in configs]
        if existing_files:
            print(f"  📁 {category}类配置: {', '.join(existing_files)}")

    return configs, duplicates

if __name__ == "__main__":
    analyze_config_files()
