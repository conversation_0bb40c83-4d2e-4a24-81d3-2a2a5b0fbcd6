# 🎬 DaVinci AI Co-pilot Pro 部署指南

## 📋 目录
1. [环境准备](#环境准备)
2. [插件部署](#插件部署)
3. [功能验证](#功能验证)
4. [实际工作流程](#实际工作流程)
5. [故障排除](#故障排除)

---

## 🔧 环境准备

### 1. DaVinci Resolve 版本要求

#### **支持的版本**
- ✅ **DaVinci Resolve 18.x** (推荐)
- ✅ **DaVinci Resolve 19.x** (最新)
- ❌ DaVinci Resolve 17.x 及以下版本不支持

#### **版本检查方法**
```bash
# 在DaVinci Resolve中
# 菜单: DaVinci Resolve > About DaVinci Resolve
# 或者快捷键: Cmd+, (Mac) / Ctrl+, (Windows)
```

#### **下载和安装**
1. 访问 [Blackmagic Design官网](https://www.blackmagicdesign.com/products/davinciresolve)
2. 下载最新版本的DaVinci Resolve
3. 安装时选择完整安装（包含Python API支持）

### 2. Python环境配置

#### **Python版本要求**
```bash
# 检查Python版本
python3 --version
# 要求: Python 3.8+ (推荐 Python 3.9-3.11)
```

#### **虚拟环境创建**
```bash
# 创建虚拟环境
python3 -m venv davinci_ai_env

# 激活虚拟环境
# macOS/Linux:
source davinci_ai_env/bin/activate
# Windows:
davinci_ai_env\Scripts\activate
```

#### **依赖包安装**
```bash
# 进入项目目录
cd "DaVinci AI Co-pilot Pro"

# 安装基础依赖
pip install -r requirements.txt

# 安装扩展功能依赖（可选）
pip install moviepy pydub Pillow opencv-python pandas numpy jieba psutil
```

### 3. DaVinci Resolve Python API 启用

#### **macOS 配置**
```bash
# 1. 找到DaVinci Resolve安装路径
RESOLVE_PATH="/Applications/DaVinci Resolve/DaVinci Resolve.app"

# 2. 添加Python路径到环境变量
echo 'export RESOLVE_SCRIPT_API="/Applications/DaVinci Resolve/DaVinci Resolve.app/Contents/Libraries/Fusion/fusionscript.so"' >> ~/.zshrc
echo 'export RESOLVE_SCRIPT_LIB="/Applications/DaVinci Resolve/DaVinci Resolve.app/Contents/Libraries/Fusion/python"' >> ~/.zshrc

# 3. 重新加载环境变量
source ~/.zshrc
```

#### **Windows 配置**
```cmd
# 1. 找到DaVinci Resolve安装路径（通常在）
# C:\Program Files\Blackmagic Design\DaVinci Resolve\

# 2. 添加环境变量
setx RESOLVE_SCRIPT_API "C:\Program Files\Blackmagic Design\DaVinci Resolve\fusionscript.dll"
setx RESOLVE_SCRIPT_LIB "C:\Program Files\Blackmagic Design\DaVinci Resolve\python"

# 3. 重启命令行窗口
```

#### **Linux 配置**
```bash
# 1. 找到DaVinci Resolve安装路径
RESOLVE_PATH="/opt/resolve"

# 2. 添加Python路径
echo 'export RESOLVE_SCRIPT_API="/opt/resolve/libs/Fusion/fusionscript.so"' >> ~/.bashrc
echo 'export RESOLVE_SCRIPT_LIB="/opt/resolve/libs/Fusion/python"' >> ~/.bashrc

# 3. 重新加载
source ~/.bashrc
```

### 4. 系统权限配置

#### **macOS 权限设置**
```bash
# 1. 给予DaVinci Resolve完全磁盘访问权限
# 系统偏好设置 > 安全性与隐私 > 隐私 > 完全磁盘访问权限
# 添加: DaVinci Resolve.app

# 2. 给予Python脚本执行权限
chmod +x run.sh
chmod +x setup.sh

# 3. 允许网络连接（如果有防火墙）
# 系统偏好设置 > 安全性与隐私 > 防火墙 > 防火墙选项
# 允许: Python, DaVinci Resolve
```

#### **Windows 权限设置**
```cmd
# 1. 以管理员身份运行命令行
# 右键点击 "命令提示符" > "以管理员身份运行"

# 2. 设置执行策略（如果使用PowerShell）
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser

# 3. 防火墙设置
# Windows Defender 防火墙 > 允许应用通过防火墙
# 添加: Python.exe, DaVinci Resolve.exe
```

---

## 🚀 插件部署

### 1. 文件部署位置

#### **DaVinci Resolve Scripts 目录**
```bash
# macOS:
SCRIPTS_DIR="$HOME/Library/Application Support/Blackmagic Design/DaVinci Resolve/Fusion/Scripts"

# Windows:
SCRIPTS_DIR="%APPDATA%\Blackmagic Design\DaVinci Resolve\Support\Fusion\Scripts"

# Linux:
SCRIPTS_DIR="$HOME/.local/share/DaVinciResolve/Fusion/Scripts"
```

#### **创建插件目录结构**
```bash
# 创建目录结构
mkdir -p "$SCRIPTS_DIR/Utility/DaVinci AI Co-pilot Pro"

# 复制项目文件
cp -r "DaVinci AI Co-pilot Pro"/* "$SCRIPTS_DIR/Utility/DaVinci AI Co-pilot Pro/"
```

### 2. 配置文件设置

#### **更新配置文件**
```bash
# 编辑配置文件
nano "$SCRIPTS_DIR/Utility/DaVinci AI Co-pilot Pro/config/config.json"
```

#### **关键配置项**
```json
{
  "davinci": {
    "resolve_path": "/Applications/DaVinci Resolve/DaVinci Resolve.app",
    "project_manager_timeout": 30,
    "media_pool_timeout": 30,
    "timeline_timeout": 30,
    "auto_save": true,
    "backup_projects": true
  },
  "app": {
    "host": "127.0.0.1",
    "port": 8000,
    "debug": false
  }
}
```

### 3. 插件注册和激活

#### **在DaVinci Resolve中注册**
1. **启动DaVinci Resolve**
2. **打开Fusion页面**
3. **访问Scripts菜单**:
   - 菜单: `Workspace > Scripts > Utility > DaVinci AI Co-pilot Pro`
4. **运行启动脚本**:
   - 选择: `run.sh` (macOS/Linux) 或 `run.bat` (Windows)

#### **验证插件加载**
```python
# 在DaVinci Resolve的Console中运行
import DaVinciResolveScript as dvr_script
resolve = dvr_script.scriptapp("Resolve")
print(f"DaVinci Resolve API Version: {resolve.GetVersion()}")
```

### 4. 服务启动脚本

#### **创建启动脚本 (macOS/Linux)**
```bash
#!/bin/bash
# 文件: start_davinci_ai.sh

# 设置工作目录
SCRIPT_DIR="$HOME/Library/Application Support/Blackmagic Design/DaVinci Resolve/Fusion/Scripts/Utility/DaVinci AI Co-pilot Pro"
cd "$SCRIPT_DIR"

# 激活虚拟环境
source davinci_ai_env/bin/activate

# 启动服务
python src/main.py

echo "DaVinci AI Co-pilot Pro 已启动在 http://127.0.0.1:8000"
```

#### **创建启动脚本 (Windows)**
```batch
@echo off
REM 文件: start_davinci_ai.bat

REM 设置工作目录
set SCRIPT_DIR=%APPDATA%\Blackmagic Design\DaVinci Resolve\Support\Fusion\Scripts\Utility\DaVinci AI Co-pilot Pro
cd /d "%SCRIPT_DIR%"

REM 激活虚拟环境
call davinci_ai_env\Scripts\activate

REM 启动服务
python src\main.py

echo DaVinci AI Co-pilot Pro 已启动在 http://127.0.0.1:8000
pause
```

---

## ✅ 功能验证

### 1. 连接状态测试

#### **基础连接测试**
```python
# 测试脚本: test_connection.py
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.davinci.resolve_api import resolve_api

async def test_connection():
    """测试DaVinci Resolve连接"""
    print("🔍 测试DaVinci Resolve连接...")
    
    # 初始化连接
    success = await resolve_api.initialize()
    
    if success:
        print("✅ 连接成功!")
        
        # 获取版本信息
        version = resolve_api.get_version()
        print(f"📋 DaVinci Resolve版本: {version}")
        
        # 获取项目列表
        projects = await resolve_api.get_project_list()
        print(f"📁 项目数量: {len(projects)}")
        
        return True
    else:
        print("❌ 连接失败!")
        print(f"错误信息: {resolve_api._last_error}")
        return False

if __name__ == "__main__":
    import asyncio
    asyncio.run(test_connection())
```

#### **运行连接测试**
```bash
# 确保DaVinci Resolve正在运行
# 然后执行测试
python test_connection.py

### 2. 功能模块测试

#### **静帧捕获测试**
```python
# 测试脚本: test_frame_capture.py
async def test_frame_capture():
    """测试静帧捕获功能"""
    print("📸 测试静帧捕获功能...")

    # 检查静帧功能状态
    status_response = await resolve_api.get_frame_export_status()
    print(f"静帧功能状态: {status_response}")

    if status_response.get('success'):
        # 导出当前帧
        frame_info = await resolve_api.export_current_frame(
            output_dir="./output/frames",
            filename="test_frame.png"
        )
        print(f"✅ 静帧导出成功: {frame_info.file_path}")

        # 导出给AI使用
        ai_frame_path = await resolve_api.export_frame_for_ai("image_generation")
        print(f"✅ AI静帧导出成功: {ai_frame_path}")
    else:
        print("❌ 静帧功能不可用")

# 运行测试
asyncio.run(test_frame_capture())
```

#### **AI深度集成测试**
```python
# 测试脚本: test_ai_integration.py
async def test_ai_integration():
    """测试AI深度集成功能"""
    print("🤖 测试AI深度集成功能...")

    # 测试内容分析
    analysis_result = await resolve_api.analyze_timeline_content(
        analysis_type="comprehensive",
        confidence_threshold=0.7,
        auto_apply=False
    )
    print(f"内容分析结果: {analysis_result}")

    # 测试字幕生成
    subtitle_result = await resolve_api.generate_ai_subtitles(
        track_index=1,
        language="zh-CN",
        ai_service="deepseek"
    )
    print(f"字幕生成结果: {subtitle_result}")

    # 测试音频提取
    audio_path = await resolve_api.extract_audio_for_ai(track_index=1)
    print(f"音频提取路径: {audio_path}")

# 运行测试
asyncio.run(test_ai_integration())
```

#### **Web界面测试**
```bash
# 1. 启动Web服务
python src/main.py

# 2. 打开浏览器访问
open http://127.0.0.1:8000

# 3. 测试各个功能模块
# - 点击"DaVinci集成"标签
# - 测试"连接DaVinci"按钮
# - 测试"捕获当前帧"功能
# - 测试AI深度集成各个标签页
```

### 3. 常见问题排查

#### **连接问题排查**
```python
# 诊断脚本: diagnose_connection.py
def diagnose_davinci_connection():
    """诊断DaVinci Resolve连接问题"""
    print("🔍 诊断DaVinci Resolve连接...")

    # 1. 检查DaVinci Resolve是否运行
    import psutil
    davinci_running = False
    for proc in psutil.process_iter(['pid', 'name']):
        if 'resolve' in proc.info['name'].lower():
            davinci_running = True
            print(f"✅ 发现DaVinci Resolve进程: {proc.info['name']} (PID: {proc.info['pid']})")
            break

    if not davinci_running:
        print("❌ DaVinci Resolve未运行，请先启动DaVinci Resolve")
        return False

    # 2. 检查Python API模块
    try:
        import DaVinciResolveScript as dvr_script
        print("✅ DaVinciResolveScript模块导入成功")
    except ImportError as e:
        print(f"❌ DaVinciResolveScript模块导入失败: {e}")
        print("解决方案:")
        print("1. 确认DaVinci Resolve版本支持Python API")
        print("2. 检查环境变量RESOLVE_SCRIPT_API和RESOLVE_SCRIPT_LIB")
        print("3. 重新安装DaVinci Resolve并选择完整安装")
        return False

    # 3. 测试API连接
    try:
        resolve = dvr_script.scriptapp("Resolve")
        if resolve:
            version = resolve.GetVersion()
            print(f"✅ API连接成功，版本: {version}")
            return True
        else:
            print("❌ 无法获取Resolve对象")
            return False
    except Exception as e:
        print(f"❌ API连接失败: {e}")
        return False

# 运行诊断
diagnose_davinci_connection()
```

#### **权限问题解决**
```bash
# macOS权限修复
sudo chmod -R 755 "$HOME/Library/Application Support/Blackmagic Design"
sudo chown -R $(whoami) "$HOME/Library/Application Support/Blackmagic Design"

# 重置DaVinci Resolve偏好设置（如果需要）
rm -rf "$HOME/Library/Application Support/Blackmagic Design/DaVinci Resolve/preferences"
```

#### **端口冲突解决**
```python
# 检查端口占用
import socket

def check_port(port):
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    result = sock.connect_ex(('127.0.0.1', port))
    sock.close()
    return result == 0

# 检查默认端口8000
if check_port(8000):
    print("❌ 端口8000被占用")
    print("解决方案: 修改config/config.json中的端口号")
else:
    print("✅ 端口8000可用")
```

---

## 🎯 实际工作流程

### 1. 项目初始化工作流

#### **步骤1: 启动环境**
```bash
# 1. 启动DaVinci Resolve
open "/Applications/DaVinci Resolve/DaVinci Resolve.app"

# 2. 等待DaVinci Resolve完全加载

# 3. 启动AI Co-pilot Pro
cd "$HOME/Library/Application Support/Blackmagic Design/DaVinci Resolve/Fusion/Scripts/Utility/DaVinci AI Co-pilot Pro"
./start_davinci_ai.sh

# 4. 打开Web界面
open http://127.0.0.1:8000
```

#### **步骤2: 建立连接**
```javascript
// 在Web界面中
1. 点击"DaVinci集成"标签
2. 点击"连接DaVinci"按钮
3. 确认连接状态显示为"已连接"
4. 检查项目列表是否正常加载
```

#### **步骤3: 项目设置**
```python
# 通过API或Web界面
1. 创建新项目或加载现有项目
2. 设置项目参数（分辨率、帧率等）
3. 导入媒体文件到媒体池
4. 创建时间线
```

### 2. 自动化剪辑工作流

#### **智能内容分析**
```python
# 工作流程示例
async def automated_editing_workflow():
    """自动化剪辑工作流程"""

    # 1. 分析时间线内容
    print("🔍 分析时间线内容...")
    analysis_result = await resolve_api.analyze_timeline_content(
        analysis_type="comprehensive",
        confidence_threshold=0.8,
        auto_apply=True
    )

    # 2. 添加智能标记
    print("🏷️ 添加智能标记...")
    markers = await resolve_api.add_smart_markers([
        {
            "frame_number": 100,
            "marker_type": "scene_change",
            "confidence": 0.9,
            "description": "场景切换点",
            "color": "Red"
        }
    ])

    # 3. 生成字幕
    print("📝 生成字幕...")
    subtitles = await resolve_api.generate_ai_subtitles(
        track_index=1,
        language="zh-CN",
        ai_service="deepseek"
    )

    # 4. 导出静帧用于AI分析
    print("📸 导出关键帧...")
    key_frames = []
    for i in range(0, 1000, 100):  # 每100帧导出一次
        frame_path = await resolve_api.export_frame_for_ai("image_generation")
        key_frames.append(frame_path)

    return {
        "analysis": analysis_result,
        "markers": markers,
        "subtitles": subtitles,
        "key_frames": key_frames
    }
```

#### **批量处理工作流**
```python
# 批量处理多个项目
async def batch_processing_workflow(project_names):
    """批量处理工作流程"""
    results = []

    for project_name in project_names:
        print(f"🎬 处理项目: {project_name}")

        # 加载项目
        await resolve_api.load_project(project_name)

        # 执行自动化处理
        result = await automated_editing_workflow()
        results.append({
            "project": project_name,
            "result": result
        })

        # 保存项目
        await resolve_api.save_project()

    return results
```

### 3. AI辅助创作工作流

#### **视频内容增强**
```python
async def content_enhancement_workflow():
    """内容增强工作流程"""

    # 1. 提取音频进行分析
    audio_path = await resolve_api.extract_audio_for_ai(track_index=1)

    # 2. 基于音频生成字幕
    subtitles = await resolve_api.generate_ai_subtitles(
        track_index=1,
        language="zh-CN"
    )

    # 3. 分析视频内容
    content_analysis = await resolve_api.analyze_timeline_content(
        analysis_type="emotion_analysis"
    )

    # 4. 根据分析结果添加效果
    if content_analysis.get("dominant_emotion") == "happy":
        # 添加明亮的色彩校正
        pass
    elif content_analysis.get("dominant_emotion") == "sad":
        # 添加冷色调效果
        pass

    # 5. 导出最终结果
    render_job = await resolve_api.start_render({
        "format": "mp4",
        "quality": "high",
        "output_path": "./output/enhanced_video.mp4"
    })

    return render_job
```

### 4. 协同工作流程

#### **与DaVinci Resolve原生功能协同**
```python
# 最佳实践示例
class DaVinciWorkflowManager:
    """DaVinci工作流管理器"""

    def __init__(self):
        self.resolve_api = resolve_api
        self.ai_services = {}

    async def smart_color_grading(self):
        """智能调色工作流"""
        # 1. 导出当前帧
        frame_path = await self.resolve_api.export_current_frame(
            "./temp", "color_reference.png"
        )

        # 2. AI分析色彩
        color_analysis = await self.analyze_frame_colors(frame_path)

        # 3. 应用建议的调色参数
        await self.apply_color_correction(color_analysis)

    async def smart_audio_sync(self):
        """智能音频同步"""
        # 1. 提取音频
        audio_path = await self.resolve_api.extract_audio_for_ai()

        # 2. 分析音频特征
        audio_features = await self.analyze_audio_features(audio_path)

        # 3. 自动同步音频
        await self.sync_audio_to_video(audio_features)

    async def generate_edit_suggestions(self):
        """生成剪辑建议"""
        # 1. 分析整个时间线
        timeline_analysis = await self.resolve_api.analyze_timeline_content(
            analysis_type="comprehensive"
        )

        # 2. 生成剪辑建议
        suggestions = await self.ai_generate_edit_suggestions(timeline_analysis)

        # 3. 应用建议（可选）
        for suggestion in suggestions:
            if suggestion["confidence"] > 0.8:
                await self.apply_edit_suggestion(suggestion)

        return suggestions
```

---

## 🛠️ 故障排除

### 1. 常见错误及解决方案

#### **错误: "DaVinci Resolve Python API not available"**
```bash
# 解决步骤:
1. 确认DaVinci Resolve版本 >= 18.0
2. 重新安装DaVinci Resolve，选择完整安装
3. 检查环境变量设置
4. 重启DaVinci Resolve和终端

# 验证修复:
python -c "import DaVinciResolveScript; print('API可用')"
```

#### **错误: "Connection refused" 或端口占用**
```bash
# 检查端口占用
lsof -i :8000

# 杀死占用进程
kill -9 <PID>

# 或修改配置文件中的端口
nano config/config.json
# 修改 "port": 8001
```

#### **错误: "Permission denied"**
```bash
# macOS权限修复
sudo chmod -R 755 ./
sudo chown -R $(whoami) ./

# 重新设置DaVinci Resolve权限
# 系统偏好设置 > 安全性与隐私 > 完全磁盘访问权限
# 添加DaVinci Resolve
```

### 2. 性能优化建议

#### **系统资源优化**
```python
# 配置文件优化
{
  "features": {
    "batch_processing": {
      "max_concurrent_tasks": 2,  # 根据CPU核心数调整
      "queue_size": 50,           # 减少内存占用
      "auto_retry": true
    }
  },
  "storage": {
    "cleanup_interval": 1800,     # 30分钟清理一次临时文件
    "max_file_size": "50MB"       # 限制单文件大小
  }
}
```

#### **网络优化**
```json
{
  "ai_services": {
    "deepseek": {
      "timeout": 60,              # 增加超时时间
      "max_retries": 5            # 增加重试次数
    }
  }
}
```

### 3. 日志和监控

#### **启用详细日志**
```json
{
  "logging": {
    "level": "DEBUG",             # 开发时使用DEBUG
    "file": "./logs/app.log",
    "max_size": "50MB",
    "backup_count": 10
  }
}
```

#### **监控脚本**
```python
# monitor.py - 系统监控脚本
import psutil
import time

def monitor_system():
    """监控系统资源使用情况"""
    while True:
        # CPU使用率
        cpu_percent = psutil.cpu_percent(interval=1)

        # 内存使用率
        memory = psutil.virtual_memory()

        # 磁盘使用率
        disk = psutil.disk_usage('/')

        print(f"CPU: {cpu_percent}% | 内存: {memory.percent}% | 磁盘: {disk.percent}%")

        # 检查DaVinci Resolve进程
        for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent']):
            if 'resolve' in proc.info['name'].lower():
                print(f"DaVinci: CPU {proc.info['cpu_percent']}% | 内存 {proc.info['memory_percent']}%")

        time.sleep(10)

if __name__ == "__main__":
    monitor_system()
```

---

## 📚 附录

### A. 配置文件模板

#### **完整配置文件示例**
```json
{
  "app": {
    "name": "DaVinci AI Co-pilot PRO",
    "version": "0.1.0",
    "debug": false,
    "host": "127.0.0.1",
    "port": 8000
  },
  "davinci": {
    "resolve_path": "/Applications/DaVinci Resolve/DaVinci Resolve.app",
    "project_manager_timeout": 30,
    "media_pool_timeout": 30,
    "timeline_timeout": 30,
    "auto_save": true,
    "backup_projects": true,
    "frame_export_format": "PNG",
    "frame_export_quality": "high"
  },
  "ai_services": {
    "deepseek": {
      "api_key": "your-deepseek-api-key",
      "api_base": "https://api.deepseek.com/v1",
      "model": "deepseek-chat",
      "max_tokens": 4000,
      "temperature": 0.7
    }
  },
  "features": {
    "frame_capture": {
      "enabled": true,
      "default_format": "PNG",
      "default_quality": "high",
      "auto_ai_export": true
    },
    "ai_integration": {
      "enabled": true,
      "default_confidence": 0.7,
      "auto_apply_results": false
    },
    "subtitle_generation": {
      "enabled": true,
      "default_language": "zh-CN",
      "default_service": "deepseek"
    }
  }
}
```

### B. 快速启动脚本

#### **一键启动脚本 (macOS)**
```bash
#!/bin/bash
# quick_start.sh

echo "🎬 启动DaVinci AI Co-pilot Pro..."

# 检查DaVinci Resolve是否运行
if ! pgrep -f "DaVinci Resolve" > /dev/null; then
    echo "启动DaVinci Resolve..."
    open "/Applications/DaVinci Resolve/DaVinci Resolve.app"
    sleep 10
fi

# 进入项目目录
cd "$HOME/Library/Application Support/Blackmagic Design/DaVinci Resolve/Fusion/Scripts/Utility/DaVinci AI Co-pilot Pro"

# 激活虚拟环境
source davinci_ai_env/bin/activate

# 启动服务
python src/main.py &

# 等待服务启动
sleep 5

# 打开Web界面
open http://127.0.0.1:8000

echo "✅ DaVinci AI Co-pilot Pro 已启动!"
echo "🌐 Web界面: http://127.0.0.1:8000"
```

### C. 卸载脚本

#### **完整卸载脚本**
```bash
#!/bin/bash
# uninstall.sh

echo "🗑️ 卸载DaVinci AI Co-pilot Pro..."

# 停止服务
pkill -f "python src/main.py"

# 删除插件文件
SCRIPTS_DIR="$HOME/Library/Application Support/Blackmagic Design/DaVinci Resolve/Fusion/Scripts/Utility"
rm -rf "$SCRIPTS_DIR/DaVinci AI Co-pilot Pro"

# 删除虚拟环境
rm -rf davinci_ai_env

# 清理日志和缓存
rm -rf logs cache temp output

echo "✅ 卸载完成!"
```

---

## 📞 技术支持

如果在部署过程中遇到问题，请：

1. **检查日志文件**: `logs/app.log`
2. **运行诊断脚本**: `python diagnose_connection.py`
3. **查看DaVinci Resolve控制台**: `Workspace > Console`
4. **提供详细的错误信息和系统环境**

---

*最后更新: 2025-01-22*
*版本: 1.0.0*
```
