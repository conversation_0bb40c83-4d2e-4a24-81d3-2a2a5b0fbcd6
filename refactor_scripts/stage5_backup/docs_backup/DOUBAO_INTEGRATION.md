# 豆包MCP服务器集成指南

## 概述

豆包MCP服务器是字节跳动豆包AI的MCP协议实现，支持文本生成和对话功能。

## 安装和配置

### 1. 安装豆包MCP服务器

```bash
# 使用uv安装
uv tool install doubao-mcp-server

# 或者从源码安装
git clone https://github.com/wwwzhouhui/doubao_mcp_server.git
cd doubao_mcp_server
uv pip install -e .
```

### 2. 获取API密钥

1. 访问[豆包开放平台](https://console.volcengine.com/ark/)
2. 创建应用并获取API密钥
3. 记录API密钥和基础URL

### 3. 配置环境变量

在`config/config.json`中添加豆包配置：

```json
{
  "ai_services": {
    "doubao": {
      "api_key": "your_doubao_api_key_here",
      "base_url": "https://ark.cn-beijing.volces.com/api/v3",
      "model": "ep-20241226140932-xxxxx"
    }
  }
}
```

### 4. 更新MCP配置

在`config/mcp_enhanced.json`中启用豆包服务：

```json
{
  "mcpServers": {
    "doubao": {
      "provider": "doubao",
      "enabled": true,
      "transport": "stdio",
      "command": "uvx",
      "args": ["doubao-mcp-server"],
      "env": {
        "DOUBAO_API_KEY": "${DOUBAO_API_KEY}",
        "DOUBAO_BASE_URL": "${DOUBAO_BASE_URL}",
        "DOUBAO_MODEL": "${DOUBAO_MODEL}"
      },
      "capabilities": [
        "text_generation",
        "chat",
        "text_analysis"
      ],
      "tools": {
        "chat": "text_generation",
        "complete": "text_generation",
        "analyze": "text_analysis"
      },
      "timeout": 30,
      "retry_count": 3
    }
  }
}
```

## 使用示例

### 1. 文本生成

```python
from src.services.enhanced_ai_services import enhanced_ai_service_manager, AIRequest, ServiceType

# 创建文本生成请求
request = AIRequest(
    service_type=ServiceType.TEXT_GENERATION,
    provider="doubao",
    prompt="请写一首关于春天的诗",
    parameters={
        "max_tokens": 200,
        "temperature": 0.7
    }
)

# 处理请求
response = await enhanced_ai_service_manager.process_request(request)
print(response.data)
```

### 2. 对话功能

```python
# 创建对话请求
request = AIRequest(
    service_type=ServiceType.CHAT,
    provider="doubao",
    prompt="你好，我想了解一下人工智能的发展历史",
    parameters={
        "messages": [
            {"role": "system", "content": "你是一个专业的AI助手"},
            {"role": "user", "content": "你好，我想了解一下人工智能的发展历史"}
        ],
        "max_tokens": 500,
        "temperature": 0.8
    }
)

response = await enhanced_ai_service_manager.process_request(request)
print(response.data)
```

## 前端集成

### 1. 添加豆包选项到UI

在`web/static/js/app.js`中添加豆包提供商选项：

```javascript
// 在文本生成界面添加豆包选项
const textProviders = [
    { value: 'doubao', label: '豆包', icon: '🫘' },
    { value: 'deepseek', label: 'DeepSeek', icon: '🤖' }
];
```

### 2. 更新API调用

```javascript
async function generateText(prompt, provider = 'doubao') {
    const response = await fetch('/api/text/generate', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            prompt: prompt,
            provider: provider,
            parameters: {
                max_tokens: 200,
                temperature: 0.7
            }
        })
    });
    
    const result = await response.json();
    return result;
}
```

## 故障排除

### 常见问题

1. **API密钥错误**
   - 检查API密钥是否正确
   - 确认API密钥有足够的权限

2. **网络连接问题**
   - 检查网络连接
   - 确认防火墙设置

3. **模型不存在**
   - 检查模型ID是否正确
   - 确认模型在您的账户中可用

### 调试方法

1. **启用详细日志**
   ```json
   {
     "global_settings": {
       "log_level": "DEBUG"
     }
   }
   ```

2. **测试连接**
   ```bash
   # 测试豆包MCP服务器
   uvx doubao-mcp-server --test
   ```

3. **检查服务状态**
   ```python
   # 在Python中检查服务状态
   from src.services.enhanced_ai_services import enhanced_ai_service_manager
   
   stats = enhanced_ai_service_manager.get_service_stats()
   print(stats.get('doubao_doubao', 'Service not found'))
   ```

## 高级配置

### 1. 自定义模型参数

```json
{
  "mcpServers": {
    "doubao": {
      "parameters": {
        "default_model": "ep-20241226140932-xxxxx",
        "max_tokens": 1000,
        "temperature": 0.7,
        "top_p": 0.9,
        "frequency_penalty": 0.0,
        "presence_penalty": 0.0
      }
    }
  }
}
```

### 2. 负载均衡配置

```json
{
  "global_settings": {
    "load_balancer": {
      "strategy": "weighted_round_robin",
      "weights": {
        "doubao": 0.7,
        "deepseek": 0.3
      }
    }
  }
}
```

### 3. 熔断器配置

```json
{
  "global_settings": {
    "circuit_breaker": {
      "enabled": true,
      "failure_threshold": 3,
      "recovery_timeout": 180
    }
  }
}
```
