# 🎤 ElevenLabs MCP集成指南

## 📋 概述

本指南详细介绍如何将ElevenLabs MCP服务器集成到DaVinci AI Co-pilot Pro插件中，实现高质量的AI语音合成功能。

## 🌟 功能特性

### ✅ 已实现功能
- **高质量语音合成**：使用ElevenLabs最新的AI语音技术
- **多语言支持**：支持中文、英文等多种语言
- **多种语音模型**：eleven_multilingual_v2、eleven_turbo_v2_5等
- **灵活音频格式**：MP3、PCM等多种格式和质量选项
- **DaVinci集成**：与DaVinci Resolve深度集成
- **实时预览**：音频播放器和下载功能

### 🚧 计划功能
- **语音克隆**：即时和专业语音克隆
- **批量处理**：大量文本的批量语音合成
- **语音转换**：语音到语音的转换功能

## 🚀 快速开始

### 1. 获取ElevenLabs API密钥

1. 访问 [ElevenLabs官网](https://elevenlabs.io/)
2. 注册账户并登录
3. 前往 [API密钥页面](https://elevenlabs.io/app/settings/api-keys)
4. 创建新的API密钥并复制

### 2. 安装依赖

确保您的系统已安装以下依赖：

```bash
# 安装uv包管理器（如果尚未安装）
curl -LsSf https://astral.sh/uv/install.sh | sh

# 安装ElevenLabs MCP服务器
uvx elevenlabs-mcp
```

### 3. 配置API密钥

在 `config/config.json` 中添加您的ElevenLabs API密钥：

```json
{
  "ai_services": {
    "elevenlabs": {
      "api_key": "your_elevenlabs_api_key_here",
      "api_base": "https://api.elevenlabs.io/v1",
      "timeout": 60,
      "max_retries": 3
    }
  }
}
```

### 4. 测试集成

在启动服务前，建议先运行集成测试：

```bash
# 运行快速集成检查
python test_integration.py

# 运行完整功能测试（需要有效的API密钥）
python tests/test_elevenlabs_integration.py
```

### 5. 启动服务

```bash
# 启动DaVinci AI Co-pilot Pro
python src/main.py
```

服务启动后，在浏览器中访问 `http://localhost:8000` 即可使用语音合成功能。

## 🎯 使用指南

### 基础语音合成

1. **打开语音合成标签页**
   - 在Web界面中点击"语音合成"标签

2. **输入文本**
   - 在文本框中输入要转换为语音的内容
   - 支持最多5000个字符

3. **选择语音和模型**
   - 从下拉菜单中选择合适的语音
   - 选择语音模型（推荐使用多语言模型v2）

4. **设置音频质量**
   - 选择适合的音频格式和质量
   - 推荐使用MP3 44.1kHz 128kbps

5. **生成语音**
   - 点击"生成语音"按钮
   - 等待处理完成

6. **播放和下载**
   - 使用内置播放器预览音频
   - 点击"下载音频"保存文件
   - 点击"导入到DaVinci"直接导入到时间线

### DaVinci Resolve集成

#### 从时间线提取文本
1. 确保DaVinci Resolve正在运行并打开项目
2. 在语音合成页面点击"从时间线提取文本"
3. 系统会自动提取时间线中的文本内容
4. 提取的文本会自动填入语音合成文本框

#### 基于字幕生成配音
1. 确保时间线中已有字幕轨道
2. 点击"基于字幕生成配音"
3. 系统会为每个字幕片段生成对应的语音
4. 生成的音频会自动同步到时间线

#### 音频导入
1. 生成语音后，点击"导入到DaVinci"
2. 音频会自动导入到DaVinci Resolve的时间线
3. 支持自动同步和轨道选择

## 🔧 高级配置

### 语音模型选择

| 模型                   | 特点             | 适用场景       |
| ---------------------- | ---------------- | -------------- |
| eleven_multilingual_v2 | 高质量多语言支持 | 中英文混合内容 |
| eleven_turbo_v2_5      | 快速生成         | 实时应用       |
| eleven_flash_v2_5      | 超快速生成       | 大量文本处理   |
| eleven_multilingual_v1 | 经典多语言模型   | 兼容性需求     |

### 音频格式选择

| 格式          | 质量 | 文件大小 | 适用场景 |
| ------------- | ---- | -------- | -------- |
| mp3_44100_128 | 标准 | 中等     | 一般用途 |
| mp3_44100_192 | 高   | 较大     | 专业制作 |
| mp3_22050_32  | 低   | 小       | 快速预览 |
| pcm_44100     | 无损 | 最大     | 后期处理 |

### 环境变量配置

您也可以通过环境变量配置API密钥：

```bash
export ELEVENLABS_API_KEY="your_api_key_here"
```

## 🐛 故障排除

### 常见问题

#### 1. API密钥认证失败
**错误信息**：`ElevenLabs API认证失败`

**解决方案**：
- 检查API密钥是否正确
- 确认API密钥是否有效且未过期
- 验证账户是否有足够的配额

#### 2. MCP服务器连接失败
**错误信息**：`MCP server 'elevenlabs' not found`

**解决方案**：
- 确认已安装ElevenLabs MCP服务器：`uvx elevenlabs-mcp`
- 检查网络连接
- 重启应用程序

#### 3. 语音生成失败
**错误信息**：`Speech synthesis failed`

**解决方案**：
- 检查文本长度是否超过限制
- 确认选择的语音ID是否有效
- 检查API配额是否充足

#### 4. 音频播放问题
**解决方案**：
- 确认浏览器支持音频播放
- 检查音频文件是否生成成功
- 尝试刷新页面

### 日志查看

查看详细日志信息：

```bash
# 查看应用日志
tail -f logs/app.log

# 查看MCP服务器日志
# 日志位置因系统而异，通常在临时目录中
```

### 性能优化

1. **文本长度控制**
   - 建议单次合成文本不超过1000字符
   - 长文本可分段处理

2. **并发控制**
   - 避免同时发起多个语音合成请求
   - 使用队列机制处理批量任务

3. **缓存策略**
   - 相同文本和参数的结果会被缓存
   - 定期清理临时音频文件

## 📚 API参考

### 语音合成API

```http
POST /api/speech/synthesize
Content-Type: application/json

{
  "text": "要转换的文本",
  "voice_id": "JBFqnCBsd6RMkjVDRZzb",
  "model_id": "eleven_multilingual_v2",
  "output_format": "mp3_44100_128",
  "provider": "elevenlabs"
}
```

### 获取语音列表API

```http
GET /api/speech/voices
```

### DaVinci集成API

```http
POST /api/davinci/timeline/extract-text
POST /api/davinci/audio/import
POST /api/davinci/subtitles/generate-voiceover
```

## 🔗 相关链接

- [ElevenLabs官网](https://elevenlabs.io/)
- [ElevenLabs API文档](https://elevenlabs.io/docs)
- [ElevenLabs MCP服务器](https://github.com/elevenlabs/elevenlabs-mcp)
- [MCP协议规范](https://github.com/modelcontextprotocol)

## 📞 技术支持

如果您在使用过程中遇到问题：

1. 查看本文档的故障排除部分
2. 检查应用日志文件
3. 确认API密钥和网络连接
4. 提交GitHub Issue并提供详细的错误信息

---

## 🎉 总结

ElevenLabs MCP集成为DaVinci AI Co-pilot Pro带来了专业级的AI语音合成能力，支持：

✅ **高质量语音合成**  
✅ **多语言和多模型支持**  
✅ **DaVinci Resolve深度集成**  
✅ **灵活的音频格式选择**  
✅ **直观的用户界面**  
✅ **完善的错误处理**

通过这个集成，您可以轻松为视频项目添加专业的AI配音，大大提升制作效率和质量。
