# MCP集成指南

## 概述

DaVinci AI Co-pilot PRO 使用通用MCP（Model Context Protocol）架构，支持轻松集成多个MCP服务器。

## 架构优势

✅ **配置驱动**：添加新MCP服务器只需修改配置文件  
✅ **零代码更改**：无需编写新的Python服务类  
✅ **标准兼容**：完全符合MCP协议标准  
✅ **易于维护**：配置与代码分离  
✅ **动态扩展**：支持多种传输方式（stdio、HTTP、SSE）

## 配置文件结构

MCP配置文件位于 `config/mcp.json`：

```json
{
  "mcpServers": {
    "server_name": {
      "transport": "stdio|http|sse",
      "command": "command_to_run",
      "args": ["arg1", "arg2"],
      "env": {
        "ENV_VAR": "${PLACEHOLDER}"
      },
      "capabilities": ["speech_synthesis", "image_generation"],
      "tools": {
        "tool_name": "capability_type"
      },
      "enabled": true,
      "timeout": 30,
      "retry_count": 3
    }
  },
  "global_settings": {
    "default_timeout": 30,
    "max_concurrent_connections": 5,
    "output_directory": "/tmp/davinci_ai_copilot/mcp_output",
    "log_level": "INFO"
  }
}
```

## 添加新MCP服务器

### 1. 编辑配置文件

在 `config/mcp.json` 的 `mcpServers` 部分添加新服务器：

```json
"your_service": {
  "transport": "stdio",
  "command": "uvx",
  "args": ["your-mcp-server"],
  "env": {
    "YOUR_API_KEY": "${YOUR_API_KEY}"
  },
  "capabilities": ["text_generation", "image_generation"],
  "tools": {
    "generate_text": "text_generation",
    "create_image": "image_generation"
  },
  "enabled": true,
  "timeout": 30,
  "retry_count": 3
}
```

### 2. 配置环境变量

在主配置文件 `config/config.json` 中添加相应的API密钥：

```json
"ai_services": {
  "your_service": {
    "api_key": "your_api_key_here"
  }
}
```

### 3. 重启应用

重启应用后，新的MCP服务器将自动加载和注册。

## 支持的传输方式

### 1. Stdio传输（推荐）

```json
{
  "transport": "stdio",
  "command": "uvx",
  "args": ["mcp-server-package"],
  "env": {
    "API_KEY": "${API_KEY}"
  }
}
```

### 2. HTTP传输

```json
{
  "transport": "http",
  "url": "https://api.example.com/mcp",
  "headers": {
    "Authorization": "Bearer ${API_KEY}"
  }
}
```

### 3. SSE传输

```json
{
  "transport": "sse",
  "url": "https://api.example.com/mcp/sse",
  "headers": {
    "Authorization": "Bearer ${API_KEY}"
  }
}
```

## 环境变量占位符

配置文件支持以下占位符：

- `${OUTPUT_DIR}` - 输出目录路径
- `${MINIMAX_API_KEY}` - Minimax API密钥
- `${MINIMAX_API_HOST}` - Minimax API主机
- `${MINIMAX_GROUP_ID}` - Minimax组ID
- `${YOUR_ENV_VAR}` - 任何环境变量

## 服务能力映射

支持的服务能力类型：

- `speech_synthesis` - 语音合成
- `image_generation` - 图像生成
- `video_generation` - 视频生成
- `text_generation` - 文本生成
- `text_analysis` - 文本分析
- `translation` - 翻译

## 示例：添加OpenAI MCP服务器

1. **配置文件**：
```json
"openai": {
  "transport": "http",
  "url": "https://api.openai.com/mcp",
  "env": {
    "OPENAI_API_KEY": "${OPENAI_API_KEY}"
  },
  "capabilities": ["text_generation", "image_generation"],
  "tools": {
    "chat_completion": "text_generation",
    "dall_e_generate": "image_generation"
  },
  "enabled": true,
  "timeout": 30
}
```

2. **环境配置**：
```json
"ai_services": {
  "openai": {
    "api_key": "sk-your-openai-api-key"
  }
}
```

3. **重启应用** - 新服务自动可用！

## 故障排除

### 1. 检查日志

查看应用启动日志中的MCP相关信息：
```
✅ MCP Manager initialized with servers: ['minimax', 'openai']
✅ Registered MCP service: minimax
✅ MCP service 'minimax' initialized
```

### 2. 验证配置

确保：
- JSON格式正确
- 环境变量已设置
- MCP服务器包已安装（对于stdio传输）
- 网络连接正常（对于HTTP/SSE传输）

### 3. 健康检查

访问 `/health` 端点查看服务状态：
```json
{
  "services": {
    "minimax": {"is_available": true},
    "openai": {"is_available": true}
  }
}
```

## 最佳实践

1. **使用stdio传输**：对于本地MCP服务器，推荐使用stdio传输
2. **设置合理超时**：根据服务响应时间设置适当的超时值
3. **启用重试机制**：设置适当的重试次数处理临时故障
4. **监控日志**：定期检查MCP服务器连接状态
5. **测试配置**：添加新服务器后进行功能测试

通过这种配置驱动的架构，您可以轻松扩展系统支持任何符合MCP协议的服务器，无需修改任何Python代码！
