# Vidu视频生成MCP服务集成指南

## 概述

Vidu是一个高质量的视频生成服务，通过MCP协议提供文本到视频、图片到视频等功能。

## 安装和配置

### 1. 安装Vidu MCP服务器

```bash
# 使用uv安装（假设已发布到PyPI）
uv tool install vidu-mcp

# 或者从源码安装
git clone https://github.com/vidu-ai/vidu-mcp-server.git
cd vidu-mcp-server
uv pip install -e .
```

### 2. 获取API密钥

1. 访问[Vidu开放平台](https://platform.vidu.cn/)
2. 注册账户并创建应用
3. 获取API密钥和访问令牌

### 3. 配置环境变量

在`config/config.json`中添加Vidu配置：

```json
{
  "ai_services": {
    "vidu": {
      "api_key": "your_vidu_api_key_here",
      "base_url": "https://api.vidu.cn/v1",
      "default_model": "vidu-1.0",
      "max_duration": 10,
      "default_resolution": "1080p"
    }
  }
}
```

### 4. 更新MCP配置

在`config/mcp_enhanced.json`中启用Vidu服务：

```json
{
  "mcpServers": {
    "vidu": {
      "provider": "vidu",
      "enabled": true,
      "transport": "stdio",
      "command": "uvx",
      "args": ["vidu-mcp"],
      "env": {
        "VIDU_API_KEY": "${VIDU_API_KEY}",
        "VIDU_BASE_URL": "${VIDU_BASE_URL}",
        "VIDU_OUTPUT_DIR": "${OUTPUT_DIR}"
      },
      "capabilities": [
        "video_generation"
      ],
      "tools": {
        "generate_video": "video_generation",
        "text_to_video": "video_generation",
        "image_to_video": "video_generation",
        "get_video_status": "utility",
        "list_models": "utility"
      },
      "timeout": 120,
      "retry_count": 2,
      "health_check_interval": 600
    }
  }
}
```

## 使用示例

### 1. 文本到视频

```python
from src.services.enhanced_ai_services import enhanced_ai_service_manager, AIRequest, ServiceType

# 创建视频生成请求
request = AIRequest(
    service_type=ServiceType.VIDEO_GENERATION,
    provider="vidu",
    prompt="一只可爱的小猫在花园里玩耍，阳光明媚，画面温馨",
    parameters={
        "duration": 5,
        "resolution": "1080p",
        "fps": 24,
        "style": "realistic",
        "aspect_ratio": "16:9"
    }
)

# 处理请求
response = await enhanced_ai_service_manager.process_request(request)
print(f"视频生成成功: {response.data['video_url']}")
```

### 2. 图片到视频

```python
# 图片到视频生成
request = AIRequest(
    service_type=ServiceType.VIDEO_GENERATION,
    provider="vidu",
    prompt="让这张图片中的人物动起来，自然地眨眼和微笑",
    parameters={
        "input_image": "/path/to/input/image.jpg",
        "duration": 3,
        "motion_intensity": 0.7,
        "preserve_identity": True
    }
)

response = await enhanced_ai_service_manager.process_request(request)
```

### 3. 异步视频生成

```python
# 异步生成长视频
request = AIRequest(
    service_type=ServiceType.VIDEO_GENERATION,
    provider="vidu",
    prompt="一部关于未来城市的短片，包含飞行汽车和高楼大厦",
    parameters={
        "duration": 10,
        "async_mode": True,
        "callback_url": "https://your-domain.com/webhook/vidu"
    }
)

response = await enhanced_ai_service_manager.process_request(request)
task_id = response.data['task_id']

# 查询生成状态
status_request = AIRequest(
    service_type=ServiceType.UTILITY,
    provider="vidu",
    parameters={
        "tool_name": "get_video_status",
        "tool_params": {"task_id": task_id}
    }
)

status_response = await enhanced_ai_service_manager.process_request(status_request)
```

## 前端集成

### 1. 更新视频生成界面

在`web/static/js/app.js`中添加Vidu选项：

```javascript
// 视频生成提供商选项
const videoProviders = [
    { value: 'vidu', label: 'Vidu', icon: '🎬', description: '高质量视频生成' },
    { value: 'minimax', label: 'MiniMax', icon: '🎥', description: '多模态视频生成' }
];

// 更新视频生成函数
async function generateVideo(prompt, provider = 'vidu', options = {}) {
    const requestData = {
        prompt: prompt,
        provider: provider,
        parameters: {
            duration: options.duration || 5,
            resolution: options.resolution || '1080p',
            style: options.style || 'realistic',
            aspect_ratio: options.aspectRatio || '16:9',
            ...options
        }
    };

    const response = await fetch('/api/video/generate', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData)
    });

    return await response.json();
}
```

### 2. 添加Vidu特定参数控制

```html
<!-- Vidu视频生成参数 -->
<div id="vidu-params" class="provider-params" style="display: none;">
    <div class="param-group">
        <label>视频时长 (秒)</label>
        <input type="range" id="vidu-duration" min="1" max="10" value="5">
        <span id="vidu-duration-value">5s</span>
    </div>
    
    <div class="param-group">
        <label>分辨率</label>
        <select id="vidu-resolution">
            <option value="720p">720p</option>
            <option value="1080p" selected>1080p</option>
            <option value="4k">4K</option>
        </select>
    </div>
    
    <div class="param-group">
        <label>视频风格</label>
        <select id="vidu-style">
            <option value="realistic" selected>写实风格</option>
            <option value="anime">动漫风格</option>
            <option value="cartoon">卡通风格</option>
            <option value="cinematic">电影风格</option>
        </select>
    </div>
    
    <div class="param-group">
        <label>运动强度</label>
        <input type="range" id="vidu-motion" min="0" max="1" step="0.1" value="0.5">
        <span id="vidu-motion-value">0.5</span>
    </div>
</div>
```

### 3. 异步任务状态监控

```javascript
class VideuTaskMonitor {
    constructor() {
        this.activeTasks = new Map();
        this.pollInterval = 5000; // 5秒轮询一次
    }
    
    async startMonitoring(taskId, onProgress, onComplete) {
        this.activeTasks.set(taskId, {
            onProgress,
            onComplete,
            startTime: Date.now()
        });
        
        this.pollTaskStatus(taskId);
    }
    
    async pollTaskStatus(taskId) {
        try {
            const response = await fetch('/api/video/status', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    provider: 'vidu',
                    task_id: taskId
                })
            });
            
            const result = await response.json();
            const task = this.activeTasks.get(taskId);
            
            if (!task) return;
            
            if (result.status === 'completed') {
                task.onComplete(result.data);
                this.activeTasks.delete(taskId);
            } else if (result.status === 'failed') {
                task.onComplete({ error: result.error });
                this.activeTasks.delete(taskId);
            } else {
                task.onProgress(result);
                setTimeout(() => this.pollTaskStatus(taskId), this.pollInterval);
            }
            
        } catch (error) {
            console.error('Failed to poll task status:', error);
            const task = this.activeTasks.get(taskId);
            if (task) {
                task.onComplete({ error: error.message });
                this.activeTasks.delete(taskId);
            }
        }
    }
}

// 使用示例
const taskMonitor = new VideuTaskMonitor();

async function generateLongVideo(prompt) {
    const response = await generateVideo(prompt, 'vidu', {
        duration: 10,
        async_mode: true
    });
    
    if (response.success && response.data.task_id) {
        taskMonitor.startMonitoring(
            response.data.task_id,
            (progress) => {
                console.log('生成进度:', progress);
                updateProgressBar(progress.progress || 0);
            },
            (result) => {
                if (result.error) {
                    showError('视频生成失败: ' + result.error);
                } else {
                    showSuccess('视频生成完成!');
                    displayVideo(result.video_url);
                }
            }
        );
    }
}
```

## 高级功能

### 1. 批量视频生成

```python
async def batch_generate_videos(prompts, provider="vidu"):
    tasks = []
    for prompt in prompts:
        request = AIRequest(
            service_type=ServiceType.VIDEO_GENERATION,
            provider=provider,
            prompt=prompt,
            parameters={"async_mode": True}
        )
        task = enhanced_ai_service_manager.process_request(request)
        tasks.append(task)
    
    responses = await asyncio.gather(*tasks)
    return responses
```

### 2. 视频后处理

```python
# 视频增强和后处理
request = AIRequest(
    service_type=ServiceType.UTILITY,
    provider="vidu",
    parameters={
        "tool_name": "enhance_video",
        "tool_params": {
            "input_video": "/path/to/video.mp4",
            "enhancement_type": "upscale",
            "target_resolution": "4k"
        }
    }
)
```

### 3. 自定义风格训练

```python
# 训练自定义风格模型
request = AIRequest(
    service_type=ServiceType.UTILITY,
    provider="vidu",
    parameters={
        "tool_name": "train_style",
        "tool_params": {
            "style_name": "my_custom_style",
            "training_images": ["/path/to/img1.jpg", "/path/to/img2.jpg"],
            "training_videos": ["/path/to/vid1.mp4"]
        }
    }
)
```

## 故障排除

### 常见问题

1. **生成时间过长**
   - 使用异步模式处理长视频
   - 适当降低分辨率和时长

2. **API配额不足**
   - 检查账户余额
   - 优化生成参数减少消耗

3. **视频质量不佳**
   - 优化提示词描述
   - 调整风格和参数设置

### 性能优化

1. **缓存机制**
   ```python
   # 启用结果缓存
   request.metadata['cache_enabled'] = True
   request.metadata['cache_ttl'] = 3600  # 1小时
   ```

2. **预处理优化**
   ```python
   # 图片预处理
   request.parameters['preprocess_image'] = True
   request.parameters['optimize_prompt'] = True
   ```
