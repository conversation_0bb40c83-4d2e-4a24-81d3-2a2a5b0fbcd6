#!/usr/bin/env python3
"""
迁移base_types到simplified_types
"""

import os
import re
from pathlib import Path

def migrate_types_in_file(file_path):
    """迁移单个文件中的类型使用"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        original_content = content

        # 替换导入语句
        content = re.sub(
            r'from\s+\.\.?services\.base_types\s+import\s+.*',
            'from .simplified_types import ServiceRequest, ServiceResponse',
            content
        )

        content = re.sub(
            r'from\s+\.\.?types\.base_types\s+import\s+.*',
            'from ..services.simplified_types import ServiceRequest, ServiceResponse',
            content
        )

        # 替换类型使用（保持向后兼容）
        # AIRequest -> ServiceRequest (通过转换函数)
        # AIResponse -> ServiceResponse (通过转换函数)

        # 如果内容有变化，写回文件
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"  ✅ 类型迁移完成: {file_path}")
            return True
        else:
            print(f"  ⏭️  无需类型迁移: {file_path}")
            return False

    except Exception as e:
        print(f"  ❌ 类型迁移失败: {file_path} - {e}")
        return False

def main():
    project_root = Path('/Library/Application Support/Blackmagic Design/DaVinci Resolve/Fusion/Scripts/Utility/DaVinci AI Co-pilot Pro')

    # 查找所有Python文件
    python_files = list(project_root.glob('src/**/*.py'))

    migrated_count = 0
    for file_path in python_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            if 'base_types' in content or 'AIRequest' in content or 'AIResponse' in content:
                if migrate_types_in_file(file_path):
                    migrated_count += 1
        except Exception as e:
            print(f"  ⚠️  检查文件失败: {file_path} - {e}")

    print(f"\n📊 类型迁移完成: {migrated_count} 个文件")

if __name__ == "__main__":
    main()
