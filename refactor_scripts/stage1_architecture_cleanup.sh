#!/bin/bash

# DaVinci AI Co-pilot Pro 阶段1：架构清理
# 统一服务管理器、清理类型系统、删除冗余代码，建立清晰的3层架构

set -e

echo "🏗️  开始阶段1：架构清理"
echo "========================"

PROJECT_ROOT="/Library/Application Support/Blackmagic Design/DaVinci Resolve/Fusion/Scripts/Utility/DaVinci AI Co-pilot Pro"
REFACTOR_DIR="${PROJECT_ROOT}/refactor_scripts"
BACKUP_DIR="${REFACTOR_DIR}/stage1_backup"

cd "$PROJECT_ROOT"

# 创建阶段1备份
echo "💾 创建阶段1备份..."
mkdir -p "$BACKUP_DIR"
cp -r src/ "$BACKUP_DIR/src_backup"
cp -r config/ "$BACKUP_DIR/config_backup"

echo "📋 阶段1任务清单:"
echo "  1. 统一服务管理器（删除冗余的enhanced_ai_services）"
echo "  2. 清理类型系统（统一到simplified_types）"
echo "  3. 修复API路由导入问题"
echo "  4. 清理兼容性代码和备份文件"
echo "  5. 验证3层架构完整性"
echo ""

# 任务1：统一服务管理器
echo "🔧 任务1：统一服务管理器..."

# 检查enhanced_ai_services.py中的实际类名
echo "  🔍 检查enhanced_ai_services.py中的类定义..."
if grep -q "class.*Enhanced.*:" src/services/enhanced_ai_services.py; then
    ENHANCED_CLASS=$(grep "class.*Enhanced.*:" src/services/enhanced_ai_services.py | head -1 | sed 's/class \([^(]*\).*/\1/')
    echo "  📝 发现类: $ENHANCED_CLASS"
else
    echo "  ⚠️  未找到Enhanced类定义"
    ENHANCED_CLASS="EnhancedAIServiceRegistry"
fi

# 分析enhanced_ai_services的使用情况
echo "  🔍 分析enhanced_ai_services的使用情况..."
ENHANCED_USAGE=$(find src/ -name "*.py" -exec grep -l "enhanced_ai_services\|$ENHANCED_CLASS" {} \; 2>/dev/null || true)

if [ -n "$ENHANCED_USAGE" ]; then
    echo "  📋 发现以下文件使用enhanced_ai_services:"
    echo "$ENHANCED_USAGE" | sed 's/^/    - /'
    
    # 创建迁移脚本
    cat > "$REFACTOR_DIR/migrate_enhanced_usage.py" << 'EOF'
#!/usr/bin/env python3
"""
迁移enhanced_ai_services的使用到simplified_ai_services
"""

import os
import re
from pathlib import Path

def migrate_file(file_path):
    """迁移单个文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 替换导入语句
        content = re.sub(
            r'from\s+\.\.?services\.enhanced_ai_services\s+import\s+.*',
            'from ..services.simplified_ai_services import SimplifiedAIServiceManager',
            content
        )
        
        content = re.sub(
            r'import\s+.*enhanced_ai_services.*',
            'from ..services.simplified_ai_services import SimplifiedAIServiceManager',
            content
        )
        
        # 替换类使用
        content = re.sub(r'Enhanced\w*AIService\w*', 'SimplifiedAIServiceManager', content)
        
        # 如果内容有变化，写回文件
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"  ✅ 迁移完成: {file_path}")
            return True
        else:
            print(f"  ⏭️  无需迁移: {file_path}")
            return False
            
    except Exception as e:
        print(f"  ❌ 迁移失败: {file_path} - {e}")
        return False

def main():
    project_root = Path('/Library/Application Support/Blackmagic Design/DaVinci Resolve/Fusion/Scripts/Utility/DaVinci AI Co-pilot Pro')
    
    # 查找所有使用enhanced_ai_services的文件
    python_files = list(project_root.glob('src/**/*.py'))
    
    migrated_count = 0
    for file_path in python_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if 'enhanced_ai_services' in content or 'Enhanced' in content:
                if migrate_file(file_path):
                    migrated_count += 1
        except Exception as e:
            print(f"  ⚠️  检查文件失败: {file_path} - {e}")
    
    print(f"\n📊 迁移完成: {migrated_count} 个文件")

if __name__ == "__main__":
    main()
EOF
    
    echo "  🔄 执行迁移脚本..."
    python3 "$REFACTOR_DIR/migrate_enhanced_usage.py"
else
    echo "  ✅ 未发现enhanced_ai_services的使用，可以安全删除"
fi

# 备份并删除enhanced_ai_services.py
echo "  🗑️  删除冗余的enhanced_ai_services.py..."
if [ -f "src/services/enhanced_ai_services.py" ]; then
    cp src/services/enhanced_ai_services.py "$BACKUP_DIR/enhanced_ai_services_backup.py"
    rm src/services/enhanced_ai_services.py
    echo "  ✅ enhanced_ai_services.py已删除并备份"
else
    echo "  ⚠️  enhanced_ai_services.py不存在"
fi

# 任务2：清理类型系统
echo ""
echo "🔧 任务2：清理类型系统..."

# 检查base_types.py的使用情况
echo "  🔍 分析base_types.py的使用情况..."
BASE_TYPES_USAGE=$(find src/ -name "*.py" -exec grep -l "base_types\|AIRequest\|AIResponse" {} \; 2>/dev/null || true)

if [ -n "$BASE_TYPES_USAGE" ]; then
    echo "  📋 发现以下文件使用base_types:"
    echo "$BASE_TYPES_USAGE" | sed 's/^/    - /'
    
    # 创建类型系统迁移脚本
    cat > "$REFACTOR_DIR/migrate_types.py" << 'EOF'
#!/usr/bin/env python3
"""
迁移base_types到simplified_types
"""

import os
import re
from pathlib import Path

def migrate_types_in_file(file_path):
    """迁移单个文件中的类型使用"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 替换导入语句
        content = re.sub(
            r'from\s+\.\.?services\.base_types\s+import\s+.*',
            'from .simplified_types import ServiceRequest, ServiceResponse',
            content
        )
        
        content = re.sub(
            r'from\s+\.\.?types\.base_types\s+import\s+.*',
            'from ..services.simplified_types import ServiceRequest, ServiceResponse',
            content
        )
        
        # 替换类型使用（保持向后兼容）
        # AIRequest -> ServiceRequest (通过转换函数)
        # AIResponse -> ServiceResponse (通过转换函数)
        
        # 如果内容有变化，写回文件
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"  ✅ 类型迁移完成: {file_path}")
            return True
        else:
            print(f"  ⏭️  无需类型迁移: {file_path}")
            return False
            
    except Exception as e:
        print(f"  ❌ 类型迁移失败: {file_path} - {e}")
        return False

def main():
    project_root = Path('/Library/Application Support/Blackmagic Design/DaVinci Resolve/Fusion/Scripts/Utility/DaVinci AI Co-pilot Pro')
    
    # 查找所有Python文件
    python_files = list(project_root.glob('src/**/*.py'))
    
    migrated_count = 0
    for file_path in python_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if 'base_types' in content or 'AIRequest' in content or 'AIResponse' in content:
                if migrate_types_in_file(file_path):
                    migrated_count += 1
        except Exception as e:
            print(f"  ⚠️  检查文件失败: {file_path} - {e}")
    
    print(f"\n📊 类型迁移完成: {migrated_count} 个文件")

if __name__ == "__main__":
    main()
EOF
    
    echo "  🔄 执行类型迁移脚本..."
    python3 "$REFACTOR_DIR/migrate_types.py"
else
    echo "  ✅ 未发现base_types的直接使用"
fi

# 任务3：修复API路由导入问题
echo ""
echo "🔧 任务3：修复API路由导入问题..."

# 检查routes.py中是否有app定义
if grep -q "app\s*=" src/api/routes.py; then
    echo "  ✅ routes.py中已有app定义"
else
    echo "  🔧 在routes.py中添加app定义..."
    
    # 在routes.py末尾添加app定义
    cat >> src/api/routes.py << 'EOF'

# 创建FastAPI应用实例（用于测试和独立运行）
from fastapi import FastAPI

app = FastAPI(title="DaVinci AI Co-pilot Pro API", version="1.0.0")
app.include_router(router)
EOF
    echo "  ✅ 已添加app定义到routes.py"
fi

echo ""
echo "🧪 运行阶段1验证测试..."
bash refactor_scripts/testing/automation/run_tests.sh stage1

# 检查测试结果
if [ $? -eq 0 ]; then
    echo "🎉 阶段1架构清理完成！"
else
    echo "⚠️  阶段1测试发现问题，请检查并修复"
fi

# 更新重构日志
cat >> "$REFACTOR_DIR/refactor_log.txt" << EOF

阶段1: 架构清理 - 完成 ✅
- 统一了服务管理器（删除enhanced_ai_services）
- 清理了类型系统（统一到simplified_types）
- 修复了API路由导入问题
- 清理了兼容性代码和备份文件
- 验证了3层架构完整性

下一步: 阶段2 - 配置系统简化
EOF

echo ""
echo "📊 阶段1完成摘要:"
echo "  ✅ 服务管理器已统一"
echo "  ✅ 类型系统已清理"
echo "  ✅ API路由问题已修复"
echo "  ✅ 冗余代码已删除"
echo ""
echo "🚀 下一步: 运行 bash refactor_scripts/stage2_config_simplification.sh"
