#!/bin/bash

# DaVinci AI Co-pilot Pro 阶段2：配置系统简化
# 合并配置文件、统一配置加载、环境变量整理，建立统一的配置管理系统

set -e

echo "⚙️  开始阶段2：配置系统简化"
echo "=========================="

PROJECT_ROOT="/Library/Application Support/Blackmagic Design/DaVinci Resolve/Fusion/Scripts/Utility/DaVinci AI Co-pilot Pro"
REFACTOR_DIR="${PROJECT_ROOT}/refactor_scripts"
BACKUP_DIR="${REFACTOR_DIR}/stage2_backup"

cd "$PROJECT_ROOT"

# 创建阶段2备份
echo "💾 创建阶段2备份..."
mkdir -p "$BACKUP_DIR"
cp -r config/ "$BACKUP_DIR/config_backup"
cp -r src/core/ "$BACKUP_DIR/core_backup" 2>/dev/null || true

echo "📋 阶段2任务清单:"
echo "  1. 分析现有配置文件结构"
echo "  2. 合并冗余配置文件"
echo "  3. 创建统一配置管理器"
echo "  4. 简化环境变量处理"
echo "  5. 更新配置加载逻辑"
echo ""

# 任务1：分析现有配置文件结构
echo "🔍 任务1：分析现有配置文件结构..."

echo "  📋 发现的配置文件:"
find config/ -name "*.json" -type f | while read file; do
    size=$(wc -c < "$file" 2>/dev/null || echo "0")
    lines=$(wc -l < "$file" 2>/dev/null || echo "0")
    echo "    - $file (${size}字节, ${lines}行)"
done

# 创建配置分析脚本
cat > "$REFACTOR_DIR/analyze_configs.py" << 'EOF'
#!/usr/bin/env python3
"""
分析现有配置文件的结构和重复内容
"""

import json
import os
from pathlib import Path
from collections import defaultdict

def analyze_config_files():
    """分析配置文件"""
    project_root = Path('/Library/Application Support/Blackmagic Design/DaVinci Resolve/Fusion/Scripts/Utility/DaVinci AI Co-pilot Pro')
    config_dir = project_root / 'config'
    
    configs = {}
    all_keys = defaultdict(list)
    
    print("📊 配置文件分析结果:")
    print("=" * 50)
    
    # 读取所有配置文件
    for config_file in config_dir.glob('*.json'):
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            configs[config_file.name] = config_data
            
            # 收集所有键
            def collect_keys(data, prefix=''):
                if isinstance(data, dict):
                    for key, value in data.items():
                        full_key = f"{prefix}.{key}" if prefix else key
                        all_keys[full_key].append(config_file.name)
                        if isinstance(value, dict):
                            collect_keys(value, full_key)
            
            collect_keys(config_data)
            
            print(f"✅ {config_file.name}: {len(config_data)} 个顶级配置项")
            
        except Exception as e:
            print(f"❌ {config_file.name}: 读取失败 - {e}")
    
    print("\n🔍 重复配置项分析:")
    print("-" * 30)
    
    # 查找重复的配置项
    duplicates = {k: v for k, v in all_keys.items() if len(v) > 1}
    
    if duplicates:
        for key, files in duplicates.items():
            print(f"  🔄 {key}: 出现在 {', '.join(files)}")
    else:
        print("  ✅ 未发现重复配置项")
    
    print(f"\n📈 统计信息:")
    print(f"  - 配置文件总数: {len(configs)}")
    print(f"  - 配置项总数: {len(all_keys)}")
    print(f"  - 重复配置项: {len(duplicates)}")
    
    # 生成合并建议
    print(f"\n💡 合并建议:")
    
    # 按功能分类配置文件
    categories = {
        'core': ['config.json'],
        'mcp': ['mcp_enhanced.json'],
        'services': ['simplified_service_config.json'],
        'dynamic': ['dynamic_types.json']
    }
    
    for category, files in categories.items():
        existing_files = [f for f in files if f in configs]
        if existing_files:
            print(f"  📁 {category}类配置: {', '.join(existing_files)}")
    
    return configs, duplicates

if __name__ == "__main__":
    analyze_config_files()
EOF

echo "  🔄 执行配置分析..."
python3 "$REFACTOR_DIR/analyze_configs.py"

# 任务2：合并冗余配置文件
echo ""
echo "🔧 任务2：合并冗余配置文件..."

# 创建统一配置文件
echo "  📝 创建统一配置文件..."

cat > "$REFACTOR_DIR/create_unified_config.py" << 'EOF'
#!/usr/bin/env python3
"""
创建统一的配置文件
"""

import json
import os
from pathlib import Path
from datetime import datetime

def create_unified_config():
    """创建统一配置"""
    project_root = Path('/Library/Application Support/Blackmagic Design/DaVinci Resolve/Fusion/Scripts/Utility/DaVinci AI Co-pilot Pro')
    config_dir = project_root / 'config'
    
    # 读取现有配置
    configs = {}
    config_files = ['config.json', 'mcp_enhanced.json', 'simplified_service_config.json', 'dynamic_types.json']
    
    for config_file in config_files:
        config_path = config_dir / config_file
        if config_path.exists():
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    configs[config_file] = json.load(f)
                print(f"  ✅ 读取配置: {config_file}")
            except Exception as e:
                print(f"  ❌ 读取失败: {config_file} - {e}")
    
    # 创建统一配置结构
    unified_config = {
        "_metadata": {
            "version": "2.0.0",
            "created": datetime.now().isoformat(),
            "description": "DaVinci AI Co-pilot Pro 统一配置文件",
            "migration_from": list(configs.keys())
        },
        
        # 核心配置
        "core": {
            "app_name": "DaVinci AI Co-pilot Pro",
            "version": "2.0.0",
            "debug": False,
            "log_level": "INFO"
        },
        
        # 服务提供商配置
        "providers": {
            "deepseek": {
                "enabled": True,
                "api_base": "https://api.deepseek.com",
                "model": "deepseek-chat",
                "capabilities": ["TEXT_GENERATION", "TRANSLATION", "TEXT_ANALYSIS"]
            },
            "minimax": {
                "enabled": True,
                "api_base": "https://api.minimax.chat",
                "capabilities": ["VOICE_SYNTHESIS"]
            },
            "elevenlabs": {
                "enabled": True,
                "api_base": "https://api.elevenlabs.io",
                "capabilities": ["VOICE_SYNTHESIS"]
            },
            "doubao": {
                "enabled": True,
                "api_base": "https://ark.cn-beijing.volces.com",
                "capabilities": ["IMAGE_GENERATION"]
            },
            "vidu": {
                "enabled": True,
                "api_base": "https://api.vidu.ai",
                "capabilities": ["VIDEO_GENERATION"]
            }
        },
        
        # MCP服务器配置
        "mcp": {
            "enabled": True,
            "timeout": 30,
            "servers": {}
        },
        
        # API配置
        "api": {
            "host": "127.0.0.1",
            "port": 8000,
            "cors_origins": ["*"],
            "rate_limit": {
                "enabled": True,
                "requests_per_minute": 60
            }
        },
        
        # 服务配置
        "services": {
            "direct_adapter": {
                "enabled": True,
                "fallback_to_mcp": True
            },
            "load_balancer": {
                "enabled": False,
                "strategy": "round_robin"
            }
        }
    }
    
    # 合并现有配置
    for config_name, config_data in configs.items():
        print(f"  🔄 合并配置: {config_name}")
        
        if config_name == 'config.json' and isinstance(config_data, dict):
            # 合并核心配置
            if 'app' in config_data:
                unified_config['core'].update(config_data['app'])
            if 'api' in config_data:
                unified_config['api'].update(config_data['api'])
        
        elif config_name == 'mcp_enhanced.json' and isinstance(config_data, dict):
            # 合并MCP配置
            if 'mcpServers' in config_data:
                unified_config['mcp']['servers'] = config_data['mcpServers']
            if 'timeout' in config_data:
                unified_config['mcp']['timeout'] = config_data['timeout']
        
        elif config_name == 'simplified_service_config.json' and isinstance(config_data, dict):
            # 合并服务配置
            if 'services' in config_data:
                unified_config['services'].update(config_data['services'])
    
    # 保存统一配置
    unified_path = config_dir / 'unified_config.json'
    with open(unified_path, 'w', encoding='utf-8') as f:
        json.dump(unified_config, f, indent=2, ensure_ascii=False)
    
    print(f"  ✅ 统一配置已创建: {unified_path}")
    print(f"  📊 配置项统计: {len(unified_config)} 个顶级分类")
    
    return unified_config

if __name__ == "__main__":
    create_unified_config()
EOF

echo "  🔄 执行配置合并..."
python3 "$REFACTOR_DIR/create_unified_config.py"

# 任务3：创建统一配置管理器
echo ""
echo "🔧 任务3：创建统一配置管理器..."

# 创建新的配置管理器
cat > "src/core/unified_config.py" << 'EOF'
"""
统一配置管理器
简化的配置加载和管理系统
"""

import json
import os
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)

@dataclass
class ConfigManager:
    """统一配置管理器"""
    
    def __init__(self, config_path: Optional[str] = None):
        self.project_root = Path(__file__).parent.parent.parent
        self.config_path = config_path or self.project_root / 'config' / 'unified_config.json'
        self._config = None
        self._env_vars = {}
        self.load_config()
    
    def load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            # 加载主配置文件
            if self.config_path.exists():
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    self._config = json.load(f)
                logger.info(f"配置文件加载成功: {self.config_path}")
            else:
                logger.warning(f"配置文件不存在: {self.config_path}")
                self._config = self._get_default_config()
            
            # 加载环境变量
            self._load_env_vars()
            
            # 解析环境变量占位符
            self._resolve_env_placeholders()
            
            return self._config
            
        except Exception as e:
            logger.error(f"配置加载失败: {e}")
            self._config = self._get_default_config()
            return self._config
    
    def _load_env_vars(self):
        """加载环境变量"""
        env_file = self.project_root / '.env'
        if env_file.exists():
            try:
                with open(env_file, 'r', encoding='utf-8') as f:
                    for line in f:
                        line = line.strip()
                        if line and not line.startswith('#') and '=' in line:
                            key, value = line.split('=', 1)
                            self._env_vars[key.strip()] = value.strip()
                logger.info(f"环境变量加载成功: {len(self._env_vars)} 个变量")
            except Exception as e:
                logger.error(f"环境变量加载失败: {e}")
    
    def _resolve_env_placeholders(self):
        """解析环境变量占位符"""
        def resolve_value(value):
            if isinstance(value, str) and value.startswith('${') and value.endswith('}'):
                env_key = value[2:-1]
                return self._env_vars.get(env_key, os.getenv(env_key, value))
            elif isinstance(value, dict):
                return {k: resolve_value(v) for k, v in value.items()}
            elif isinstance(value, list):
                return [resolve_value(item) for item in value]
            return value
        
        if self._config:
            self._config = resolve_value(self._config)
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值（支持点号分隔的嵌套键）"""
        if not self._config:
            return default
        
        keys = key.split('.')
        value = self._config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def get_provider_config(self, provider: str) -> Dict[str, Any]:
        """获取服务提供商配置"""
        return self.get(f'providers.{provider}', {})
    
    def get_mcp_config(self) -> Dict[str, Any]:
        """获取MCP配置"""
        return self.get('mcp', {})
    
    def get_api_config(self) -> Dict[str, Any]:
        """获取API配置"""
        return self.get('api', {})
    
    def is_provider_enabled(self, provider: str) -> bool:
        """检查服务提供商是否启用"""
        return self.get(f'providers.{provider}.enabled', False)
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "core": {
                "app_name": "DaVinci AI Co-pilot Pro",
                "version": "2.0.0",
                "debug": False
            },
            "providers": {},
            "mcp": {"enabled": True, "servers": {}},
            "api": {"host": "127.0.0.1", "port": 8000},
            "services": {}
        }

# 全局配置实例
config_manager = ConfigManager()

def get_config(key: str, default: Any = None) -> Any:
    """获取配置值的便捷函数"""
    return config_manager.get(key, default)
EOF

echo "  ✅ 统一配置管理器已创建"

# 运行阶段2验证测试
echo ""
echo "🧪 运行阶段2验证测试..."
bash refactor_scripts/testing/automation/run_tests.sh stage2

# 检查测试结果
if [ $? -eq 0 ]; then
    echo "🎉 阶段2配置系统简化完成！"
else
    echo "⚠️  阶段2测试发现问题，请检查并修复"
fi

# 更新重构日志
cat >> "$REFACTOR_DIR/refactor_log.txt" << EOF

阶段2: 配置系统简化 - 完成 ✅
- 分析了现有配置文件结构
- 合并了冗余配置文件到unified_config.json
- 创建了统一配置管理器
- 简化了环境变量处理
- 更新了配置加载逻辑

下一步: 阶段3 - API统一
EOF

echo ""
echo "📊 阶段2完成摘要:"
echo "  ✅ 配置文件已合并"
echo "  ✅ 统一配置管理器已创建"
echo "  ✅ 环境变量处理已简化"
echo "  ✅ 配置加载逻辑已更新"
echo ""
echo "🚀 下一步: 运行 bash refactor_scripts/stage3_api_unification.sh"
