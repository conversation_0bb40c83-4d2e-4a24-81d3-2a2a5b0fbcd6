
"""
API路由模块
定义所有的API端点和请求处理逻辑
"""

import json
import logging
import os
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional, List
from fastapi import APIRouter, HTTPException, UploadFile, File, Form, Query, Request
from pydantic import BaseModel, Field
import httpx

from ..services.simplified_types import ServiceRequest, ServiceCapability, ServiceProvider

from ..services import (
    simplified_ai_service_manager as ai_service_manager
)
from ..services.simplified_types import ServiceRequest, ServiceCapability, ServiceProvider
from ..core import (
    BasePluginError,
    APIError,
    handle_errors,
    monitor_performance,
    get_config
)

# 导入媒体集成（保留需要的功能）
from .auto_media_integration import auto_add_to_media_library
# 导入简化配置路由
from .simple_config_routes import router as config_router

logger = logging.getLogger(__name__)

# 创建API路由器
router = APIRouter(prefix="/api", tags=["api"])

# 注册简化配置路由
router.include_router(config_router)

# 请求模型
class AIAnalysisRequest(BaseModel):
    """AI分析请求模型"""
    service_type: str = Field(..., description="服务类型")
    provider: Optional[str] = Field(None, description="服务提供商")
    prompt: str = Field(..., description="输入提示词")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="额外参数")

class SpeechSynthesisRequest(BaseModel):
    """语音合成请求模型"""
    text: str = Field(..., description="要转换为语音的文本")
    voice_id: Optional[str] = Field("JBFqnCBsd6RMkjVDRZzb", description="语音ID")
    model_id: Optional[str] = Field("eleven_multilingual_v2", description="语音模型ID")
    output_format: Optional[str] = Field("mp3_44100_128", description="输出音频格式")
    provider: Optional[str] = Field("elevenlabs", description="服务提供商")

class VoiceCloneRequest(BaseModel):
    """语音克隆请求模型"""
    name: str = Field(..., description="语音克隆名称")
    clone_type: str = Field("instant", description="克隆类型: instant 或 professional")
    sample_files: List[str] = Field(..., description="语音样本文件路径列表")

class FavoriteVoiceRequest(BaseModel):
    """常用语音请求模型"""
    voice_id: str = Field(..., description="语音ID")
    voice_name: str = Field(..., description="语音名称")
    provider: str = Field(..., description="语音提供商")
    description: Optional[str] = Field(None, description="语音描述")
    category: Optional[str] = Field(None, description="语音分类")
    language: Optional[str] = Field(None, description="语音语言")
    preview_url: Optional[str] = Field(None, description="预览音频URL")

class BatchSpeechRequest(BaseModel):
    """批量语音合成请求模型"""
    name: str = Field(..., description="批量任务名称")
    texts: List[str] = Field(..., description="要转换的文本列表")
    voice_config: Dict[str, Any] = Field(..., description="语音配置参数")
    auto_start: bool = Field(True, description="是否自动开始处理")

class AudioImportRequest(BaseModel):
    """音频导入请求模型"""
    audio_url: str = Field(..., description="音频文件URL")
    track_index: int = Field(1, description="导入到的轨道索引")
    auto_sync: bool = Field(True, description="是否自动同步")

class VoiceoverRequest(BaseModel):
    """配音生成请求模型"""
    voice_id: str = Field(..., description="语音ID")
    model_id: str = Field("eleven_multilingual_v2", description="语音模型ID")
    output_format: str = Field("mp3_44100_128", description="输出音频格式")

class AIGenerationRequest(BaseModel):
    """AI生成请求模型"""
    service_type: str = Field(..., description="服务类型")
    provider: Optional[str] = Field(None, description="服务提供商")
    prompt: str = Field(..., description="输入提示词")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="生成参数")

# 响应模型
class APIResponse(BaseModel):
    """API响应模型"""
    success: bool
    data: Any = None
    error: Optional[str] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)

# ==================== 统一动态API端点 ====================

class UnifiedServiceRequest(BaseModel):
    """统一服务请求模型"""
    service_type: str = Field(..., description="服务类型")
    capability: Optional[str] = Field(None, description="具体能力（可选，用于细分服务类型）")
    provider: Optional[str] = Field(None, description="服务提供商")
    content: str = Field(..., description="主要内容（提示词、文本等）")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="额外参数")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="元数据")

@router.post("/services/unified")
@handle_errors(APIError)
@monitor_performance("api_unified_service")
async def unified_service_endpoint(request: UnifiedServiceRequest):
    """统一动态服务端点 - 处理所有服务类型的请求"""
    try:
        print(f"🔧 PRINT DEBUG: 统一服务端点收到请求: {request.service_type}, provider: {request.provider}")
        logger.info(f"🔧 统一服务端点收到请求: {request.service_type}")

        # 验证服务类型
        try:
            service_capability = ServiceCapability(request.service_type)
        except ValueError:
            return APIResponse(
                success=False,
                error=f"Invalid service type: {request.service_type}",
                metadata={"error_code": "INVALID_PARAMETER"}
            )

        # 验证服务提供商 - 优先从顶级字段获取，否则从parameters中获取
        provider_name = request.provider or request.parameters.get('provider')
        try:
            service_provider = ServiceProvider(provider_name) if provider_name else None
        except ValueError:
            return APIResponse(
                success=False,
                error=f"Invalid provider: {provider_name}",
                metadata={"error_code": "INVALID_PARAMETER"}
            )

        # 创建服务请求
        service_request = ServiceRequest(
            capability=service_capability,
            provider=service_provider,
            content=request.content,
            parameters=request.parameters or {},
            metadata={
                **request.metadata,
                "source": "unified_api",
                "endpoint": "/api/services/unified",
                "capability": request.capability
            }
        )

        # 处理请求
        response = await ai_service_manager.process_request(service_request)

        if response.success:
            # 根据服务类型返回适当的字段名
            if request.service_type == "text_generation":
                data_field = 'generated_text'
            elif request.service_type == "translation":
                data_field = 'translated_text'
            elif request.capability == "prompt_enhancement":
                data_field = 'enhanced_prompt'
            else:
                data_field = 'result'

            # 提取实际内容
            if isinstance(response.data, dict):
                # 如果是字典，尝试提取主要内容
                content = (response.data.get('text') or
                          response.data.get('content') or
                          response.data.get('generated_text') or
                          response.data.get('translated_text') or
                          response.data.get('enhanced_prompt') or
                          str(response.data))
            else:
                content = response.data if isinstance(response.data, str) else str(response.data)

            # 简化响应格式 - 只返回必要字段
            return APIResponse(
                success=True,
                data={
                    data_field: content
                },
                metadata={
                    'provider': str(response.provider) if response.provider else None,
                    'service_type': str(response.service_type) if response.service_type else None,
                    'processing_time': response.processing_time
                }
            )
        else:
            return APIResponse(
                success=False,
                error=response.error or f"{request.service_type}处理失败",
                metadata={
                    "error_code": "SERVICE_FAILED",
                    "provider": str(response.provider) if response.provider else None,
                    "service_type": request.service_type
                }
            )

    except Exception as e:
        logger.error(f"❌ 统一服务端点错误: {e}")
        return APIResponse(
            success=False,
            error=f"服务请求失败: {str(e)}",
            metadata={"error_code": "INTERNAL_SERVER_ERROR"}
        )

# AI服务相关路由
@router.get("/services/stats")
@handle_errors(APIError)
@monitor_performance("api_service_stats")
async def get_service_stats():
    """获取服务统计信息"""
    try:
        stats = ai_service_manager.get_service_stats()
        return APIResponse(success=True, data=stats)
    except Exception as e:
        logger.error(f"Failed to get service stats: {e}")
        return APIResponse(
            success=False,
            error="Failed to get service statistics",
            metadata={"error_code": "INTERNAL_SERVER_ERROR"}
        )

@router.get("/services/capabilities")
@handle_errors(APIError)
@monitor_performance("api_service_capabilities")
async def get_service_capabilities():
    """获取服务能力"""
    try:
        capabilities = ai_service_manager.get_available_capabilities()
        return APIResponse(success=True, data=capabilities)
    except Exception as e:
        logger.error(f"Failed to get service capabilities: {e}")
        return APIResponse(
            success=False,
            error="Failed to get service capabilities",
            metadata={"error_code": "INTERNAL_SERVER_ERROR"}
        )

@router.post("/ai/analyze")
@handle_errors(APIError)
@monitor_performance("api_ai_analyze")
async def analyze_text(request: AIAnalysisRequest):
    """文案分析API"""
    try:
        # 验证服务类型
        try:
            service_capability = ServiceCapability(request.service_type)
        except ValueError:
            return APIResponse(
                success=False,
                error=f"Invalid service type: {request.service_type}",
                metadata={"error_code": "INVALID_PARAMETER"}
            )

        # 验证服务提供商
        try:
            service_provider = ServiceProvider(request.provider) if request.provider else None
        except ValueError:
            return APIResponse(
                success=False,
                error=f"Invalid provider: {request.provider}",
                metadata={"error_code": "INVALID_PARAMETER"}
            )

        # 创建服务请求
        service_request = ServiceRequest(
            capability=service_capability,
            provider=service_provider,
            content=request.prompt,
            parameters=request.parameters or {},
            metadata={
                "source": "web_api",
                "endpoint": "/api/ai/analyze"
            }
        )

        # 处理请求
        response = await ai_service_manager.process_request(service_request)

        return APIResponse(
            success=response.success,
            data=response.data,
            error=response.error,
            metadata={
                "provider": response.provider.value if response.provider else None,
                "service_type": response.service_type.value if response.service_type else None,
                "processing_time": response.processing_time
            }
        )

    except HTTPException:
        raise
    except BasePluginError as e:
        logger.error(f"Plugin error in analyze_text: {e}")
        return APIResponse(
            success=False,
            error=str(e),
            metadata={"error_code": e.error_code.value}
        )
    except Exception as e:
        logger.exception(f"Unexpected error in analyze_text: {e}")
        return APIResponse(
            success=False,
            error="Internal server error",
            metadata={"error_code": "INTERNAL_SERVER_ERROR"}
        )

@router.post("/ai/generate")
@handle_errors(APIError)
@monitor_performance("api_ai_generate")
async def generate_content(request: AIGenerationRequest):
    """内容生成API - 已弃用，重定向到统一端点"""
    logger.warning("⚠️ 使用已弃用的端点 /api/ai/generate，请迁移到 /api/services/unified")

    try:
        # 转换为统一请求格式
        unified_request = UnifiedServiceRequest(
            service_type=request.service_type,
            provider=request.provider,
            content=request.prompt,
            parameters=request.parameters,
            metadata={
                "source": "legacy_api",
                "original_endpoint": "/api/ai/generate",
                "migrated_to_unified": True
            }
        )

        # 调用统一端点
        return await unified_service_endpoint(unified_request)

    except HTTPException:
        raise
    except BasePluginError as e:
        logger.error(f"Plugin error in generate_content: {e}")
        return APIResponse(
            success=False,
            error=str(e),
            metadata={"error_code": e.error_code.value}
        )
    except Exception as e:
        logger.exception(f"Unexpected error in generate_content: {e}")
        return APIResponse(
            success=False,
            error="Internal server error",
            metadata={"error_code": "INTERNAL_SERVER_ERROR"}
        )

@router.get("/debug/mcp-status")
async def debug_mcp_status():
    """调试MCP连接状态"""
    try:
        from ..services.fastmcp_server import fastmcp_manager

        status = {
            "initialized": fastmcp_manager._initialized,
            "available_servers": fastmcp_manager.get_available_servers(),
            "server_info": {}
        }

        for server_name in fastmcp_manager.get_available_servers():
            server_info = fastmcp_manager.server_info.get(server_name)
            if server_info:
                status["server_info"][server_name] = {
                    "is_connected": server_info.is_connected,
                    "last_error": server_info.last_error,
                    "capabilities": server_info.capabilities,
                    "available_tools": server_info.available_tools
                }

        return APIResponse(success=True, data=status)
    except Exception as e:
        logger.error(f"Debug MCP status error: {e}")
        return APIResponse(success=False, error=str(e))

@router.post("/debug/test-post")
async def debug_test_post(request: dict):
    """测试POST请求是否能到达路由"""
    logger.info(f"🔧 DEBUG: Test POST request received: {request}")
    return APIResponse(success=True, data={"message": "POST request received", "request": request})

@router.get("/health/detailed")
@monitor_performance("api_health_detailed")
async def detailed_health_check():
    """详细健康检查"""
    try:
        service_stats = ai_service_manager.get_service_stats()
        capabilities = ai_service_manager.get_available_capabilities()

        # 计算整体健康状态
        total_services = len(service_stats)
        available_services = sum(1 for stats in service_stats.values() if stats['is_available'])
        health_percentage = (available_services / total_services * 100) if total_services > 0 else 0

        overall_status = "healthy" if health_percentage >= 50 else "degraded" if health_percentage > 0 else "unhealthy"

        return APIResponse(
            success=True,
            data={
                "status": overall_status,
                "health_percentage": health_percentage,
                "total_services": total_services,
                "available_services": available_services,
                "services": service_stats,
                "capabilities": capabilities
            },
            metadata={
                "timestamp": ai_service_manager.registry.services[list(ai_service_manager.registry.services.keys())[0]].get_stats()['total_requests'] if service_stats else 0
            }
        )

    except Exception as e:
        logger.error(f"Failed to get detailed health status: {e}")
        return APIResponse(
            success=False,
            error="Failed to get health status",
            data={"status": "error"}
        )

# 配置相关路由
@router.get("/config/status")
@monitor_performance("api_config_status")
async def get_config_status():
    """获取配置状态"""
    try:
        from ..core import config_manager

        # 获取配置源信息
        config_sources = []
        for source in config_manager.config_sources:
            config_sources.append({
                "type": source.__class__.__name__,
                "priority": source.priority
            })

        return APIResponse(
            success=True,
            data={
                "sources": config_sources,
                "total_sources": len(config_sources)
            }
        )

    except Exception as e:
        logger.error(f"Failed to get config status: {e}")
        return APIResponse(
            success=False,
            error="Failed to get configuration status",
            metadata={"error_code": "INTERNAL_SERVER_ERROR"}
        )

# 系统信息路由
@router.get("/system/info")
@monitor_performance("api_system_info")
async def get_system_info():
    """获取系统信息"""
    try:
        import sys
        import platform
        from pathlib import Path

        # 获取项目根目录
        project_root = Path(__file__).parent.parent.parent

        return APIResponse(
            success=True,
            data={
                "python_version": sys.version,
                "platform": platform.platform(),
                "architecture": platform.architecture(),
                "processor": platform.processor(),
                "project_root": str(project_root),
                "app_version": "0.1.0"
            }
        )

    except Exception as e:
        logger.error(f"Failed to get system info: {e}")
        return APIResponse(
            success=False,
            error="Failed to get system information",
            metadata={"error_code": "INTERNAL_SERVER_ERROR"}
        )

# 错误处理路由
@router.get("/errors/stats")
@monitor_performance("api_error_stats")
async def get_error_stats():
    """获取错误统计"""
    try:
        from ..core import error_handler

        stats = error_handler.get_error_stats()

        return APIResponse(
            success=True,
            data={
                "error_counts": stats,
                "total_errors": sum(stats.values())
            }
        )

    except Exception as e:
        logger.error(f"Failed to get error stats: {e}")
        return APIResponse(
            success=False,
            error="Failed to get error statistics",
            metadata={"error_code": "INTERNAL_SERVER_ERROR"}
        )

# 语音合成路由
@router.get("/speech/voices/{voice_id}/preview")
@monitor_performance("api_voice_preview")
async def get_voice_preview(voice_id: str, provider: str = "elevenlabs"):
    """获取语音预览/试听音频 - 避免浪费API费用"""
    try:
        logger.info(f"🎵 获取语音预览: voice_id={voice_id}, provider={provider}")

        if provider.lower() == "elevenlabs":
            # ElevenLabs语音预览
            from ..services.fastmcp_server import fastmcp_manager

            if not fastmcp_manager._initialized:
                return APIResponse(
                    success=False,
                    error="MCP服务未初始化"
                )

            if 'elevenlabs' not in fastmcp_manager.server_info:
                return APIResponse(
                    success=False,
                    error="ElevenLabs MCP服务不可用"
                )

            server_info = fastmcp_manager.server_info['elevenlabs']
            if not server_info.is_connected:
                return APIResponse(
                    success=False,
                    error="ElevenLabs MCP服务未连接"
                )

            try:
                # 调用ElevenLabs获取语音详细信息（包含预览URL）
                result = await fastmcp_manager.call_tool('elevenlabs', 'search_voices', {
                    'search': voice_id,
                    'sort': 'name',
                    'sort_direction': 'asc'
                })

                if hasattr(result, 'content') and isinstance(result.content, list):
                    for item in result.content:
                        if hasattr(item, 'text'):
                            # 解析ElevenLabs响应查找预览URL
                            import json
                            try:
                                voices_data = json.loads(item.text)
                                if isinstance(voices_data, dict) and 'voices' in voices_data:
                                    for voice in voices_data['voices']:
                                        if voice.get('voice_id') == voice_id or voice.get('id') == voice_id:
                                            preview_url = voice.get('preview_url') or voice.get('sample_url')
                                            if preview_url:
                                                return APIResponse(
                                                    success=True,
                                                    data={
                                                        'voice_id': voice_id,
                                                        'name': voice.get('name', ''),
                                                        'preview_url': preview_url,
                                                        'provider': 'elevenlabs',
                                                        'description': voice.get('description', ''),
                                                        'category': voice.get('category', 'premade')
                                                    }
                                                )
                            except json.JSONDecodeError:
                                continue

                # 如果没有找到预览URL，生成一个短的试听样本
                logger.info(f"⚠️ 未找到预览URL，生成试听样本: {voice_id}")
                return await generate_voice_sample(voice_id, provider)

            except Exception as e:
                logger.error(f"❌ ElevenLabs预览获取失败: {e}")
                return APIResponse(
                    success=False,
                    error=f"获取ElevenLabs语音预览失败: {str(e)}"
                )

        elif provider.lower() == "minimax":
            # MiniMax语音预览（从克隆语音存储中获取demo_url）
            try:
                from ..storage.clone_voices import clone_voice_storage
                clone_voices = clone_voice_storage.get_clone_voices(provider="minimax")

                for voice in clone_voices:
                    if voice.get("voice_id") == voice_id:
                        demo_url = voice.get("demo_url")
                        logger.info(f"🔍 MiniMax语音 {voice_id} 的demo_url: {demo_url}")
                        if demo_url:
                            logger.info(f"✅ 使用MiniMax demo_url预览: {demo_url}")
                            return APIResponse(
                                success=True,
                                data={
                                    'voice_id': voice_id,
                                    'name': voice.get('name', ''),
                                    'preview_url': demo_url,
                                    'provider': 'minimax',
                                    'description': f"MiniMax克隆语音免费预览: {voice.get('name', '')}",
                                    'category': 'clone',
                                    'is_demo_url': True,  # 标记这是真正的demo_url
                                    'cost': 'free'  # 标记免费
                                }
                            )
                        else:
                            logger.warning(f"⚠️ MiniMax语音 {voice_id} 没有demo_url")

                # 如果没有找到，生成试听样本
                logger.info(f"⚠️ MiniMax未找到预览URL，生成试听样本: {voice_id}")
                return await generate_voice_sample(voice_id, provider)

            except Exception as e:
                logger.error(f"❌ MiniMax预览获取失败: {e}")
                return APIResponse(
                    success=False,
                    error=f"获取MiniMax语音预览失败: {str(e)}"
                )

        else:
            return APIResponse(
                success=False,
                error=f"不支持的服务提供商: {provider}"
            )

    except Exception as e:
        logger.error(f"❌ 语音预览获取失败: {e}")
        return APIResponse(
            success=False,
            error=f"语音预览获取失败: {str(e)}"
        )

async def generate_voice_sample(voice_id: str, provider: str) -> APIResponse:
    """生成语音试听样本（临时文件，不存入媒体库）"""
    try:
        # 准备试听文本
        sample_text = "Hello, this is a voice sample. 你好，这是语音试听。" if provider.lower() == "elevenlabs" else "你好，这是语音试听样本。"
        logger.info(f"🎵 生成{provider}试听样本: voice_id={voice_id}")

        # 直接调用AI服务，跳过媒体库集成

        ai_request = ServiceRequest(
            capability=ServiceCapability.SPEECH_SYNTHESIS,
            provider=ServiceProvider(provider.upper()),
            content=sample_text,
            parameters={'voice_id': voice_id, 'is_preview': True}  # 标记为预览，避免媒体库集成
        )

        result = await ai_service_manager.process_request(ai_request)

        if result.success:
            # 简化URL提取逻辑
            audio_url = result.data.get('url') or result.data.get('audio_url')

            # 如果是文件路径，转换为URL
            if not audio_url and result.data.get('file_path'):
                file_path = result.data['file_path']
                # 修复重复路径问题
                if 'Pro/output/media_library/' in file_path:
                    # 提取文件名，重新构建正确的URL
                    import os
                    file_name = os.path.basename(file_path)
                    audio_url = f"/output/media_library/{file_name}"
                else:
                    from ..core.path_manager import path_manager
                    audio_url = path_manager.file_to_url(file_path)

            return APIResponse(
                success=True,
                data={
                    'voice_id': voice_id,
                    'name': f"试听样本 - {voice_id}",
                    'preview_url': audio_url,
                    'provider': provider,
                    'description': f"试听样本: '{sample_text}'",
                    'category': 'sample',
                    'is_generated_sample': True,
                    'sample_text': sample_text,
                    'is_bilingual': provider.lower() == "elevenlabs"
                }
            )
        else:
            return APIResponse(
                success=False,
                error=f"生成试听样本失败: {result.error}"
            )

    except Exception as e:
        logger.error(f"❌ 生成试听样本失败: {e}")
        return APIResponse(
            success=False,
            error=f"生成试听样本失败: {str(e)}"
        )

@router.get("/speech/voices/previews")
@monitor_performance("api_voices_previews")
async def get_voices_with_previews(provider: str = "elevenlabs", limit: int = 10):
    """获取带预览URL的语音列表 - 优化版，避免重复API调用"""
    try:
        logger.info(f"🎵 获取带预览的语音列表: provider={provider}, limit={limit}")

        voices_with_previews = []

        if provider.lower() == "elevenlabs":
            # 获取ElevenLabs语音列表
            elevenlabs_voices = await get_elevenlabs_voices_from_mcp()

            for voice in elevenlabs_voices[:limit]:
                voice_data = {
                    'id': voice.get('id'),
                    'name': voice.get('name'),
                    'provider': 'elevenlabs',
                    'category': voice.get('category', 'premade'),
                    'language': voice.get('language', 'en'),
                    'description': voice.get('description', ''),
                    'preview_url': voice.get('preview_url', ''),
                    'has_preview': bool(voice.get('preview_url'))
                }
                voices_with_previews.append(voice_data)

        elif provider.lower() == "minimax":
            # 获取MiniMax克隆语音（带demo_url）
            try:
                from ..storage.clone_voices import clone_voice_storage
                clone_voices = clone_voice_storage.get_clone_voices(provider="minimax")

                for voice in clone_voices[:limit]:
                    voice_data = {
                        'id': voice.get('voice_id'),
                        'name': voice.get('name'),
                        'provider': 'minimax',
                        'category': 'clone',
                        'language': 'zh',  # MiniMax主要支持中文
                        'description': f"MiniMax克隆语音: {voice.get('name', '')}",
                        'preview_url': voice.get('demo_url', ''),
                        'has_preview': bool(voice.get('demo_url')),
                        'created_at': voice.get('created_at'),
                        'usage_count': voice.get('usage_count', 0)
                    }
                    voices_with_previews.append(voice_data)
            except Exception as e:
                logger.warning(f"获取MiniMax克隆语音失败: {e}")

        elif provider.lower() == "all":
            # 获取所有提供商的语音
            # ElevenLabs
            elevenlabs_voices = await get_elevenlabs_voices_from_mcp()
            for voice in elevenlabs_voices[:limit//2]:
                voice_data = {
                    'id': voice.get('id'),
                    'name': voice.get('name'),
                    'provider': 'elevenlabs',
                    'category': voice.get('category', 'premade'),
                    'language': voice.get('language', 'en'),
                    'description': voice.get('description', ''),
                    'preview_url': voice.get('preview_url', ''),
                    'has_preview': bool(voice.get('preview_url'))
                }
                voices_with_previews.append(voice_data)

            # MiniMax
            try:
                from ..storage.clone_voices import clone_voice_storage
                clone_voices = clone_voice_storage.get_clone_voices(provider="minimax")

                for voice in clone_voices[:limit//2]:
                    voice_data = {
                        'id': voice.get('voice_id'),
                        'name': voice.get('name'),
                        'provider': 'minimax',
                        'category': 'clone',
                        'language': 'zh',
                        'description': f"MiniMax克隆语音: {voice.get('name', '')}",
                        'preview_url': voice.get('demo_url', ''),
                        'has_preview': bool(voice.get('demo_url')),
                        'created_at': voice.get('created_at'),
                        'usage_count': voice.get('usage_count', 0)
                    }
                    voices_with_previews.append(voice_data)
            except Exception as e:
                logger.warning(f"获取MiniMax克隆语音失败: {e}")

        # 统计信息
        total_voices = len(voices_with_previews)
        voices_with_preview = len([v for v in voices_with_previews if v['has_preview']])
        voices_without_preview = total_voices - voices_with_preview

        return APIResponse(
            success=True,
            data={
                'voices': voices_with_previews,
                'stats': {
                    'total': total_voices,
                    'with_preview': voices_with_preview,
                    'without_preview': voices_without_preview,
                    'preview_coverage': f"{(voices_with_preview/total_voices*100):.1f}%" if total_voices > 0 else "0%"
                },
                'usage_tips': [
                    "🎵 有预览的语音可以直接试听，无需消耗API费用",
                    "🔊 没有预览的语音会生成短试听样本（消耗少量API费用）",
                    "💡 建议优先使用有预览的语音进行测试",
                    "⚡ 预览功能帮助您在正式合成前了解语音效果"
                ]
            },
            metadata={
                'provider': provider,
                'limit': limit,
                'api_cost': 'free_for_previews'
            }
        )

    except Exception as e:
        logger.error(f"❌ 获取语音预览列表失败: {e}")
        return APIResponse(
            success=False,
            error=f"获取语音预览列表失败: {str(e)}"
        )

@router.post("/speech/synthesize")
@monitor_performance("api_speech_synthesize")
async def synthesize_speech(request: SpeechSynthesisRequest):
    """语音合成API"""
    try:
        # 创建AI请求
        provider_name = "elevenlabs" if request.provider == "elevenlabs" else "minimax"
        ai_request = ServiceRequest(
            capability=ServiceCapability.SPEECH_SYNTHESIS,
            provider=ServiceProvider(provider_name.upper()),
            content=request.text,
            parameters={
                'voice_id': request.voice_id,
                'model_id': request.model_id,
                'output_format': request.output_format
            }
        )

        # 处理请求
        response = await ai_service_manager.process_request(ai_request)

        if response.success:
            # 更新语音使用次数（异步执行，不影响主要响应）
            try:
                await update_voice_usage_count(request.voice_id, request.provider)
            except Exception as e:
                logger.warning(f"Failed to update voice usage count: {e}")

            # 处理返回数据，确保包含前端需要的字段
            result_data = response.data.copy() if response.data else {}

            # 添加audio_url字段供前端使用
            if not result_data.get('audio_url'):
                # 处理文件路径类型
                if result_data.get('file_path'):
                    file_path = result_data['file_path']
                    # 修复重复路径问题并生成正确的URL
                    if 'Pro/output/media_library/' in file_path:
                        import os
                        file_name = os.path.basename(file_path)
                        audio_url = f"/output/media_library/{file_name}"
                    else:
                        from ..core.path_manager import path_manager
                        audio_url = path_manager.file_to_url(file_path)

                    if audio_url:
                        result_data['audio_url'] = audio_url
                        result_data['audio_file'] = audio_url  # 兼容性字段

                # 处理URL类型（MiniMax等云端服务）
                elif result_data.get('type') == 'url' and result_data.get('url'):
                    result_data['audio_url'] = result_data['url']
                    result_data['audio_file'] = result_data['url']  # 兼容性字段

            return APIResponse(
                success=True,
                data=result_data,
                metadata=response.metadata
            )
        else:
            return APIResponse(
                success=False,
                error=response.error or "语音合成失败",
                metadata=response.metadata
            )

    except APIError as e:
        # API错误，保留原始错误信息
        logger.error(f"Speech synthesis API error: {e}")
        return APIResponse(
            success=False,
            error=str(e),
            metadata={
                "error_code": e.error_code.value if hasattr(e, 'error_code') else "API_ERROR",
                "provider": request.provider
            }
        )
    except Exception as e:
        # 未知错误，提供更详细的错误信息
        logger.error(f"Speech synthesis unexpected error: {e}", exc_info=True)

        # 根据错误类型提供更具体的错误信息
        error_message = "语音合成服务暂时不可用"
        error_code = "SPEECH_SYNTHESIS_ERROR"

        if "not found" in str(e).lower():
            error_message = "MCP服务器未找到，请检查服务配置"
            error_code = "MCP_SERVER_NOT_FOUND"
        elif "not connected" in str(e).lower():
            error_message = "MCP服务器连接失败，请检查网络连接"
            error_code = "MCP_CONNECTION_ERROR"
        elif "timeout" in str(e).lower():
            error_message = "请求超时，请稍后重试"
            error_code = "REQUEST_TIMEOUT"
        elif "api_key" in str(e).lower() or "unauthorized" in str(e).lower():
            error_message = "API密钥认证失败，请检查配置"
            error_code = "API_AUTHENTICATION_ERROR"

        return APIResponse(
            success=False,
            error=error_message,
            metadata={
                "error_code": error_code,
                "provider": request.provider,
                "details": str(e)
            }
        )

@router.get("/speech/voices/debug")
@monitor_performance("api_speech_voices_debug")
async def debug_mcp_tools():
    """调试MCP工具和ElevenLabs原始数据"""
    try:
        from ..services.simplified_ai_services import SimplifiedAIServiceManager
        from ..services.fastmcp_server import fastmcp_manager

        debug_info = {
            "elevenlabs": {"available": False, "tools": [], "error": None, "raw_voices": []},
            "minimax": {"available": False, "tools": [], "error": None},
            "fastmcp_manager": {"initialized": False, "servers": [], "error": None}
        }

        # 获取ElevenLabs原始语音数据
        if fastmcp_manager._initialized and 'elevenlabs' in fastmcp_manager.server_info:
            try:
                # 尝试不同的工具来获取语音
                tools_to_try = ['search_voices', 'search_voice_library']
                for tool_name in tools_to_try:
                    try:
                        result = await fastmcp_manager.call_tool('elevenlabs', tool_name, {
                            'search': '',
                            'sort': 'name',
                            'sort_direction': 'asc'
                        })
                        debug_info["elevenlabs"][f"raw_{tool_name}"] = str(result)[:1000] + "..." if len(str(result)) > 1000 else str(result)
                        debug_info["elevenlabs"]["available"] = True
                        break
                    except Exception as tool_error:
                        debug_info["elevenlabs"][f"error_{tool_name}"] = str(tool_error)

                # 尝试获取可用工具列表
                try:
                    client = fastmcp_manager.clients.get('elevenlabs')
                    if client:
                        # 这里应该有获取工具列表的方法
                        debug_info["elevenlabs"]["client_available"] = True
                except Exception as e:
                    debug_info["elevenlabs"]["client_error"] = str(e)

            except Exception as e:
                debug_info["elevenlabs"]["error"] = str(e)

        # 检查ElevenLabs MCP服务器
        try:
            elevenlabs_service = ai_service_manager.registry.get_service("elevenlabs")
            if elevenlabs_service and hasattr(elevenlabs_service, 'mcp_manager'):
                mcp_manager = elevenlabs_service.mcp_manager
                if 'elevenlabs' in mcp_manager.server_info:
                    server_info = mcp_manager.server_info['elevenlabs']
                    debug_info["elevenlabs"]["available"] = server_info.is_connected
                    debug_info["elevenlabs"]["tools"] = server_info.available_tools or []
                    debug_info["elevenlabs"]["error"] = server_info.last_error
        except Exception as e:
            debug_info["elevenlabs"]["error"] = str(e)

        # 检查Minimax MCP服务器
        try:
            minimax_service = ai_service_manager.registry.get_service("minimax")
            if minimax_service and hasattr(minimax_service, 'mcp_manager'):
                mcp_manager = minimax_service.mcp_manager
                if 'minimax' in mcp_manager.server_info:
                    server_info = mcp_manager.server_info['minimax']
                    debug_info["minimax"]["available"] = server_info.is_connected
                    debug_info["minimax"]["tools"] = server_info.available_tools or []
                    debug_info["minimax"]["error"] = server_info.last_error
        except Exception as e:
            debug_info["minimax"]["error"] = str(e)

        # 检查FastMCP管理器状态
        try:
            from ..services.fastmcp_server import fastmcp_manager
            debug_info["fastmcp_manager"]["initialized"] = fastmcp_manager._initialized
            debug_info["fastmcp_manager"]["servers"] = list(fastmcp_manager.server_info.keys())

            # 获取每个服务器的详细信息
            for server_name, server_info in fastmcp_manager.server_info.items():
                debug_info[server_name] = {
                    "available": server_info.is_connected,
                    "tools": server_info.available_tools or [],
                    "error": server_info.last_error,
                    "capabilities": server_info.capabilities or []
                }
        except Exception as e:
            debug_info["fastmcp_manager"]["error"] = str(e)

        return {
            "success": True,
            "data": debug_info
        }

    except Exception as e:
        logger.error(f"❌ Debug MCP tools failed: {e}")
        return {
            "success": False,
            "error": f"调试失败: {str(e)}"
        }

# 废弃的语音搜索API端点已移除

@router.get("/speech/voices/favorites")
@monitor_performance("api_speech_voices_favorites")
async def get_favorite_voices():
    """获取常用语音列表 - 只返回验证通过的语音"""
    try:
        favorites = await load_favorite_voices()

        # 验证ElevenLabs语音ID的有效性
        validated_favorites = []
        for voice in favorites:
            if voice.get('provider') == 'elevenlabs':
                # 验证ElevenLabs语音ID
                is_valid = await validate_elevenlabs_voice_id(voice.get('voice_id'))
                if is_valid:
                    validated_favorites.append(voice)
                else:
                    logger.warning(f"⚠️ Invalid ElevenLabs voice ID: {voice.get('voice_id')} - {voice.get('voice_name')}")
            else:
                # 非ElevenLabs语音直接添加
                validated_favorites.append(voice)

        # 如果验证后的列表与原列表不同，保存更新
        if len(validated_favorites) != len(favorites):
            await save_favorite_voices(validated_favorites)
            logger.info(f"🧹 Cleaned up invalid voices: {len(favorites)} -> {len(validated_favorites)}")

        return APIResponse(
            success=True,
            data={"voices": validated_favorites},
            metadata={"total": len(validated_favorites)}
        )
    except Exception as e:
        logger.error(f"Failed to get favorite voices: {e}")
        return APIResponse(
            success=False,
            error="获取常用语音列表失败",
            metadata={"error_code": "FAVORITES_LOAD_ERROR"}
        )

@router.post("/speech/voices/favorites")
@monitor_performance("api_speech_voices_favorites_add")
async def add_favorite_voice(request: FavoriteVoiceRequest):
    """添加语音到常用列表"""
    try:
        # 加载现有的常用语音
        favorites = await load_favorite_voices()

        # 检查是否已存在
        existing = next((v for v in favorites if v['voice_id'] == request.voice_id and v['provider'] == request.provider), None)
        if existing:
            return APIResponse(
                success=False,
                error="该语音已在常用列表中",
                metadata={"error_code": "VOICE_ALREADY_EXISTS"}
            )

        # 如果是ElevenLabs语音，尝试从API获取正确的语音信息
        voice_name = request.voice_name
        description = request.description

        if request.provider == 'elevenlabs':
            try:
                voice_info = await get_voice_info_from_elevenlabs(request.voice_id)
                if voice_info and voice_info.get('name'):
                    voice_name = voice_info['name']
                    description = voice_info.get('description', description)
                    logger.info(f"✅ Got official voice info: {voice_name}")
            except Exception as e:
                logger.warning(f"Failed to get official voice info, using provided name: {e}")

        # 清理语音名称（移除HTML标签和多余空白）
        if voice_name:
            import re
            # 移除HTML标签
            voice_name = re.sub(r'<[^>]+>', '', voice_name)
            # 移除多余的空白字符
            voice_name = ' '.join(voice_name.split())
            # 移除"(搜索结果)"后缀
            voice_name = voice_name.replace('(搜索结果)', '').strip()

        # 创建新的常用语音条目
        new_favorite = {
            "voice_id": request.voice_id,
            "voice_name": voice_name or request.voice_id,
            "provider": request.provider,
            "description": description or "语音信息",
            "category": request.category or "user_added",
            "language": request.language or "en",
            "preview_url": request.preview_url,
            "added_at": datetime.now().isoformat(),
            "usage_count": 0
        }

        # 添加到列表
        favorites.append(new_favorite)

        # 保存到文件
        await save_favorite_voices(favorites)

        logger.info(f"✅ Added voice '{new_favorite['voice_name']}' to favorites")
        return APIResponse(
            success=True,
            data=new_favorite,
            metadata={"message": "语音已添加到常用列表"}
        )

    except Exception as e:
        logger.error(f"Failed to add favorite voice: {e}")
        return APIResponse(
            success=False,
            error="添加常用语音失败",
            metadata={"error_code": "FAVORITES_ADD_ERROR"}
        )

@router.delete("/speech/voices/favorites/{voice_id}")
@monitor_performance("api_speech_voices_favorites_remove")
async def remove_favorite_voice(voice_id: str, provider: str = Query(..., description="语音提供商")):
    """从常用列表中移除语音"""
    try:
        # 加载现有的常用语音
        favorites = await load_favorite_voices()

        # 查找并移除
        original_count = len(favorites)
        favorites = [v for v in favorites if not (v['voice_id'] == voice_id and v['provider'] == provider)]

        if len(favorites) == original_count:
            return APIResponse(
                success=False,
                error="未找到指定的常用语音",
                metadata={"error_code": "VOICE_NOT_FOUND"}
            )

        # 保存更新后的列表
        await save_favorite_voices(favorites)

        logger.info(f"✅ Removed voice '{voice_id}' from favorites")
        return APIResponse(
            success=True,
            data={"removed_voice_id": voice_id, "provider": provider},
            metadata={"message": "语音已从常用列表中移除"}
        )

    except Exception as e:
        logger.error(f"Failed to remove favorite voice: {e}")
        return APIResponse(
            success=False,
            error="移除常用语音失败",
            metadata={"error_code": "FAVORITES_REMOVE_ERROR"}
        )

@router.post("/speech/voices/favorites/cleanup")
@monitor_performance("api_speech_voices_favorites_cleanup")
async def cleanup_favorite_voices():
    """清理常用语音列表，修复名称和获取正确的官方信息"""
    try:
        # 加载现有的常用语音
        favorites = await load_favorite_voices()
        updated_favorites = []

        for voice in favorites:
            voice_id = voice.get('voice_id')
            provider = voice.get('provider')

            if provider == 'elevenlabs' and voice_id:
                try:
                    # 从ElevenLabs API获取正确的语音信息
                    voice_info = await get_voice_info_from_elevenlabs(voice_id)

                    # 更新语音信息
                    updated_voice = {
                        "voice_id": voice_id,
                        "voice_name": voice_info.get('name', voice_id),
                        "provider": provider,
                        "description": voice_info.get('description', voice.get('description', '')),
                        "category": voice_info.get('category', voice.get('category', 'user')),
                        "language": voice_info.get('language', voice.get('language', 'en')),
                        "preview_url": voice_info.get('preview_url', voice.get('preview_url', '')),
                        "added_at": voice.get('added_at', datetime.now().isoformat()),
                        "usage_count": voice.get('usage_count', 0),
                        "last_used": voice.get('last_used')
                    }

                    updated_favorites.append(updated_voice)
                    logger.info(f"✅ Updated voice info for '{voice_info.get('name', voice_id)}'")

                except Exception as e:
                    logger.warning(f"Failed to update voice {voice_id}: {e}")
                    # 如果获取失败，保留原始信息但清理名称
                    cleaned_name = voice.get('voice_name', voice_id)
                    if cleaned_name:
                        import re
                        cleaned_name = re.sub(r'<[^>]+>', '', cleaned_name)
                        cleaned_name = ' '.join(cleaned_name.split())
                        cleaned_name = cleaned_name.replace('(搜索结果)', '').strip()

                    updated_voice = voice.copy()
                    updated_voice['voice_name'] = cleaned_name or voice_id
                    updated_favorites.append(updated_voice)
            else:
                # 非ElevenLabs语音，只清理名称
                cleaned_name = voice.get('voice_name', voice_id)
                if cleaned_name:
                    import re
                    cleaned_name = re.sub(r'<[^>]+>', '', cleaned_name)
                    cleaned_name = ' '.join(cleaned_name.split())
                    cleaned_name = cleaned_name.replace('(搜索结果)', '').strip()

                updated_voice = voice.copy()
                updated_voice['voice_name'] = cleaned_name or voice_id
                updated_favorites.append(updated_voice)

        # 保存更新后的列表
        await save_favorite_voices(updated_favorites)

        logger.info(f"✅ Cleaned up {len(updated_favorites)} favorite voices")
        return APIResponse(
            success=True,
            data={"voices": updated_favorites},
            metadata={"message": f"已清理 {len(updated_favorites)} 个常用语音", "total": len(updated_favorites)}
        )

    except Exception as e:
        logger.error(f"Failed to cleanup favorite voices: {e}")
        return APIResponse(
            success=False,
            error="清理常用语音失败",
            metadata={"error_code": "FAVORITES_CLEANUP_ERROR"}
        )

@router.get("/speech/voices")
@monitor_performance("api_speech_voices")
async def get_available_voices(provider: Optional[str] = None):
    """获取可用语音列表"""
    all_voices = []  # 初始化变量，确保在所有代码路径中都有定义

    try:
        # 如果指定了提供商，只获取该提供商的语音
        providers_to_check = []
        if provider:
            if provider.lower() == 'elevenlabs':
                providers_to_check = ["elevenlabs"]
            elif provider.lower() == 'minimax':
                providers_to_check = ["minimax"]
        else:
            # 获取所有提供商的语音
            providers_to_check = ["elevenlabs", "minimax"]

        # 获取ElevenLabs语音列表 - 统一使用MCP服务器
        if "elevenlabs" in providers_to_check:
            elevenlabs_voices = await get_elevenlabs_voices_from_mcp()
            all_voices.extend(elevenlabs_voices)

        # 获取Minimax语音列表 - 统一使用MCP服务器
        if "minimax" in providers_to_check:
            minimax_voices = await get_minimax_voices_from_mcp()
            all_voices.extend(minimax_voices)

        logger.info(f"✅ Retrieved {len(all_voices)} total voices from {len(providers_to_check)} provider(s)")

    except Exception as e:
        logger.error(f"Error getting voices: {e}")
        # 在异常情况下，all_voices已经初始化为空列表

    # 如果没有获取到任何语音，返回空列表而不是硬编码数据
    if not all_voices:
        logger.warning("No voices retrieved from any provider, returning empty list")
        logger.warning("Please check MCP server connections and API keys")

    return APIResponse(
        success=True,
        data={"voices": all_voices}
    )

@router.get("/speech/voices/clones")
@monitor_performance("api_speech_voices_clones")
async def get_clone_voices():
    """获取用户的声音克隆列表"""
    clone_voices = []

    try:
        # ElevenLabs克隆声音获取（静默处理错误）
        elevenlabs_api_key = get_config('ai_services.elevenlabs.api_key')
        if elevenlabs_api_key:
            try:
                logger.debug("🔍 尝试获取ElevenLabs克隆声音...")
                elevenlabs_clones = await get_elevenlabs_clone_voices()
                if elevenlabs_clones:
                    clone_voices.extend(elevenlabs_clones)
                    logger.info(f"✅ Retrieved {len(elevenlabs_clones)} ElevenLabs clone voices")
            except Exception as e:
                # 静默处理ElevenLabs错误，不影响MiniMax功能
                logger.debug(f"ElevenLabs克隆声音获取失败（已忽略）: {e}")
        else:
            logger.debug("ElevenLabs API密钥未配置，跳过ElevenLabs克隆声音获取")

        # MiniMax克隆声音获取（从本地存储）
        minimax_api_key = get_config('ai_services.minimax.api_key')
        if minimax_api_key:
            try:
                logger.info("🔍 开始获取MiniMax克隆声音（本地存储）...")
                from ..storage.clone_voices import clone_voice_storage
                minimax_clones = clone_voice_storage.get_clone_voices(provider="minimax")
                if minimax_clones:
                    # 转换为标准格式
                    formatted_clones = []
                    for clone in minimax_clones:
                        formatted_clone = {
                            "id": clone.get("voice_id"),
                            "name": clone.get("name"),
                            "provider": "minimax",
                            "category": "clone",
                            "is_generated": True,
                            "created_at": clone.get("created_at"),
                            "usage_count": clone.get("usage_count", 0),
                            "demo_url": clone.get("demo_url")
                        }
                        formatted_clones.append(formatted_clone)

                    clone_voices.extend(formatted_clones)
                    logger.info(f"✅ Retrieved {len(formatted_clones)} MiniMax clone voices from local storage")
            except Exception as e:
                logger.error(f"❌ Failed to get MiniMax clone voices: {e}")
        else:
            logger.info("⚠️ MiniMax API密钥未配置，跳过MiniMax克隆声音获取")

        logger.info(f"✅ Retrieved {len(clone_voices)} total clone voices")

    except Exception as e:
        logger.error(f"Error getting clone voices: {e}")

    return APIResponse(
        success=True,
        data={"voices": clone_voices}
    )

@router.post("/speech/voices/clone/create")
@monitor_performance("api_speech_voice_clone_create")
async def create_voice_clone(request: Request):
    """创建声音克隆"""
    try:
        # 解析请求数据
        form_data = await request.form()

        name = form_data.get('name', '').strip()
        provider = form_data.get('provider', 'elevenlabs')
        audio_source = form_data.get('audio_source', 'upload')

        if not name:
            return APIResponse(
                success=False,
                error="克隆名称不能为空"
            )

        # 检查提供商配置
        if provider == 'elevenlabs':
            api_key = get_config('ai_services.elevenlabs.api_key')
            if not api_key:
                return APIResponse(
                    success=False,
                    error="ElevenLabs API密钥未配置"
                )
        elif provider == 'minimax':
            api_key = get_config('ai_services.minimax.api_key')
            if not api_key:
                return APIResponse(
                    success=False,
                    error="MiniMax API密钥未配置"
                )

        # 处理音频文件
        audio_files = []
        if audio_source == 'davinci':
            # 从达芬奇提取的音频
            audio_path = form_data.get('audio_path', '')
            if not audio_path or not Path(audio_path).exists():
                return APIResponse(
                    success=False,
                    error="达芬奇音频文件不存在"
                )
            audio_files = [audio_path]
        else:
            # 上传的音频文件
            uploaded_files = form_data.getlist('audio_files')
            if not uploaded_files:
                return APIResponse(
                    success=False,
                    error="请上传音频文件"
                )

            # 保存上传的文件
            upload_dir = Path('./output') / 'voice_clone_uploads'
            upload_dir.mkdir(parents=True, exist_ok=True)

            for file in uploaded_files:
                if hasattr(file, 'filename') and file.filename:
                    file_path = upload_dir / file.filename
                    with open(file_path, 'wb') as f:
                        content = await file.read()
                        f.write(content)
                    audio_files.append(str(file_path))

        if not audio_files:
            return APIResponse(
                success=False,
                error="没有有效的音频文件"
            )

        # 调用相应提供商的克隆API
        if provider == 'elevenlabs':
            result = await create_elevenlabs_voice_clone(name, audio_files)
        elif provider == 'minimax':
            result = await create_minimax_voice_clone(name, audio_files)
        else:
            return APIResponse(
                success=False,
                error=f"不支持的提供商: {provider}"
            )

        if result.get('success'):
            return APIResponse(
                success=True,
                data={
                    'voice_id': result.get('voice_id'),
                    'name': name,
                    'provider': provider,
                    'status': result.get('status', 'created'),
                    'message': f'声音克隆 "{name}" 创建成功'
                }
            )
        else:
            return APIResponse(
                success=False,
                error=result.get('error', '声音克隆创建失败')
            )

    except Exception as e:
        logger.error(f"Error creating voice clone: {e}")
        return APIResponse(
            success=False,
            error=f"创建声音克隆时发生错误: {str(e)}"
        )

@router.get("/speech/voices/available")
@monitor_performance("api_speech_voices_available")
async def get_available_voices():
    """获取真正可用的语音列表 - 简化版

    只返回用户确实可以使用的语音：
    1. 用户自己的语音（100%可用）
    2. ElevenLabs默认语音（100%可用）

    不包含需要验证权限的共享语音库语音
    """
    logger.info("🚀 Starting get_available_voices API call")
    try:
        from ..services.fastmcp_server import fastmcp_manager

        available_voices = []

        # 1. 获取用户保存的常用语音（最高优先级）
        favorite_voices = []
        try:
            favorites = await load_favorite_voices()
            for voice in favorites:
                formatted_voice = {
                    'id': voice.get('voice_id'),
                    'name': voice.get('voice_name'),
                    'category': 'favorite',
                    'provider': voice.get('provider', 'elevenlabs'),
                    'source': 'user',
                    'availability': 'guaranteed',
                    'priority': 3,  # 最高优先级
                    'language': voice.get('language', 'en'),
                    'description': voice.get('description', f"Favorite voice: {voice.get('voice_name')}")
                }
                favorite_voices.append(formatted_voice)
            logger.info(f"✅ Found {len(favorite_voices)} favorite voices")
        except Exception as e:
            logger.warning(f"⚠️ Failed to get favorite voices: {e}")

        # 2. 获取用户自己的语音（100%可用，排除已在常用列表中的）
        user_voices = []
        favorite_voice_ids = {v['id'] for v in favorite_voices}

        try:
            if fastmcp_manager._initialized and 'elevenlabs' in fastmcp_manager.server_info:
                # 尝试两种不同的search_voices调用方式
                all_user_voices = []

                # 尝试多种不同的search_voices调用方式
                search_methods = [
                    # 方式1：按名称升序
                    {'search': '', 'sort': 'name', 'sort_direction': 'asc'},
                    # 方式2：按名称降序
                    {'search': '', 'sort': 'name', 'sort_direction': 'desc'},
                    # 方式3：按创建时间升序
                    {'search': '', 'sort': 'created_at_unix', 'sort_direction': 'asc'},
                    # 方式4：按创建时间降序
                    {'search': '', 'sort': 'created_at_unix', 'sort_direction': 'desc'},
                    # 方式5：无参数
                    {},
                    # 方式6：只有search参数为None
                    {'search': None},
                ]

                for params in search_methods:
                    try:
                        result = await fastmcp_manager.call_tool('elevenlabs', 'search_voices', params)
                        if result:
                            voices = parse_mcp_voices_response(result, 'user')
                            all_user_voices.extend(voices)
                    except Exception:
                        continue

                # 去重（基于ID）
                seen_ids = set()
                unique_voices = []
                for voice in all_user_voices:
                    voice_id = voice.get('id')
                    if voice_id and voice_id not in seen_ids:
                        seen_ids.add(voice_id)
                        unique_voices.append(voice)

                # 排除已在常用列表中的
                user_voices = [v for v in unique_voices if v.get('id') not in favorite_voice_ids]
                logger.info(f"✅ Found {len(user_voices)} user voices (total: {len(unique_voices)})")

        except Exception as e:
            logger.warning(f"⚠️ Failed to get user voices: {e}")

        # 2. 移除硬编码预制语音，始终动态加载
        # 所有语音都通过MCP动态获取，确保实时性和准确性

        # 3. 合并语音列表：常用语音 > 用户语音（移除硬编码默认语音）
        available_voices.extend(favorite_voices)
        available_voices.extend(user_voices)

        # 4. 生成统计信息（移除硬编码默认语音统计）
        stats = {
            'total': len(available_voices),
            'user_voices': len(favorite_voices) + len(user_voices),
            'favorite_voices': len(favorite_voices),
            'dynamic_voices': len(user_voices),  # 动态加载的语音数量
            'guaranteed_available': len(available_voices)  # 所有语音都是100%可用的
        }

        # 5. 生成使用建议
        recommendations = []
        usage_tips = []

        if len(favorite_voices) > 0:
            recommendations.append(f"⭐ 您有 {len(favorite_voices)} 个常用语音")
            usage_tips.append("🎯 常用语音显示在最前面，优先使用")
        else:
            recommendations.append("💡 您还没有保存常用语音")
            usage_tips.append("⭐ 建议将喜欢的语音添加到常用列表")

        total_user_voices = len(favorite_voices) + len(user_voices)
        if total_user_voices > 0:
            recommendations.append(f"✅ 您有 {total_user_voices} 个自己的语音")
            usage_tips.append("🎯 您的语音100%可用，优先使用")
        else:
            recommendations.append("💡 您还没有自己的语音")
            usage_tips.append("📝 建议克隆一些语音到您的账户")

        recommendations.append(f"🔧 系统动态加载 {len(user_voices)} 个可用语音")

        if total_user_voices < 3:
            remaining_slots = 3 - total_user_voices
            recommendations.append(f"💎 免费用户还可添加 {remaining_slots} 个语音")

        usage_tips.extend([
            "✅ 所有显示的语音都可以直接使用",
            "🚫 不显示需要验证权限的共享语音",
            "🎵 可以直接用于文本转语音功能"
        ])

        return APIResponse(
            success=True,
            data={
                "voices": available_voices,
                "stats": stats,
                "recommendations": recommendations,
                "usage_tips": usage_tips,
                "info": {
                    "description": "只显示100%可用的语音",
                    "includes": ["用户语音", "默认语音"],
                    "excludes": ["共享语音库（需要验证权限）"],
                    "free_user_limit": 3
                }
            }
        )

    except Exception as e:
        logger.error(f"Error getting available voices: {e}")
        return APIResponse(
            success=False,
            error=f"Failed to get available voices: {str(e)}"
        )

@router.post("/voice/clone/upload-sample")
@monitor_performance("api_voice_clone_upload")
async def upload_voice_sample(file: UploadFile = File(...), sample_name: str = Form(...)):
    """上传语音样本用于克隆"""
    try:
        # 验证文件类型
        if not file.content_type.startswith('audio/'):
            return APIResponse(
                success=False,
                error="只支持音频文件格式",
                metadata={"error_code": "INVALID_FILE_TYPE"}
            )

        # 验证文件大小（限制为10MB）
        file_size = 0
        content = await file.read()
        file_size = len(content)

        if file_size > 10 * 1024 * 1024:  # 10MB
            return APIResponse(
                success=False,
                error="文件大小不能超过10MB",
                metadata={"error_code": "FILE_TOO_LARGE"}
            )

        # 保存文件到临时目录
        from pathlib import Path
        import uuid

        temp_dir = Path(get_config('storage.output_dir', './output')) / 'voice_samples'
        temp_dir.mkdir(exist_ok=True)

        # 生成唯一文件名
        file_id = str(uuid.uuid4())
        file_extension = Path(file.filename).suffix
        temp_file_path = temp_dir / f"{file_id}{file_extension}"

        # 写入文件
        with open(temp_file_path, 'wb') as f:
            f.write(content)

        # 返回文件信息
        sample_info = {
            "sample_id": file_id,
            "sample_name": sample_name,
            "file_path": str(temp_file_path),
            "file_size": file_size,
            "content_type": file.content_type,
            "upload_time": datetime.now().isoformat()
        }

        return APIResponse(
            success=True,
            data=sample_info,
            metadata={"message": "语音样本上传成功"}
        )

    except Exception as e:
        logger.error(f"Failed to upload voice sample: {e}")
        return APIResponse(
            success=False,
            error="语音样本上传失败",
            metadata={"error_code": "UPLOAD_ERROR"}
        )

@router.post("/voice/clone/create")
@monitor_performance("api_voice_clone_create")
async def create_voice_clone(request: VoiceCloneRequest):
    """创建语音克隆"""
    try:
        # 准备克隆参数
        clone_params = {
            'name': request.name,
            'clone_type': request.clone_type,
            'sample_files': request.sample_files
        }

        # 调用ElevenLabs语音克隆工具
        ai_request = ServiceRequest(
            capability=ServiceCapability.SPEECH_SYNTHESIS,
            provider=ServiceProvider.ELEVENLABS,
            content="voice_clone",
            parameters=clone_params
        )

        response = await ai_service_manager.process_request(ai_request)

        if response.success:
            return APIResponse(
                success=True,
                data=response.data,
                metadata=response.metadata
            )
        else:
            return APIResponse(
                success=False,
                error=response.error or "语音克隆创建失败",
                metadata=response.metadata
            )

    except Exception as e:
        logger.error(f"Voice clone creation failed: {e}")
        return APIResponse(
            success=False,
            error="语音克隆服务暂时不可用",
            metadata={"error_code": "VOICE_CLONE_ERROR"}
        )

@router.get("/voice/clone/status/{clone_id}")
@monitor_performance("api_voice_clone_status")
async def get_voice_clone_status(clone_id: str):
    """获取语音克隆状态"""
    try:
        # 调用ElevenLabs获取克隆状态
        ai_request = ServiceRequest(
            capability=ServiceCapability.UTILITY,
            provider=ServiceProvider.ELEVENLABS,
            content="get_clone_status",
            parameters={"clone_id": clone_id}
        )

        response = await ai_service_manager.process_request(ai_request)

        if response.success:
            return APIResponse(
                success=True,
                data=response.data
            )
        else:
            return APIResponse(
                success=False,
                error="获取克隆状态失败",
                metadata={"error_code": "STATUS_CHECK_ERROR"}
            )

    except Exception as e:
        logger.error(f"Failed to get clone status: {e}")
        return APIResponse(
            success=False,
            error="状态查询失败",
            metadata={"error_code": "STATUS_ERROR"}
        )

@router.post("/speech/batch/create")
@monitor_performance("api_batch_speech_create")
async def create_batch_speech_job(request: BatchSpeechRequest):
    """创建批量语音合成任务"""
    try:
        from ..services.batch_speech_service import batch_speech_service

        # 验证输入
        if not request.texts:
            return APIResponse(
                success=False,
                error="文本列表不能为空",
                metadata={"error_code": "EMPTY_TEXT_LIST"}
            )

        if len(request.texts) > 100:  # 限制批量大小
            return APIResponse(
                success=False,
                error="批量任务最多支持100个文本项",
                metadata={"error_code": "BATCH_SIZE_LIMIT"}
            )

        # 创建批量任务
        job_id = await batch_speech_service.create_batch_job(
            name=request.name,
            texts=request.texts,
            voice_config=request.voice_config,
            auto_start=request.auto_start
        )

        return APIResponse(
            success=True,
            data={
                "job_id": job_id,
                "message": "批量语音合成任务创建成功"
            }
        )

    except Exception as e:
        logger.error(f"Failed to create batch speech job: {e}")
        return APIResponse(
            success=False,
            error="创建批量任务失败",
            metadata={"error_code": "BATCH_CREATE_ERROR"}
        )

@router.get("/speech/batch/status/{job_id}")
@monitor_performance("api_batch_speech_status")
async def get_batch_speech_status(job_id: str):
    """获取批量语音合成任务状态"""
    try:
        from ..services.batch_speech_service import batch_speech_service

        status = batch_speech_service.get_batch_job_status(job_id)

        if status is None:
            return APIResponse(
                success=False,
                error="任务不存在",
                metadata={"error_code": "JOB_NOT_FOUND"}
            )

        return APIResponse(
            success=True,
            data=status
        )

    except Exception as e:
        logger.error(f"Failed to get batch job status: {e}")
        return APIResponse(
            success=False,
            error="获取任务状态失败",
            metadata={"error_code": "STATUS_ERROR"}
        )

@router.post("/speech/batch/cancel/{job_id}")
@monitor_performance("api_batch_speech_cancel")
async def cancel_batch_speech_job(job_id: str):
    """取消批量语音合成任务"""
    try:
        from ..services.batch_speech_service import batch_speech_service

        success = await batch_speech_service.cancel_batch_job(job_id)

        if success:
            return APIResponse(
                success=True,
                data={"message": "任务已取消"}
            )
        else:
            return APIResponse(
                success=False,
                error="取消任务失败",
                metadata={"error_code": "CANCEL_FAILED"}
            )

    except Exception as e:
        logger.error(f"Failed to cancel batch job: {e}")
        return APIResponse(
            success=False,
            error="取消任务时发生错误",
            metadata={"error_code": "CANCEL_ERROR"}
        )

@router.get("/speech/batch/list")
@monitor_performance("api_batch_speech_list")
async def list_batch_speech_jobs():
    """列出所有批量语音合成任务"""
    try:
        from ..services.batch_speech_service import batch_speech_service

        jobs = batch_speech_service.list_batch_jobs()

        return APIResponse(
            success=True,
            data={"jobs": jobs}
        )

    except Exception as e:
        logger.error(f"Failed to list batch jobs: {e}")
        return APIResponse(
            success=False,
            error="获取任务列表失败",
            metadata={"error_code": "LIST_ERROR"}
        )

@router.post("/davinci/timeline/extract-text")
@monitor_performance("api_davinci_extract_text")
async def extract_timeline_text():
    """从DaVinci时间线提取文本"""
    try:
        from ..davinci.resolve_api import DaVinciResolveAPI

        # 创建DaVinci API实例
        davinci_api = DaVinciResolveAPI()

        # 连接到DaVinci Resolve
        if not await davinci_api.connect():
            return APIResponse(
                success=False,
                error="无法连接到DaVinci Resolve，请确保软件正在运行",
                metadata={"error_code": "DAVINCI_CONNECTION_ERROR"}
            )

        # 获取当前项目和时间线
        project = davinci_api.get_current_project()
        if not project:
            return APIResponse(
                success=False,
                error="没有打开的DaVinci Resolve项目",
                metadata={"error_code": "NO_PROJECT"}
            )

        timeline = davinci_api.get_current_timeline()
        if not timeline:
            return APIResponse(
                success=False,
                error="没有活动的时间线",
                metadata={"error_code": "NO_TIMELINE"}
            )

        # 提取文本内容
        text_data = await davinci_api.extract_timeline_text()

        return APIResponse(
            success=True,
            data={
                'combined_text': text_data.get('combined_text', ''),
                'segments': text_data.get('segments', []),
                'timeline_info': text_data.get('timeline_info', {}),
                'total_segments': len(text_data.get('segments', []))
            }
        )

    except Exception as e:
        logger.error(f"Failed to extract timeline text: {e}")
        return APIResponse(
            success=False,
            error="提取时间线文本失败",
            metadata={"error_code": "TEXT_EXTRACTION_ERROR"}
        )

@router.post("/davinci/audio/import")
@monitor_performance("api_davinci_import_audio")
async def import_audio_to_timeline(request: AudioImportRequest):
    """将音频导入到DaVinci时间线"""
    try:
        from ..davinci.resolve_api import DaVinciResolveAPI

        # 创建DaVinci API实例
        davinci_api = DaVinciResolveAPI()

        # 连接到DaVinci Resolve
        if not await davinci_api.connect():
            return APIResponse(
                success=False,
                error="无法连接到DaVinci Resolve",
                metadata={"error_code": "DAVINCI_CONNECTION_ERROR"}
            )

        # 验证音频文件
        audio_path = request.audio_url
        if audio_path.startswith('/api/audio/'):
            # 转换为本地文件路径
            filename = audio_path.split('/')[-1]
            audio_path = str(Path(get_config('storage.output_dir', './output')) / filename)

        if not Path(audio_path).exists():
            return APIResponse(
                success=False,
                error="音频文件不存在",
                metadata={"error_code": "AUDIO_FILE_NOT_FOUND"}
            )

        # 导入音频到时间线
        result = await davinci_api.import_audio_to_timeline(
            audio_path=audio_path,
            track_index=request.track_index,
            auto_sync=request.auto_sync
        )

        if result.get('success'):
            return APIResponse(
                success=True,
                data={
                    'message': '音频已成功导入到DaVinci时间线',
                    'audio_path': audio_path,
                    'track_index': request.track_index,
                    'clip_info': result.get('clip_info', {})
                }
            )
        else:
            return APIResponse(
                success=False,
                error=result.get('error', '音频导入失败'),
                metadata={"error_code": "AUDIO_IMPORT_ERROR"}
            )

    except Exception as e:
        logger.error(f"Failed to import audio to timeline: {e}")
        return APIResponse(
            success=False,
            error="音频导入时发生错误",
            metadata={"error_code": "IMPORT_ERROR"}
        )

@router.post("/davinci/subtitles/generate-voiceover")
@monitor_performance("api_davinci_subtitle_voiceover")
async def generate_subtitle_voiceover(request: VoiceoverRequest):
    """基于字幕生成配音"""
    try:
        from ..davinci.resolve_api import DaVinciResolveAPI
        from ..services.batch_speech_service import batch_speech_service

        # 创建DaVinci API实例
        davinci_api = DaVinciResolveAPI()

        # 连接到DaVinci Resolve
        if not await davinci_api.connect():
            return APIResponse(
                success=False,
                error="无法连接到DaVinci Resolve",
                metadata={"error_code": "DAVINCI_CONNECTION_ERROR"}
            )

        # 获取字幕数据
        subtitles = await davinci_api.get_timeline_subtitles()

        if not subtitles:
            return APIResponse(
                success=False,
                error="时间线中没有找到字幕",
                metadata={"error_code": "NO_SUBTITLES"}
            )

        # 准备批量语音合成
        texts = [subtitle['text'] for subtitle in subtitles]
        voice_config = {
            'voice_id': request.voice_id,
            'model_id': request.model_id,
            'output_format': request.output_format,
            'provider': 'elevenlabs'
        }

        # 创建批量任务
        job_id = await batch_speech_service.create_batch_job(
            name=f"字幕配音_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            texts=texts,
            voice_config=voice_config,
            auto_start=True
        )

        return APIResponse(
            success=True,
            data={
                'job_id': job_id,
                'subtitle_count': len(subtitles),
                'message': f'已开始为{len(subtitles)}个字幕片段生成配音'
            }
        )

    except Exception as e:
        logger.error(f"Failed to generate subtitle voiceover: {e}")
        return APIResponse(
            success=False,
            error="生成字幕配音失败",
            metadata={"error_code": "VOICEOVER_ERROR"}
        )

# ==================== 文本生成和对话API ====================

class TextGenerationRequest(BaseModel):
    """文本生成请求"""
    prompt: str = Field(..., description="输入提示词")
    provider: Optional[str] = Field(None, description="指定服务提供商")
    parameters: Optional[Dict[str, Any]] = Field(default_factory=dict, description="生成参数")

class ChatRequest(BaseModel):
    """对话请求"""
    message: str = Field(..., description="用户消息")
    provider: Optional[str] = Field(None, description="指定服务提供商")
    parameters: Optional[Dict[str, Any]] = Field(default_factory=dict, description="对话参数")

@router.post("/text/generate")
@monitor_performance("api_text_generate")
async def generate_text(request: TextGenerationRequest):
    """文本生成API - 已弃用，重定向到统一端点"""
    logger.warning("⚠️ 使用已弃用的端点 /api/text/generate，请迁移到 /api/services/unified")

    try:
        # 转换为统一请求格式
        unified_request = UnifiedServiceRequest(
            service_type="text_generation",
            provider=request.provider,
            content=request.prompt,
            parameters=request.parameters or {},
            metadata={
                "source": "legacy_api",
                "original_endpoint": "/api/text/generate",
                "migrated_to_unified": True
            }
        )

        # 调用统一端点
        return await unified_service_endpoint(unified_request)

    except Exception as e:
        logger.error(f"Text generation error: {e}")
        return APIResponse(
            success=False,
            error=f"文本生成失败: {str(e)}",
            metadata={"error_code": "API_SERVER_ERROR"}
        )

@router.post("/chat")
@monitor_performance("api_chat")
async def chat(request: ChatRequest):
    """对话API"""
    try:
        # 验证服务提供商
        provider = request.provider

        # 创建AI请求
        ai_request = ServiceRequest(
            capability=ServiceCapability.CHAT,
            provider=ServiceProvider(provider.upper()),
            content=request.message,
            parameters=request.parameters
        )

        # 处理请求
        response = await ai_service_manager.process_request(ai_request)

        if response.success:
            return APIResponse(
                success=True,
                data={
                    'message': response.data,
                    'provider': response.provider,
                    'processing_time': response.processing_time
                },
                metadata=response.metadata
            )
        else:
            return APIResponse(
                success=False,
                error=response.error or "对话失败",
                metadata={"error_code": "CHAT_FAILED"}
            )

    except Exception as e:
        logger.error(f"Chat error: {e}")
        return APIResponse(
            success=False,
            error=f"对话失败: {str(e)}",
            metadata={"error_code": "API_SERVER_ERROR"}
        )

@router.post("/text/analyze")
@monitor_performance("api_text_analyze")
async def analyze_text(request: TextGenerationRequest):
    """文本分析API"""
    try:
        # 验证服务提供商
        provider = request.provider

        # 创建AI请求
        ai_request = ServiceRequest(
            capability=ServiceCapability.TEXT_ANALYSIS,
            provider=ServiceProvider(provider.upper()),
            content=request.prompt,
            parameters=request.parameters
        )

        # 处理请求
        response = await ai_service_manager.process_request(ai_request)

        if response.success:
            return APIResponse(
                success=True,
                data={
                    'analysis': response.data,
                    'provider': response.provider,
                    'processing_time': response.processing_time
                },
                metadata=response.metadata
            )
        else:
            return APIResponse(
                success=False,
                error=response.error or "文本分析失败",
                metadata={"error_code": "ANALYSIS_FAILED"}
            )

    except Exception as e:
        logger.error(f"Text analysis error: {e}")
        return APIResponse(
            success=False,
            error=f"文本分析失败: {str(e)}",
            metadata={"error_code": "API_SERVER_ERROR"}
        )

class VideoStatusRequest(BaseModel):
    """视频状态查询请求"""
    provider: str = Field(..., description="服务提供商")
    task_id: str = Field(..., description="任务ID")

@router.post("/video/status")
@monitor_performance("api_video_status")
async def get_video_status(request: VideoStatusRequest):
    """查询视频生成状态"""
    try:
        # 创建AI请求
        ai_request = ServiceRequest(
            capability=ServiceCapability.UTILITY,
            provider=ServiceProvider(request.provider.upper()),
            content="get_video_status",
            parameters={
                'tool_name': 'get_video_status',
                'tool_params': {'task_id': request.task_id}
            }
        )

        # 处理请求
        response = await ai_service_manager.process_request(ai_request)

        if response.success:
            return APIResponse(
                success=True,
                data=response.data,
                metadata=response.metadata
            )
        else:
            return APIResponse(
                success=False,
                error=response.error or "状态查询失败",
                metadata={"error_code": "STATUS_QUERY_FAILED"}
            )

    except Exception as e:
        logger.error(f"Video status query error: {e}")
        return APIResponse(
            success=False,
            error=f"状态查询失败: {str(e)}",
            metadata={"error_code": "API_SERVER_ERROR"}
        )

@router.get("/audio/{file_id}")
@monitor_performance("api_audio_file")
async def get_audio_file(file_id: str):
    """获取音频文件"""
    try:
        from fastapi.responses import FileResponse
        from web.api.media_library import media_manager

        # 从媒体库获取音频文件信息
        media_item = media_manager.get_media_item(file_id)

        if not media_item or media_item.type != 'audio':
            return APIResponse(
                success=False,
                error="音频文件不存在",
                metadata={"error_code": "AUDIO_NOT_FOUND"}
            )

        file_path = Path(media_item.file_path)
        if not file_path.exists():
            return APIResponse(
                success=False,
                error="音频文件已损坏或丢失",
                metadata={"error_code": "FILE_MISSING"}
            )

        # 返回文件响应
        return FileResponse(
            path=str(file_path),
            media_type="audio/mpeg",
            filename=media_item.name
        )

    except Exception as e:
        logger.error(f"Failed to get audio file {file_id}: {e}")
        return APIResponse(
            success=False,
            error="获取音频文件失败",
            metadata={"error_code": "AUDIO_ACCESS_ERROR"}
        )

# DaVinci Resolve路由在main.py中直接处理，避免重复导入

# 提示词优化API - 已弃用，重定向到统一端点
@router.post("/ai/enhance-prompt")
@handle_errors(APIError)
@monitor_performance("api_enhance_prompt")
async def enhance_prompt(request: dict):
    """专门的提示词优化接口 - 已弃用，重定向到统一端点"""
    logger.warning("⚠️ 使用已弃用的端点 /api/ai/enhance-prompt，请迁移到 /api/services/unified")

    try:
        # 验证请求参数
        if 'prompt' not in request:
            return APIResponse(
                success=False,
                error="Missing 'prompt' parameter",
                metadata={"error_code": "INVALID_PARAMETER"}
            )

        # 转换为统一请求格式
        unified_request = UnifiedServiceRequest(
            service_type="text_analysis",
            capability="prompt_enhancement",
            provider=request.get('provider'),
            content=request['prompt'],
            parameters={
                'task': 'prompt_enhancement',
                'target_type': request.get('target_type', 'general'),
                'enhancement_style': request.get('enhancement_style', 'detailed')
            },
            metadata={
                "source": "legacy_api",
                "original_endpoint": "/api/ai/enhance-prompt",
                "migrated_to_unified": True
            }
        )

        # 调用统一端点
        return await unified_service_endpoint(unified_request)

    except Exception as e:
        logger.error(f"Prompt enhancement failed: {e}")
        return APIResponse(
            success=False,
            error="Prompt enhancement failed",
            metadata={"error_code": "INTERNAL_SERVER_ERROR", "details": str(e)}
        )

# 异步任务管理已移除，使用简化架构的同步处理

# 语音获取辅助函数
def parse_voice_library_text(text_content: str) -> List[Dict[str, Any]]:
    """
    解析search_voice_library返回的文本格式

    示例格式:
    Shared Voices:

    Name: Alice
    ID: voice_id_123
    Category: premade
    Gender: female
    Age: young
    Description: A warm, friendly voice
    Languages: English (US)

    Name: Bob
    ID: voice_id_456
    ...
    """
    voices = []

    if not text_content.startswith("Shared Voices:"):
        return voices

    # 移除标题并分割语音条目
    content = text_content.replace("Shared Voices:", "").strip()
    if not content:
        return voices

    # 按双换行符分割语音条目
    voice_blocks = content.split("\n\n")

    for block in voice_blocks:
        if not block.strip():
            continue

        voice_data = {}
        lines = block.strip().split("\n")

        for line in lines:
            if ":" in line:
                key, value = line.split(":", 1)
                key = key.strip()
                value = value.strip()

                if key == "Name":
                    voice_data["name"] = value
                elif key == "ID":
                    voice_data["voice_id"] = value
                elif key == "Category":
                    voice_data["category"] = value
                elif key == "Gender":
                    voice_data["gender"] = value
                elif key == "Age":
                    voice_data["age"] = value
                elif key == "Description":
                    voice_data["description"] = value
                elif key == "Languages":
                    voice_data["languages"] = value
                elif key == "Accent":
                    voice_data["accent"] = value
                elif key == "Use Case":
                    voice_data["use_case"] = value
                elif key == "Preview URL":
                    voice_data["preview_url"] = value

        # 只添加有基本信息的语音
        if voice_data.get("name") and voice_data.get("voice_id"):
            voices.append(voice_data)

    return voices

async def get_elevenlabs_voices_from_mcp() -> List[Dict]:
    """从ElevenLabs MCP服务器获取语音列表 - 优化版，专注中英文语音"""
    voices = []
    try:
        # 直接使用FastMCP客户端调用search_voices工具
        from ..services.fastmcp_server import fastmcp_manager

        if not fastmcp_manager._initialized:
            logger.warning("FastMCP manager not initialized")
            return voices

        if 'elevenlabs' not in fastmcp_manager.server_info:
            logger.warning("ElevenLabs MCP server not available")
            return voices

        server_info = fastmcp_manager.server_info['elevenlabs']
        if not server_info.is_connected:
            logger.warning("ElevenLabs MCP server not connected")
            return voices

        # 优先调用search_voices工具获取用户自己的语音库（确保有权限使用）
        try:
            result = await fastmcp_manager.call_tool('elevenlabs', 'search_voices', {
                'search': '',  # 空查询获取所有用户语音
                'sort': 'name',
                'sort_direction': 'asc'
            })
            logger.info("✅ Successfully called search_voices tool (user voices)")
        except Exception as e:
            logger.warning(f"⚠️ search_voices tool failed, trying search_voice_library: {e}")
            # 如果search_voices失败，尝试search_voice_library（但这些语音可能无权使用）
            try:
                result = await fastmcp_manager.call_tool('elevenlabs', 'search_voice_library', {
                    'page': 0,
                    'page_size': 50,  # 减少数量，因为大部分可能无权使用
                    'search': None  # 不过滤，获取所有语音
                })
                logger.info("✅ Successfully called search_voice_library tool as fallback")
                logger.warning("⚠️ Using public voice library - some voices may not be accessible")
            except Exception as e2:
                logger.error(f"❌ Both search_voices and search_voice_library failed: {e2}")
                return voices

        logger.info(f"ElevenLabs MCP result type: {type(result)}")
        logger.info(f"ElevenLabs MCP result content: {repr(result)}")

        if result:
            import json
            try:
                # 调试：记录原始响应数据
                logger.info(f"ElevenLabs MCP raw result type: {type(result)}")
                logger.info(f"ElevenLabs MCP raw result data: {result}")

                # 详细调试：检查result的结构
                if hasattr(result, '__dict__'):
                    logger.info(f"ElevenLabs MCP result attributes: {result.__dict__}")
                if hasattr(result, 'content'):
                    logger.info(f"ElevenLabs MCP content type: {type(result.content)}")
                    logger.info(f"ElevenLabs MCP content: {result.content}")

                # 解析FastMCP CallToolResult对象
                if hasattr(result, 'content'):
                    # FastMCP 2.0 返回的是CallToolResult对象
                    content = result.content
                    if isinstance(content, list) and len(content) > 0:
                        # 获取第一个内容项
                        first_content = content[0]
                        if hasattr(first_content, 'text'):
                            # 检查是否是search_voice_library返回的文本格式
                            text_content = first_content.text
                            if text_content.startswith("Shared Voices:"):
                                # 解析search_voice_library返回的文本格式
                                voices_data = parse_voice_library_text(text_content)
                                logger.info(f"✅ Parsed {len(voices_data)} voices from voice library text")
                            else:
                                # 尝试解析为JSON
                                try:
                                    voices_data = json.loads(text_content)
                                except json.JSONDecodeError:
                                    logger.warning(f"Failed to parse as JSON: {text_content[:200]}...")
                                    voices_data = []
                        else:
                            voices_data = first_content
                    else:
                        voices_data = content
                elif isinstance(result, str):
                    if result.strip():  # 检查是否为空字符串
                        if result.startswith("Shared Voices:"):
                            # 解析search_voice_library返回的文本格式
                            voices_data = parse_voice_library_text(result)
                        else:
                            voices_data = json.loads(result)
                    else:
                        logger.warning("ElevenLabs MCP returned empty string")
                        return voices
                else:
                    voices_data = result

                # 处理不同的数据格式
                if isinstance(voices_data, list):
                    for voice in voices_data:
                        voices.append({
                            "id": voice.get('voice_id', voice.get('id', '')),
                            "name": voice.get('name', ''),
                            "language": voice.get('language', 'en'),
                            "provider": "elevenlabs",
                            "category": voice.get('category', 'premade'),
                            "description": voice.get('description', voice.get('name', '')),
                            "preview_url": voice.get('preview_url', ''),
                            "settings": voice.get('settings', {})
                        })
                elif isinstance(voices_data, dict) and 'voices' in voices_data:
                    for voice in voices_data['voices']:
                        voices.append({
                            "id": voice.get('voice_id', voice.get('id', '')),
                            "name": voice.get('name', ''),
                            "language": voice.get('language', 'en'),
                            "provider": "elevenlabs",
                            "category": voice.get('category', 'premade'),
                            "description": voice.get('description', voice.get('name', '')),
                            "preview_url": voice.get('preview_url', ''),
                            "settings": voice.get('settings', {})
                        })
                else:
                    logger.warning(f"Unexpected ElevenLabs data format: {type(voices_data)}")
                    logger.info(f"ElevenLabs data content: {voices_data}")

                    # 尝试处理单个语音对象
                    if isinstance(voices_data, dict):
                        if 'voice_id' in voices_data or 'id' in voices_data:
                            # 单个语音对象
                            voices.append({
                                "id": voices_data.get('voice_id', voices_data.get('id', '')),
                                "name": voices_data.get('name', ''),
                                "language": voices_data.get('language', 'en'),
                                "provider": "elevenlabs",
                                "category": voices_data.get('category', 'premade'),
                                "description": voices_data.get('description', voices_data.get('name', '')),
                                "preview_url": voices_data.get('preview_url', ''),
                                "settings": voices_data.get('settings', {}),
                                "labels": voices_data.get('labels', {})
                            })
                        else:
                            # 检查是否有其他可能的键
                            logger.info(f"Dict keys: {list(voices_data.keys()) if isinstance(voices_data, dict) else 'Not a dict'}")
                            # 尝试查找可能包含语音数据的键
                            for key in ['data', 'result', 'voices', 'items']:
                                if key in voices_data and isinstance(voices_data[key], list):
                                    logger.info(f"Found voices data in key '{key}'")
                                    for voice in voices_data[key]:
                                        voices.append({
                                            "id": voice.get('voice_id', voice.get('id', '')),
                                            "name": voice.get('name', ''),
                                            "language": voice.get('language', 'en'),
                                            "provider": "elevenlabs",
                                            "category": voice.get('category', 'premade'),
                                            "description": voice.get('description', voice.get('name', '')),
                                            "preview_url": voice.get('preview_url', ''),
                                            "settings": voice.get('settings', {}),
                                            "labels": voice.get('labels', {})
                                        })
                                    break

                logger.info(f"✅ Retrieved {len(voices)} ElevenLabs voices via MCP")

            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse ElevenLabs MCP result as JSON: {e}")
                logger.debug(f"Raw result data: {result}")
            except Exception as e:
                logger.error(f"Error processing ElevenLabs MCP result: {e}")
        else:
            logger.warning("ElevenLabs MCP returned no result")

    except Exception as e:
        logger.error(f"Error getting ElevenLabs voices from MCP: {e}")

    return voices

def parse_mcp_voices_response(result, source_type: str) -> List[Dict]:
    """解析MCP search_voices工具的响应"""
    voices = []
    try:
        logger.debug(f"Parsing MCP voices response, type: {type(result)}")

        # 新的FastMCP格式：检查 result.data.result
        if hasattr(result, 'data') and hasattr(result.data, 'result'):
            logger.debug(f"Found result.data.result with {len(result.data.result)} items")
            for voice_obj in result.data.result:
                # voice_obj 是 McpVoice 对象
                voice = {
                    'id': getattr(voice_obj, 'id', ''),
                    'name': getattr(voice_obj, 'name', ''),
                    'category': getattr(voice_obj, 'category', 'user'),
                    'provider': 'elevenlabs',
                    'source': source_type,
                    'availability': 'guaranteed' if source_type == 'user' else 'needs_validation',
                    'priority': 2 if source_type == 'user' else 1,  # 用户语音优先级更高
                    'language': 'zh' if any(char in getattr(voice_obj, 'name', '') for char in '中文汉字') else 'en',
                    'description': f"{getattr(voice_obj, 'name', '')} - {getattr(voice_obj, 'category', 'user')} voice"
                }
                voices.append(voice)

        # 检查FastMCP 2.0的content格式
        elif hasattr(result, 'content') and isinstance(result.content, list):
            logger.debug(f"Found result.content with {len(result.content)} items")
            for item in result.content:
                if hasattr(item, 'text'):
                    # 尝试解析JSON格式的语音数据
                    try:
                        import json
                        voices_data = json.loads(item.text)
                        logger.debug(f"Parsed JSON data: {type(voices_data)}")

                        if isinstance(voices_data, dict):
                            # 单个语音对象
                            voice = {
                                'id': voices_data.get('voice_id', voices_data.get('id', '')),
                                'name': voices_data.get('name', ''),
                                'category': voices_data.get('category', 'user'),
                                'provider': 'elevenlabs',
                                'source': source_type,
                                'availability': 'guaranteed' if source_type == 'user' else 'needs_validation',
                                'priority': 2 if source_type == 'user' else 1,
                                'language': 'zh' if any(char in voices_data.get('name', '') for char in '中文汉字') else 'en',
                                'description': voices_data.get('description', f"{voices_data.get('name', '')} - {voices_data.get('category', 'user')} voice")
                            }
                            voices.append(voice)
                        elif isinstance(voices_data, list):
                            # 语音列表
                            for voice_data in voices_data:
                                voice = {
                                    'id': voice_data.get('voice_id', voice_data.get('id', '')),
                                    'name': voice_data.get('name', ''),
                                    'category': voice_data.get('category', 'user'),
                                    'provider': 'elevenlabs',
                                    'source': source_type,
                                    'availability': 'guaranteed' if source_type == 'user' else 'needs_validation',
                                    'priority': 2 if source_type == 'user' else 1,
                                    'language': 'zh' if any(char in voice_data.get('name', '') for char in '中文汉字') else 'en',
                                    'description': voice_data.get('description', f"{voice_data.get('name', '')} - {voice_data.get('category', 'user')} voice")
                                }
                                voices.append(voice)
                    except json.JSONDecodeError as e:
                        logger.warning(f"Failed to parse JSON from MCP response: {e}")
                        logger.debug(f"Raw text content: {item.text[:200]}...")
                elif hasattr(item, 'data'):
                    # 直接的数据对象
                    logger.debug(f"Found item.data: {type(item.data)}")
                    voices_data = item.data
                    if isinstance(voices_data, dict):
                        voice = {
                            'id': voices_data.get('voice_id', voices_data.get('id', '')),
                            'name': voices_data.get('name', ''),
                            'category': voices_data.get('category', 'user'),
                            'provider': 'elevenlabs',
                            'source': source_type,
                            'availability': 'guaranteed' if source_type == 'user' else 'needs_validation',
                            'priority': 2 if source_type == 'user' else 1,
                            'language': 'zh' if any(char in voices_data.get('name', '') for char in '中文汉字') else 'en',
                            'description': voices_data.get('description', f"{voices_data.get('name', '')} - {voices_data.get('category', 'user')} voice")
                        }
                        voices.append(voice)

        # 检查直接的字典格式（可能是新的ElevenLabs MCP格式）
        elif isinstance(result, dict):
            logger.debug(f"Found dict result with keys: {list(result.keys())}")
            # 检查是否有voices字段
            if 'voices' in result:
                voices_data = result['voices']
                logger.debug(f"Found voices field with {len(voices_data)} items")
                for voice_data in voices_data:
                    voice = {
                        'id': voice_data.get('voice_id', voice_data.get('id', '')),
                        'name': voice_data.get('name', ''),
                        'category': voice_data.get('category', 'user'),
                        'provider': 'elevenlabs',
                        'source': source_type,
                        'availability': 'guaranteed' if source_type == 'user' else 'needs_validation',
                        'priority': 2 if source_type == 'user' else 1,
                        'language': 'zh' if any(char in voice_data.get('name', '') for char in '中文汉字') else 'en',
                        'description': voice_data.get('description', f"{voice_data.get('name', '')} - {voice_data.get('category', 'user')} voice")
                    }
                    voices.append(voice)
            else:
                # 单个语音对象
                voice = {
                    'id': result.get('voice_id', result.get('id', '')),
                    'name': result.get('name', ''),
                    'category': result.get('category', 'user'),
                    'provider': 'elevenlabs',
                    'source': source_type,
                    'availability': 'guaranteed' if source_type == 'user' else 'needs_validation',
                    'priority': 2 if source_type == 'user' else 1,
                    'language': 'zh' if any(char in result.get('name', '') for char in '中文汉字') else 'en',
                    'description': result.get('description', f"{result.get('name', '')} - {result.get('category', 'user')} voice")
                }
                voices.append(voice)

        # 备用：直接列表格式
        elif isinstance(result, list):
            logger.debug(f"Found list result with {len(result)} items")
            for voice_data in result:
                voice = {
                    'id': voice_data.get('id', voice_data.get('voice_id', '')),
                    'name': voice_data.get('name', ''),
                    'category': voice_data.get('category', 'user'),
                    'provider': 'elevenlabs',
                    'source': source_type,
                    'availability': 'guaranteed' if source_type == 'user' else 'needs_validation',
                    'priority': 2 if source_type == 'user' else 1,
                    'language': 'zh' if any(char in voice_data.get('name', '') for char in '中文汉字') else 'en',
                    'description': voice_data.get('description', f"{voice_data.get('name', '')} - {voice_data.get('category', 'user')} voice")
                }
                voices.append(voice)

        else:
            logger.warning(f"Unexpected MCP result format: {type(result)}")
            logger.debug(f"Result content: {str(result)[:200]}...")

        logger.info(f"✅ Parsed {len(voices)} voices from MCP response (source: {source_type})")

    except Exception as e:
        logger.warning(f"Failed to parse MCP voices response: {e}")
        import traceback
        logger.warning(f"Traceback: {traceback.format_exc()}")

    return voices

def parse_mcp_voice_library_response(result, search_type: str) -> List[Dict]:
    """解析MCP search_voice_library工具的响应"""
    voices = []
    try:
        if hasattr(result, 'content') and isinstance(result.content, list):
            # FastMCP 2.0 格式
            for item in result.content:
                if hasattr(item, 'text'):
                    text_content = item.text
                    # 解析文本内容中的语音信息
                    voices_data = parse_voice_library_text(text_content)
                    for voice_data in voices_data:
                        # 判断语言
                        language = detect_voice_language(voice_data, search_type)

                        voice = {
                            'id': voice_data.get('id', voice_data.get('voice_id', '')),
                            'name': voice_data.get('name', ''),
                            'category': voice_data.get('category', 'shared'),
                            'provider': 'elevenlabs',
                            'source': 'public',
                            'availability': 'needs_validation',
                            'priority': 2,
                            'language': language,
                            'description': voice_data.get('description', ''),
                            'gender': voice_data.get('gender', ''),
                            'age': voice_data.get('age', ''),
                            'accent': voice_data.get('accent', ''),
                            'search_type': search_type
                        }
                        voices.append(voice)
    except Exception as e:
        logger.warning(f"Failed to parse MCP voice library response: {e}")

    return voices

def detect_voice_language(voice_data: Dict, search_type: str) -> str:
    """根据语音数据和搜索类型检测语言"""
    # 优先使用明确的语言信息
    if 'language' in voice_data:
        return voice_data['language']

    # 根据搜索类型推断
    if search_type.startswith('chinese'):
        return 'zh'
    elif search_type.startswith('english'):
        return 'en'

    # 根据描述、名称等推断
    text_to_check = ' '.join([
        voice_data.get('name', ''),
        voice_data.get('description', ''),
        voice_data.get('accent', ''),
    ]).lower()

    chinese_keywords = ['chinese', 'mandarin', '中文', 'zh', 'china', 'beijing', 'shanghai']
    english_keywords = ['english', 'american', 'british', 'australian', 'canadian', 'en', 'us', 'uk']

    if any(keyword in text_to_check for keyword in chinese_keywords):
        return 'zh'
    elif any(keyword in text_to_check for keyword in english_keywords):
        return 'en'

    return 'unknown'

def deduplicate_voices(voices: List[Dict]) -> List[Dict]:
    """去重语音列表，基于voice_id"""
    seen_ids = set()
    unique_voices = []

    for voice in voices:
        voice_id = voice.get('id', '')
        if voice_id and voice_id not in seen_ids:
            seen_ids.add(voice_id)
            unique_voices.append(voice)

    return unique_voices

async def get_optimized_chinese_english_voices() -> List[Dict]:
    """获取优化的中英文语音列表 - 专为免费用户设计"""
    voices = []
    try:
        from ..services.fastmcp_server import fastmcp_manager

        if not fastmcp_manager._initialized:
            logger.warning("FastMCP manager not initialized")
            return voices

        if 'elevenlabs' not in fastmcp_manager.server_info:
            logger.warning("ElevenLabs MCP server not available")
            return voices

        # 1. 获取用户自己的语音（最高优先级，100%可用）
        user_voices = []
        try:
            result = await fastmcp_manager.call_tool('elevenlabs', 'search_voices', {
                'search': '',
                'sort': 'name',
                'sort_direction': 'asc'
            })
            user_voices = parse_mcp_voices_response(result, 'user')
            logger.info(f"✅ Got {len(user_voices)} user voices")
        except Exception as e:
            logger.warning(f"⚠️ Failed to get user voices: {e}")

        # 2. 搜索高质量中文语音
        chinese_voices = []
        chinese_search_terms = ['chinese', 'mandarin', '中文']

        for term in chinese_search_terms:
            try:
                result = await fastmcp_manager.call_tool('elevenlabs', 'search_voice_library', {
                    'page': 0,
                    'page_size': 8,
                    'search': term
                })
                term_voices = parse_mcp_voice_library_response(result, f'chinese_{term}')
                chinese_voices.extend(term_voices)
                logger.info(f"✅ Found {len(term_voices)} voices for '{term}'")
            except Exception as e:
                logger.warning(f"⚠️ Failed to search Chinese voices with '{term}': {e}")

        # 3. 搜索高质量英文语音
        english_voices = []
        english_search_terms = ['english', 'american', 'british']

        for term in english_search_terms:
            try:
                result = await fastmcp_manager.call_tool('elevenlabs', 'search_voice_library', {
                    'page': 0,
                    'page_size': 8,
                    'search': term
                })
                term_voices = parse_mcp_voice_library_response(result, f'english_{term}')
                english_voices.extend(term_voices)
                logger.info(f"✅ Found {len(term_voices)} voices for '{term}'")
            except Exception as e:
                logger.warning(f"⚠️ Failed to search English voices with '{term}': {e}")

        # 4. 去重和优先级排序
        chinese_voices = deduplicate_voices(chinese_voices)
        english_voices = deduplicate_voices(english_voices)

        # 5. 组合结果：用户语音 > 中文语音 > 英文语音
        all_voices = []
        all_voices.extend(user_voices)  # 用户语音优先
        all_voices.extend(chinese_voices[:12])  # 限制中文语音数量
        all_voices.extend(english_voices[:15])  # 限制英文语音数量

        logger.info(f"🎯 Optimized voice collection: {len(user_voices)} user + {len(chinese_voices[:12])} Chinese + {len(english_voices[:15])} English")

        return all_voices

    except Exception as e:
        logger.error(f"❌ Failed to get optimized Chinese/English voices: {e}")
        return voices

def _parse_minimax_voices_text(text_content: str) -> List[Dict]:
    """解析MiniMax MCP返回的语音列表文本"""
    voices = []
    try:
        # MiniMax返回格式: "Success. System Voices: ['Name: 青涩青年音色, ID: male-qn-qingse', ...]"
        if "System Voices:" in text_content:
            # 提取语音列表部分
            voices_part = text_content.split("System Voices:")[1]
            if voices_part.strip().startswith('[') and ']' in voices_part:
                # 提取方括号内的内容
                start_idx = voices_part.find('[')
                end_idx = voices_part.find(']', start_idx)
                if start_idx != -1 and end_idx != -1:
                    voices_str = voices_part[start_idx+1:end_idx]

                    # 解析每个语音条目: 'Name: 青涩青年音色, ID: male-qn-qingse'
                    import re
                    voice_pattern = r"'Name:\s*([^,]+),\s*ID:\s*([^']+)'"
                    matches = re.findall(voice_pattern, voices_str)

                    for name, voice_id in matches:
                        name = name.strip()
                        voice_id = voice_id.strip()

                        # 确定语言
                        language = "zh"  # 默认中文
                        if any(char in name for char in "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"):
                            if not any('\u4e00' <= char <= '\u9fff' for char in name):
                                language = "en"  # 纯英文名称

                        # 确定类别
                        category = "standard"
                        if "beta" in voice_id.lower() or "jingpin" in voice_id.lower():
                            category = "premium"
                        elif "童" in name or "boy" in name.lower() or "girl" in name.lower():
                            category = "child"
                        elif "主持" in name or "host" in name.lower() or "anchor" in name.lower():
                            category = "professional"

                        voices.append({
                            "id": voice_id,
                            "name": name,
                            "language": language,
                            "provider": "minimax",
                            "category": category,
                            "description": f"{name} ({voice_id})"
                        })

        logger.info(f"✅ Parsed {len(voices)} voices from MiniMax text response")

    except Exception as e:
        logger.error(f"Error parsing MiniMax voices text: {e}")
        logger.debug(f"Text content: {text_content[:500]}...")

    return voices

async def get_minimax_voices_from_mcp() -> List[Dict]:
    """从Minimax MCP服务器获取语音列表"""
    voices = []
    try:
        # 直接使用FastMCP客户端调用list_voices工具
        from ..services.fastmcp_server import fastmcp_manager

        if not fastmcp_manager._initialized:
            logger.warning("FastMCP manager not initialized")
            return voices

        if 'minimax' not in fastmcp_manager.server_info:
            logger.warning("Minimax MCP server not available")
            return voices

        server_info = fastmcp_manager.server_info['minimax']
        if not server_info.is_connected:
            logger.warning("Minimax MCP server not connected")
            return voices

        # 调用list_voices工具
        result = await fastmcp_manager.call_tool('minimax', 'list_voices', {})

        logger.info(f"Minimax MCP result type: {type(result)}")
        logger.info(f"Minimax MCP result content: {repr(result)}")

        if result:
            import json
            try:
                # 调试：记录原始响应数据
                logger.info(f"Minimax MCP raw result type: {type(result)}")
                logger.info(f"Minimax MCP raw result data: {result}")

                # 解析FastMCP CallToolResult对象
                if hasattr(result, 'content'):
                    # FastMCP 2.0 返回的是CallToolResult对象
                    content = result.content
                    if isinstance(content, list) and len(content) > 0:
                        # 获取第一个内容项
                        first_content = content[0]
                        if hasattr(first_content, 'text'):
                            # MiniMax返回的是描述性字符串，需要解析
                            text_content = first_content.text
                            logger.info(f"MiniMax raw text content: {text_content[:200]}...")

                            # 解析MiniMax特有的语音列表格式
                            voices_data = _parse_minimax_voices_text(text_content)
                        else:
                            voices_data = first_content
                    else:
                        voices_data = content
                elif isinstance(result, str):
                    if result.strip():  # 检查是否为空字符串
                        # 尝试解析为JSON，如果失败则作为文本解析
                        try:
                            voices_data = json.loads(result)
                        except json.JSONDecodeError:
                            voices_data = _parse_minimax_voices_text(result)
                    else:
                        logger.warning("Minimax MCP returned empty string")
                        return voices
                else:
                    voices_data = result

                # 处理Minimax语音数据格式
                if isinstance(voices_data, list):
                    for voice in voices_data:
                        voices.append({
                            "id": voice.get('id', voice.get('voice_id', '')),
                            "name": voice.get('name', voice.get('id', '')),
                            "language": voice.get('language', 'zh'),
                            "provider": "minimax",
                            "category": voice.get('category', 'standard'),
                            "description": voice.get('description', voice.get('name', ''))
                        })
                elif isinstance(voices_data, dict) and 'voices' in voices_data:
                    for voice in voices_data['voices']:
                        voices.append({
                            "id": voice.get('id', voice.get('voice_id', '')),
                            "name": voice.get('name', voice.get('id', '')),
                            "language": voice.get('language', 'zh'),
                            "provider": "minimax",
                            "category": voice.get('category', 'standard'),
                            "description": voice.get('description', voice.get('name', ''))
                        })
                else:
                    logger.warning(f"Unexpected Minimax data format: {type(voices_data)}")

                logger.info(f"✅ Retrieved {len(voices)} Minimax voices via MCP")

            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse Minimax MCP result as JSON: {e}")
                logger.debug(f"Raw result data: {result}")
            except Exception as e:
                logger.error(f"Error processing Minimax MCP result: {e}")
        else:
            logger.warning("Minimax MCP returned no result")

    except Exception as e:
        logger.error(f"Error getting Minimax voices from MCP: {e}")

    return voices

# 语音搜索辅助函数

def enhance_voice_description(description: str) -> str:
    """增强语音描述以满足ElevenLabs API要求（至少20个字符）"""
    # 如果描述已经足够长，直接返回
    if len(description) >= 20:
        return description

    # 中文描述增强模板
    chinese_patterns = {
        '中文': 'A Chinese speaker with clear pronunciation and natural tone',
        '女生': 'A young female voice with warm and friendly characteristics',
        '男生': 'A young male voice with confident and clear characteristics',
        '温柔': 'A gentle and soft voice with caring tone',
        '活泼': 'A lively and energetic voice with cheerful characteristics',
        '成熟': 'A mature and sophisticated voice with professional tone'
    }

    # 英文描述增强模板
    english_patterns = {
        'woman': 'A professional female voice with warm and engaging characteristics',
        'man': 'A professional male voice with confident and clear characteristics',
        'girl': 'A young female voice with friendly and cheerful characteristics',
        'boy': 'A young male voice with energetic and clear characteristics',
        'warm': 'A warm and friendly voice with engaging characteristics',
        'professional': 'A professional voice with clear and authoritative characteristics',
        'friendly': 'A friendly and approachable voice with warm characteristics',
        'calm': 'A calm and soothing voice with peaceful characteristics',
        'energetic': 'An energetic and lively voice with dynamic characteristics'
    }

    # 检查是否包含中文字符
    has_chinese = any('\u4e00' <= char <= '\u9fff' for char in description)

    if has_chinese:
        # 处理中文描述
        for pattern, template in chinese_patterns.items():
            if pattern in description:
                return template
        # 如果没有匹配的模式，使用通用中文模板
        return f"A voice that matches the description: {description} with natural and clear characteristics"
    else:
        # 处理英文描述
        desc_lower = description.lower()
        for pattern, template in english_patterns.items():
            if pattern in desc_lower:
                return template
        # 如果没有匹配的模式，使用通用英文模板
        return f"A voice with {description} characteristics and natural tone"

# 废弃的search_voices_with_natural_language函数已移除

# 废弃的enhanced_natural_language_search函数已移除

async def call_elevenlabs_text_to_voice_api(description: str, limit: int = 3) -> List[Dict]:
    """调用ElevenLabs Text-to-Voice API生成语音预览"""
    try:
        logger.info(f"🎨 Calling ElevenLabs Text-to-Voice API")

        # 获取API密钥
        api_key = os.getenv('ELEVENLABS_API_KEY')
        if not api_key:
            logger.error("❌ ElevenLabs API key not found")
            return []

        # 增强描述以满足API要求（至少20个字符）
        enhanced_description = enhance_voice_description(description)
        logger.debug(f"📝 Enhanced description: '{enhanced_description}'")

        # ElevenLabs Text-to-Voice API端点
        url = "https://api.elevenlabs.io/v1/text-to-voice/create-previews"

        headers = {
            "Accept": "application/json",
            "xi-api-key": api_key,
            "Content-Type": "application/json"
        }

        # 构建请求数据 - 基于ElevenLabs MCP服务器的实现
        payload = {
            "voice_description": enhanced_description,
            "text": None,  # 让API自动生成文本
            "auto_generate_text": True
        }

        logger.debug(f"📤 Sending request to ElevenLabs: {payload}")

        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.post(url, json=payload, headers=headers)

            if response.status_code == 200:
                result = response.json()
                logger.debug(f"📥 ElevenLabs response: {result}")

                # 解析响应并转换为语音格式
                voices = []

                # 根据ElevenLabs MCP服务器的实现，响应应该包含previews数组
                if 'previews' in result:
                    for i, preview in enumerate(result['previews'][:limit]):
                        voices.append({
                            'id': preview.get('generated_voice_id', f'generated_{i}'),
                            'name': f"AI Generated Voice {i+1}",
                            'description': f"Generated from: {description}",
                            'provider': 'elevenlabs',
                            'language': 'en',
                            'gender': extract_gender_from_description(description),
                            'age': extract_age_from_description(description),
                            'accent': extract_accent_from_description(description),
                            'use_case': 'generated',
                            'preview_url': f"data:audio/mp3;base64,{preview.get('audio_base_64', '')}",
                            'is_generated': True,
                            'audio_data': preview.get('audio_base_64', ''),
                            'generated_voice_id': preview.get('generated_voice_id', '')
                        })
                else:
                    # 如果响应格式不符合预期，记录详细信息
                    logger.warning(f"Unexpected ElevenLabs Text-to-Voice API response format: {result.keys()}")
                    logger.debug(f"Full response: {result}")

                logger.info(f"✅ Generated {len(voices)} voices from ElevenLabs Text-to-Voice API")
                return voices

            else:
                error_msg = f"ElevenLabs Text-to-Voice API error: {response.status_code} - {response.text}"
                logger.error(f"❌ {error_msg}")
                return []

    except Exception as e:
        logger.error(f"❌ ElevenLabs Text-to-Voice API call failed: {e}")
        return []

# 废弃的smart_voice_search函数已移除

def extract_gender_from_description(description: str) -> str:
    """从描述中提取性别信息"""
    desc_lower = description.lower()
    if any(word in desc_lower for word in ['女', '女性', 'female', 'woman', 'feminine', 'she', 'her']):
        return 'female'
    elif any(word in desc_lower for word in ['男', '男性', 'male', 'man', 'masculine', 'he', 'his']):
        return 'male'
    return 'unknown'

def extract_age_from_description(description: str) -> str:
    """从描述中提取年龄信息"""
    desc_lower = description.lower()
    if any(word in desc_lower for word in ['年轻', '青年', 'young', 'youthful', 'teen']):
        return 'young'
    elif any(word in desc_lower for word in ['老', '年长', 'old', 'elderly', 'senior']):
        return 'old'
    elif any(word in desc_lower for word in ['中年', 'middle', 'mature']):
        return 'middle'
    return 'adult'

def extract_accent_from_description(description: str) -> str:
    """从描述中提取口音信息"""
    desc_lower = description.lower()
    if any(word in desc_lower for word in ['英式', '英国', 'british', 'uk']):
        return 'british'
    elif any(word in desc_lower for word in ['美式', '美国', 'american', 'us']):
        return 'american'
    elif any(word in desc_lower for word in ['澳式', '澳洲', 'australian']):
        return 'australian'
    return 'neutral'

async def search_elevenlabs_voices(query: str, limit: int = 10) -> List[Dict]:
    """搜索ElevenLabs语音"""
    try:
        # 使用ElevenLabs的search_voices工具
        ai_request = ServiceRequest(
            capability=ServiceCapability.UTILITY,
            provider=ServiceProvider.ELEVENLABS,
            content="search_voices",
            parameters={
                'tool_name': 'search_voices',
                'tool_params': {
                    'query': query,
                    'limit': limit
                }
            }
        )

        response = await ai_service_manager.process_request(ai_request)

        if response.success and response.data:
            # 解析搜索结果
            import json
            try:
                if isinstance(response.data, str):
                    voices_data = json.loads(response.data)
                else:
                    voices_data = response.data

                # 格式化语音数据
                voices = []
                if isinstance(voices_data, list):
                    for voice in voices_data[:limit]:
                        voices.append({
                            'id': voice.get('voice_id', voice.get('id', '')),
                            'name': voice.get('name', ''),
                            'description': voice.get('description', ''),
                            'provider': 'elevenlabs',
                            'language': voice.get('language', 'en'),
                            'gender': voice.get('gender', ''),
                            'age': voice.get('age', ''),
                            'accent': voice.get('accent', ''),
                            'use_case': voice.get('use_case', ''),
                            'preview_url': voice.get('preview_url', '')
                        })
                elif isinstance(voices_data, dict) and 'voices' in voices_data:
                    for voice in voices_data['voices'][:limit]:
                        voices.append({
                            'id': voice.get('voice_id', voice.get('id', '')),
                            'name': voice.get('name', ''),
                            'description': voice.get('description', ''),
                            'provider': 'elevenlabs',
                            'language': voice.get('language', 'en'),
                            'gender': voice.get('gender', ''),
                            'age': voice.get('age', ''),
                            'accent': voice.get('accent', ''),
                            'use_case': voice.get('use_case', ''),
                            'preview_url': voice.get('preview_url', '')
                        })

                logger.info(f"✅ Found {len(voices)} ElevenLabs voices for query: '{query}'")
                return voices

            except (json.JSONDecodeError, AttributeError) as e:
                logger.warning(f"Failed to parse ElevenLabs search response: {e}")
                return []
        else:
            logger.warning(f"ElevenLabs voice search failed: {response.error if hasattr(response, 'error') else 'Unknown error'}")
            return []

    except Exception as e:
        logger.error(f"Error searching ElevenLabs voices: {e}")
        return []

async def search_minimax_voices(query: str, limit: int = 10) -> List[Dict]:
    """搜索Minimax语音"""
    try:
        # 获取Minimax语音列表并进行本地搜索
        ai_request = ServiceRequest(
            capability=ServiceCapability.UTILITY,
            provider=ServiceProvider.MINIMAX,
            content="list_voices",
            parameters={
                'tool_name': 'list_voices',
                'tool_params': {}
            }
        )

        response = await ai_service_manager.process_request(ai_request)

        if response.success and response.data:
            # 解析语音列表
            import json
            try:
                if isinstance(response.data, str):
                    voices_data = json.loads(response.data)
                else:
                    voices_data = response.data

                # 获取所有语音
                all_voices = []
                if isinstance(voices_data, list):
                    all_voices = voices_data
                elif isinstance(voices_data, dict) and 'voices' in voices_data:
                    all_voices = voices_data['voices']

                # 本地搜索匹配
                query_lower = query.lower()
                matched_voices = []

                for voice in all_voices:
                    voice_name = voice.get('name', '').lower()
                    voice_desc = voice.get('description', '').lower()
                    voice_id = voice.get('id', voice.get('voice_id', '')).lower()

                    # 简单的关键词匹配
                    if (query_lower in voice_name or
                        query_lower in voice_desc or
                        query_lower in voice_id or
                        any(keyword in voice_name or keyword in voice_desc
                            for keyword in query_lower.split())):

                        matched_voices.append({
                            'id': voice.get('id', voice.get('voice_id', '')),
                            'name': voice.get('name', ''),
                            'description': voice.get('description', ''),
                            'provider': 'minimax',
                            'language': voice.get('language', 'zh'),
                            'gender': voice.get('gender', ''),
                            'age': voice.get('age', ''),
                            'accent': voice.get('accent', ''),
                            'use_case': voice.get('use_case', ''),
                            'preview_url': voice.get('preview_url', '')
                        })

                # 限制结果数量
                matched_voices = matched_voices[:limit]
                logger.info(f"✅ Found {len(matched_voices)} Minimax voices for query: '{query}'")
                return matched_voices

            except (json.JSONDecodeError, AttributeError) as e:
                logger.warning(f"Failed to parse Minimax voices response: {e}")
                return []
        else:
            logger.warning(f"Minimax voice list failed: {response.error if hasattr(response, 'error') else 'Unknown error'}")
            return []

    except Exception as e:
        logger.error(f"Error searching Minimax voices: {e}")
        return []

# 常用语音管理函数
async def load_favorite_voices() -> List[Dict]:
    """加载常用语音列表"""
    try:
        favorites_file = Path(get_config('storage.output_dir', './output')) / 'favorite_voices.json'

        if not favorites_file.exists():
            # 如果文件不存在，尝试从ElevenLabs获取一些默认语音
            default_favorites = []

            try:
                # 获取ElevenLabs的可用语音
                elevenlabs_voices = await get_elevenlabs_voices_from_mcp()

                # 选择前3个可用的语音作为默认常用语音
                for voice in elevenlabs_voices[:3]:
                    voice_id = voice.get('id')
                    if voice_id and await validate_elevenlabs_voice_id(voice_id):
                        default_voice = {
                            "voice_id": voice_id,
                            "voice_name": voice.get('name', voice_id),
                            "provider": "elevenlabs",
                            "description": voice.get('description', '高质量AI语音'),
                            "category": voice.get('category', 'professional'),
                            "language": voice.get('language', 'en'),
                            "preview_url": voice.get('preview_url', ''),
                            "added_at": datetime.now().isoformat(),
                            "usage_count": 0
                        }
                        default_favorites.append(default_voice)

                logger.info(f"✅ Created default favorites with {len(default_favorites)} validated voices")

            except Exception as e:
                logger.warning(f"Failed to get default voices from ElevenLabs: {e}")
                # 如果获取失败，创建一个空列表
                default_favorites = []

            await save_favorite_voices(default_favorites)
            return default_favorites

        with open(favorites_file, 'r', encoding='utf-8') as f:
            favorites = json.load(f)
            return favorites

    except Exception as e:
        logger.error(f"Failed to load favorite voices: {e}")
        return []

async def save_favorite_voices(favorites: List[Dict]) -> bool:
    """保存常用语音列表"""
    try:
        favorites_file = Path(get_config('storage.output_dir', './output')) / 'favorite_voices.json'
        favorites_file.parent.mkdir(exist_ok=True)

        with open(favorites_file, 'w', encoding='utf-8') as f:
            json.dump(favorites, f, ensure_ascii=False, indent=2)

        logger.info(f"✅ Saved {len(favorites)} favorite voices to {favorites_file}")
        return True

    except Exception as e:
        logger.error(f"Failed to save favorite voices: {e}")
        return False

async def validate_elevenlabs_voice_id(voice_id: str) -> bool:
    """验证ElevenLabs语音ID是否有效"""
    try:
        from ..services.fastmcp_server import fastmcp_manager
        mcp_manager = fastmcp_manager

        if not mcp_manager._initialized:
            logger.warning("MCP manager not initialized")
            return False

        if 'elevenlabs' not in mcp_manager.server_info:
            logger.warning("ElevenLabs MCP server not available")
            return False

        server_info = mcp_manager.server_info['elevenlabs']
        if not server_info.is_connected:
            logger.warning("ElevenLabs MCP server not connected")
            return False

        # 尝试调用ElevenLabs MCP工具验证语音ID
        try:
            # 使用更长的测试文本来确保真正触发API调用和权限检查
            test_params = {
                'text': 'This is a test to validate voice permissions and accessibility.',
                'voice_id': voice_id,
                'model_id': 'eleven_multilingual_v2',
                'output_format': 'mp3_44100_128'
            }

            # 调用text_to_speech工具，如果语音ID无效或无权限会抛出异常
            result = await mcp_manager.call_tool('elevenlabs', 'text_to_speech', test_params)

            # 如果没有抛出异常，说明语音ID有效且用户有权限使用
            logger.debug(f"✅ Voice ID {voice_id} is valid and accessible")
            return True

        except Exception as e:
            error_msg = str(e).lower()
            if any(keyword in error_msg for keyword in [
                'voice_not_found', 'not found', 'voice not found',
                'unauthorized', 'permission denied', 'access denied',
                'insufficient permissions', 'voice unavailable',
                'voice not accessible', 'invalid voice'
            ]):
                logger.debug(f"❌ Voice ID {voice_id} not accessible: {e}")
                return False
            else:
                # 其他错误（如网络问题）不应该导致语音被标记为无效
                logger.warning(f"⚠️ Error validating voice ID {voice_id}: {e}")
                return False  # 改为保守处理，如果验证失败就认为不可用

    except Exception as e:
        logger.error(f"Failed to validate voice ID {voice_id}: {e}")
        return True  # 保守处理，假设语音有效

async def get_voice_info_from_elevenlabs(voice_id: str) -> Dict:
    """从ElevenLabs API获取语音的官方信息"""
    try:
        from ..services.fastmcp_server import fastmcp_manager
        mcp_manager = fastmcp_manager
        if not mcp_manager._initialized:
            raise Exception("MCP manager not initialized")

        # 直接调用现有的语音获取函数
        try:
            voices = await get_elevenlabs_voices_from_mcp()
            for voice in voices:
                if voice.get('id') == voice_id:
                    return {
                        'voice_id': voice_id,
                        'name': voice.get('name', voice_id),
                        'description': voice.get('description', ''),
                        'category': voice.get('category', 'professional'),
                        'language': voice.get('language', 'en'),
                        'preview_url': voice.get('preview_url', '')
                    }
        except Exception as e:
            logger.warning(f"Failed to get voice from ElevenLabs: {e}")

        # 如果都没找到，返回基本信息
        return {
            'voice_id': voice_id,
            'name': voice_id,
            'description': '语音信息',
            'category': 'unknown',
            'language': 'en',
            'preview_url': ''
        }

    except Exception as e:
        logger.error(f"Failed to get voice info from ElevenLabs: {e}")
        return {
            'voice_id': voice_id,
            'name': voice_id,
            'description': '语音信息',
            'category': 'unknown',
            'language': 'en',
            'preview_url': ''
        }

async def update_voice_usage_count(voice_id: str, provider: str):
    """更新语音使用次数"""
    try:
        favorites = await load_favorite_voices()

        for voice in favorites:
            if voice['voice_id'] == voice_id and voice['provider'] == provider:
                voice['usage_count'] = voice.get('usage_count', 0) + 1
                voice['last_used'] = datetime.now().isoformat()
                break

        await save_favorite_voices(favorites)

    except Exception as e:
        logger.error(f"Failed to update voice usage count: {e}")

async def get_elevenlabs_clone_voices() -> List[Dict]:
    """从ElevenLabs获取用户的克隆声音列表"""
    clone_voices = []
    try:
        from ..services.fastmcp_server import fastmcp_manager

        if not fastmcp_manager._initialized:
            logger.warning("FastMCP manager not initialized")
            return clone_voices

        if 'elevenlabs' not in fastmcp_manager.server_info:
            logger.warning("ElevenLabs MCP server not available")
            return clone_voices

        server_info = fastmcp_manager.server_info['elevenlabs']
        if not server_info.is_connected:
            logger.warning("ElevenLabs MCP server not connected")
            return clone_voices

        # 调用search_voices工具获取所有声音（包括克隆声音）
        result = await fastmcp_manager.call_tool('elevenlabs', 'search_voices', {
            'search': '',  # 空查询获取所有语音
            'sort': 'name',
            'sort_direction': 'asc'
        })

        if hasattr(result, 'content') and isinstance(result.content, list):
            for item in result.content:
                if hasattr(item, 'text'):
                    # 解析文本内容中的语音信息
                    voices_data = parse_voice_library_text(item.text)

                    # 过滤出克隆的声音（通常包含"clone"、"custom"等标识）
                    for voice_data in voices_data:
                        if is_clone_voice(voice_data):
                            clone_voice = {
                                'voice_id': voice_data.get('voice_id', ''),
                                'name': voice_data.get('name', ''),
                                'provider': 'elevenlabs',
                                'category': 'clone',
                                'language': voice_data.get('language', 'en'),
                                'description': voice_data.get('description', ''),
                                'clone_type': voice_data.get('clone_type', 'instant'),
                                'created_at': voice_data.get('created_at', ''),
                                'status': voice_data.get('status', 'ready')
                            }
                            clone_voices.append(clone_voice)

        logger.info(f"✅ Found {len(clone_voices)} ElevenLabs clone voices")

    except Exception as e:
        logger.error(f"❌ Failed to get ElevenLabs clone voices: {e}")

    return clone_voices

async def get_minimax_clone_voices() -> List[Dict]:
    """从MiniMax获取用户的克隆声音列表"""
    clone_voices = []
    try:
        from ..services.fastmcp_server import fastmcp_manager

        if not fastmcp_manager._initialized:
            logger.warning("FastMCP manager not initialized")
            return clone_voices

        if 'minimax' not in fastmcp_manager.server_info:
            logger.warning("MiniMax MCP server not available")
            return clone_voices

        server_info = fastmcp_manager.server_info['minimax']
        if not server_info.is_connected:
            logger.warning("MiniMax MCP server not connected")
            return clone_voices

        # 🔑 修复：调用list_voices工具获取所有声音，包括克隆声音
        logger.info("🔍 调用MiniMax list_voices工具获取声音列表...")
        result = await fastmcp_manager.call_tool('minimax', 'list_voices', {}, timeout=30)

        logger.info(f"🔍 MiniMax list_voices响应类型: {type(result)}")

        if hasattr(result, 'content') and isinstance(result.content, list):
            logger.info(f"🔍 MiniMax响应包含 {len(result.content)} 个内容项")

            for i, item in enumerate(result.content):
                if hasattr(item, 'text'):
                    text_content = item.text
                    logger.info(f"🔍 内容项 {i}: {text_content[:200]}...")

                    # 🔑 修复：解析MiniMax返回的语音列表
                    # MiniMax可能返回JSON格式或文本格式的语音列表
                    voices_data = _parse_minimax_voices_response(text_content)
                    logger.info(f"🔍 解析出 {len(voices_data)} 个语音")

                    # 过滤出克隆的声音
                    for voice_data in voices_data:
                        logger.info(f"🔍 检查语音: {voice_data.get('name', 'Unknown')} - {voice_data.get('voice_id', 'No ID')}")

                        if _is_minimax_clone_voice(voice_data):
                            clone_voice = {
                                'voice_id': voice_data.get('voice_id', voice_data.get('id', '')),
                                'name': voice_data.get('name', ''),
                                'provider': 'minimax',
                                'category': 'clone',
                                'language': voice_data.get('language', 'zh'),
                                'description': voice_data.get('description', f"MiniMax克隆声音: {voice_data.get('name', '')}"),
                                'clone_type': voice_data.get('clone_type', 'custom'),
                                'created_at': voice_data.get('created_at', ''),
                                'status': voice_data.get('status', 'ready')
                            }
                            clone_voices.append(clone_voice)
                            logger.info(f"✅ 找到克隆声音: {clone_voice['name']} ({clone_voice['voice_id']})")
        else:
            logger.warning("⚠️ MiniMax响应格式不符合预期")

        logger.info(f"✅ Found {len(clone_voices)} MiniMax clone voices")

    except Exception as e:
        logger.error(f"❌ Failed to get MiniMax clone voices: {e}")

    return clone_voices

def _parse_minimax_voices_response(text_content: str) -> List[Dict]:
    """解析MiniMax语音列表响应"""
    voices = []
    try:
        logger.info(f"🔍 开始解析MiniMax响应: {text_content[:100]}...")

        # 🔑 修复：专门处理MiniMax的特殊响应格式
        # MiniMax返回格式：Success. System Voices: [...], Voice Cloning Voices: [...]

        if "Voice Cloning Voices:" in text_content:
            # 提取克隆声音部分
            import re

            # 查找 Voice Cloning Voices: [...] 部分
            clone_match = re.search(r'Voice Cloning Voices:\s*\[(.*?)\]', text_content, re.DOTALL)
            if clone_match:
                clone_content = clone_match.group(1).strip()
                logger.info(f"🔍 找到克隆声音内容: {clone_content[:100]}...")

                if clone_content and clone_content != '':
                    # 解析克隆声音列表
                    # 格式可能是：'Name: ZY1234567890, ID: voice_ZY1234567890_1234567890'
                    clone_items = re.findall(r"'Name:\s*([^,]+),\s*ID:\s*([^']+)'", clone_content)

                    for name, voice_id in clone_items:
                        voice_data = {
                            'voice_id': voice_id.strip(),
                            'name': name.strip(),
                            'description': f"MiniMax克隆声音: {name.strip()}",
                            'type': 'clone',
                            'category': 'clone',
                            'language': 'zh'
                        }
                        voices.append(voice_data)
                        logger.info(f"✅ 解析到克隆声音: {name.strip()} ({voice_id.strip()})")
                else:
                    logger.info("📝 克隆声音列表为空")

            logger.info(f"🔍 MiniMax克隆声音解析完成，找到 {len(voices)} 个克隆声音")
            return voices

        # 如果没有找到特殊格式，尝试JSON解析
        import json
        try:
            data = json.loads(text_content)
            if isinstance(data, dict):
                if 'voices' in data:
                    voices = data['voices']
                elif 'data' in data and isinstance(data['data'], list):
                    voices = data['data']
                elif isinstance(data.get('result'), list):
                    voices = data['result']
            elif isinstance(data, list):
                voices = data

            logger.info(f"🔍 JSON解析成功，找到 {len(voices)} 个语音")
            return voices

        except json.JSONDecodeError:
            logger.warning("⚠️ 无法解析MiniMax响应格式，返回空列表")
            return []

    except Exception as e:
        logger.error(f"❌ 解析MiniMax语音响应失败: {e}")
        return []

def _is_minimax_clone_voice(voice_data: Dict) -> bool:
    """判断是否为MiniMax克隆声音"""
    # 🔑 修复：更精确的克隆声音识别逻辑

    # 检查语音数据中的克隆标识
    name = voice_data.get('name', '').lower()
    voice_id = voice_data.get('voice_id', voice_data.get('id', '')).lower()
    description = voice_data.get('description', '').lower()
    voice_type = voice_data.get('type', '').lower()
    category = voice_data.get('category', '').lower()

    # 1. 直接检查type和category字段
    if voice_type == 'clone' or category == 'clone':
        return True

    # 2. 检查是否有明确的克隆标识
    if (voice_data.get('is_clone') or voice_data.get('clone_type')):
        return True

    # 3. 检查描述中是否包含"克隆"
    if '克隆' in description or 'clone' in description.lower():
        return True

    # 4. 检查voice_id格式 - MiniMax克隆声音通常有特定格式
    # 例如：voice_ZY1234567890_1234567890
    if voice_id.startswith(('voice_', 'clone_', 'minimax_clone_')):
        return True

    # 5. 检查是否是用户自定义名称（通常不是系统预设的格式）
    # 系统预设声音通常有特定的命名模式
    system_patterns = [
        'male-', 'female-', 'presenter_', 'audiobook_', 'chinese', 'english_',
        'japanese_', 'korean_', 'spanish_', 'portuguese_', 'french_', 'german_',
        'russian_', 'italian_', 'arabic_', 'turkish_', 'ukrainian_', 'indonesian_',
        'dutch_', 'vietnamese_', 'cantonese_'
    ]

    # 如果voice_id不匹配任何系统模式，且包含用户自定义特征，可能是克隆声音
    is_system_voice = any(pattern in voice_id for pattern in system_patterns)
    if not is_system_voice and len(voice_id) > 5:
        # 进一步检查是否包含用户自定义特征（字母+数字组合）
        if any(char.isdigit() for char in voice_id) and any(char.isalpha() for char in voice_id):
            return True

    return False

def is_clone_voice(voice_data: Dict) -> bool:
    """判断是否为克隆声音"""
    # 检查声音名称、描述或类别中是否包含克隆相关关键词
    name = voice_data.get('name', '').lower()
    description = voice_data.get('description', '').lower()
    category = voice_data.get('category', '').lower()

    clone_keywords = ['clone', 'custom', 'user', 'my', '克隆', '自定义', '我的']

    for keyword in clone_keywords:
        if keyword in name or keyword in description or keyword in category:
            return True

    # 检查是否有克隆相关的属性
    if voice_data.get('is_clone') or voice_data.get('clone_type'):
        return True

    return False

async def create_elevenlabs_voice_clone(name: str, audio_files: List[str]) -> Dict:
    """使用ElevenLabs创建声音克隆"""
    try:
        from ..services.fastmcp_server import fastmcp_manager

        if not fastmcp_manager._initialized:
            return {"success": False, "error": "FastMCP manager not initialized"}

        if 'elevenlabs' not in fastmcp_manager.server_info:
            return {"success": False, "error": "ElevenLabs MCP server not available"}

        server_info = fastmcp_manager.server_info['elevenlabs']
        if not server_info.is_connected:
            return {"success": False, "error": "ElevenLabs MCP server not connected"}

        # 调用voice_clone工具创建克隆（修复参数名称和超时）
        result = await fastmcp_manager.call_tool('elevenlabs', 'voice_clone', {
            'name': name,
            'files': audio_files,  # 🔑 修复：使用正确的参数名 'files'
            'clone_type': 'instant'  # 默认使用即时克隆
        }, timeout=90)  # 🔑 修复：设置90秒超时

        if hasattr(result, 'content') and isinstance(result.content, list):
            for item in result.content:
                if hasattr(item, 'text'):
                    # 解析返回的文本内容
                    response_text = item.text
                    if 'success' in response_text.lower() or 'created' in response_text.lower():
                        # 尝试从响应中提取voice_id
                        import re
                        voice_id_match = re.search(r'voice[_-]?id[:\s]*([a-zA-Z0-9_-]+)', response_text)
                        voice_id = voice_id_match.group(1) if voice_id_match else f"clone_{int(time.time())}"

                        return {
                            "success": True,
                            "voice_id": voice_id,
                            "status": "created",
                            "message": response_text
                        }
                    else:
                        # 🔑 修复：提供更详细的错误信息和解决方案
                        if 'can_not_use_instant_voice_cloning' in response_text:
                            return {
                                "success": False,
                                "error": "ElevenLabs语音克隆需要付费订阅",
                                "details": {
                                    "reason": "当前账户不支持即时语音克隆功能",
                                    "solution": "升级到Starter计划($5/月)以使用即时语音克隆，或Creator计划($22/月)以使用专业语音克隆",
                                    "alternatives": "建议使用MiniMax提供商进行语音克隆",
                                    "upgrade_url": "https://elevenlabs.io/pricing"
                                }
                            }
                        else:
                            return {"success": False, "error": response_text}

        return {"success": False, "error": "Unexpected response format"}

    except Exception as e:
        logger.error(f"Failed to create ElevenLabs voice clone: {e}")
        error_msg = str(e)

        # 🔑 修复：提供更友好的错误信息
        if 'can_not_use_instant_voice_cloning' in error_msg:
            return {
                "success": False,
                "error": "ElevenLabs语音克隆需要付费订阅",
                "details": {
                    "reason": "当前账户不支持即时语音克隆功能",
                    "solution": "升级到Starter计划($5/月)以使用即时语音克隆，或Creator计划($22/月)以使用专业语音克隆",
                    "alternatives": "建议使用MiniMax提供商进行语音克隆",
                    "upgrade_url": "https://elevenlabs.io/pricing"
                }
            }
        elif 'timeout' in error_msg.lower():
            return {"success": False, "error": "ElevenLabs语音克隆请求超时，请稍后重试"}
        elif 'connection' in error_msg.lower():
            return {"success": False, "error": "ElevenLabs服务连接失败，请检查网络连接"}
        else:
            return {"success": False, "error": f"ElevenLabs语音克隆失败: {error_msg}"}

async def create_minimax_voice_clone(name: str, audio_files: List[str]) -> Dict:
    """使用MiniMax创建声音克隆 - 增强版本，包含详细日志和错误处理"""
    logger.info(f"🎭 开始创建MiniMax语音克隆: name='{name}', audio_files={len(audio_files)} files")

    try:
        # 🔑 步骤1：导入和初始化检查
        from ..services.fastmcp_server import fastmcp_manager
        logger.info("🔧 导入FastMCP管理器成功")

        if not fastmcp_manager._initialized:
            logger.error("❌ FastMCP管理器未初始化")
            return {"success": False, "error": "FastMCP manager not initialized"}

        if 'minimax' not in fastmcp_manager.server_info:
            logger.error("❌ MiniMax MCP服务器不可用")
            return {"success": False, "error": "MiniMax MCP server not available"}

        server_info = fastmcp_manager.server_info['minimax']
        if not server_info.is_connected:
            logger.error("❌ MiniMax MCP服务器未连接")
            return {"success": False, "error": "MiniMax MCP server not connected"}

        logger.info("✅ MiniMax MCP服务器连接状态正常")

        # 🔑 步骤1.5：连接健康检查
        logger.info("🔍 执行MiniMax连接健康检查...")
        is_healthy = await fastmcp_manager.check_connection_health('minimax')
        if not is_healthy:
            logger.error("❌ MiniMax连接健康检查失败")
            return {"success": False, "error": "MiniMax服务连接不稳定，请稍后重试"}
        logger.info("✅ MiniMax连接健康检查通过")

        # 🔑 步骤2：参数验证和预处理
        if not audio_files:
            logger.error("❌ MiniMax语音克隆失败：没有提供音频文件")
            return {"success": False, "error": "没有提供音频文件"}

        if not name or not name.strip():
            logger.error("❌ MiniMax语音克隆失败：克隆名称为空")
            return {"success": False, "error": "克隆名称不能为空"}

        # 🔑 步骤3：生成符合MiniMax官方要求的voice_id
        import re

        def generate_valid_voice_id(name: str) -> str:
            """
            生成符合MiniMax官方要求的voice_id：
            1. 长度范围[3,256]
            2. 首字符必须为英文字母
            3. 允许数字、字母、下划线_
            4. 末位字符不可为、_
            5. 示例：minimax_man_01
            """
            # 清理名称：只保留字母、数字、下划线
            clean_name = re.sub(r'[^a-zA-Z0-9_]', '_', name.strip())

            # 确保首字符为英文字母
            if not clean_name or not clean_name[0].isalpha():
                clean_name = f"voice_{clean_name}" if clean_name else "voice"

            # 添加时间戳确保唯一性
            timestamp = str(int(time.time()))
            voice_id = f"{clean_name}_{timestamp}"

            # 确保末位字符不是下划线
            voice_id = voice_id.rstrip('_')

            # 确保长度在[3,256]范围内
            if len(voice_id) < 3:
                voice_id = f"voice_{timestamp}"
            elif len(voice_id) > 256:
                # 截断但保留时间戳
                max_name_len = 256 - len(timestamp) - 1  # 减去下划线和时间戳长度
                voice_id = f"{clean_name[:max_name_len]}_{timestamp}"

            return voice_id

        voice_id = generate_valid_voice_id(name)
        logger.info(f"🔧 生成符合官方要求的voice_id: {voice_id} (长度: {len(voice_id)})")

        # 🔑 步骤3.5：验证voice_id是否符合官方要求
        def validate_voice_id(vid: str) -> tuple[bool, str]:
            """验证voice_id是否符合MiniMax官方要求"""
            if len(vid) < 3 or len(vid) > 256:
                return False, f"长度不符合要求[3,256]，当前长度: {len(vid)}"

            if not vid[0].isalpha():
                return False, f"首字符必须为英文字母，当前首字符: '{vid[0]}'"

            if not re.match(r'^[a-zA-Z0-9_]+$', vid):
                return False, "只允许数字、字母、下划线"

            if vid.endswith('_'):
                return False, "末位字符不可为下划线"

            return True, "voice_id格式正确"

        is_valid, validation_msg = validate_voice_id(voice_id)
        if not is_valid:
            logger.error(f"❌ voice_id验证失败: {validation_msg}")
            return {"success": False, "error": f"voice_id格式错误: {validation_msg}"}

        logger.info(f"✅ voice_id验证通过: {validation_msg}")

        # 🔑 步骤4：准备MiniMax API参数（修复文件路径处理）
        from pathlib import Path

        audio_file_path = audio_files[0]
        logger.info(f"🔧 原始音频文件路径: {audio_file_path}")

        # 🔑 关键修复：转换为相对于MiniMax BASE_PATH的路径
        # MiniMax MCP BASE_PATH = "./output"
        # 我们的文件路径 = "./output/voice_clone_uploads/test_audio.wav"
        # 需要转换为相对路径 = "voice_clone_uploads/test_audio.wav"

        try:
            abs_file_path = Path(audio_file_path).resolve()

            # 🔑 尝试多种路径格式，看哪种MiniMax能识别
            logger.info(f"🔧 绝对路径: {abs_file_path}")
            logger.info(f"🔧 文件是否存在: {abs_file_path.exists()}")

            # 先尝试绝对路径
            minimax_file_path = str(abs_file_path)
            logger.info(f"🔧 使用绝对路径: {minimax_file_path}")

            minimax_params = {
                'voice_id': voice_id,  # 必需的voice_id
                'file': minimax_file_path,  # 使用绝对路径
                'text': f"Custom voice clone: {name}"  # 描述文本
            }
            logger.info(f"🔧 MiniMax API参数: voice_id={voice_id}, file='{minimax_file_path}', text='{minimax_params['text']}'")

        except Exception as file_error:
            logger.error(f"❌ 处理音频文件路径失败: {file_error}")
            return {"success": False, "error": f"处理音频文件路径失败: {file_error}"}

        # 🔑 步骤5：调用MiniMax MCP工具（增强错误处理）
        logger.info("🚀 开始调用MiniMax voice_clone工具...")
        result = await fastmcp_manager.call_tool('minimax', 'voice_clone', minimax_params, timeout=120)
        logger.info("✅ MiniMax voice_clone工具调用完成")

        # 🔑 步骤6：处理MiniMax API响应（修复解析逻辑）
        logger.info(f"🔍 分析MiniMax API响应: type={type(result)}")

        if hasattr(result, 'content') and isinstance(result.content, list):
            logger.info(f"🔍 响应包含 {len(result.content)} 个内容项")
            for i, item in enumerate(result.content):
                if hasattr(item, 'text'):
                    response_text = item.text
                    logger.info(f"🔍 内容项 {i}: {response_text}")

                    # 🔑 关键修复：正确解析MiniMax响应
                    # 检查是否包含明确的错误信息
                    if 'API Error:' in response_text:
                        logger.error(f"❌ MiniMax语音克隆失败: {response_text}")
                        return {"success": False, "error": f"Failed to clone voice: {response_text}"}

                    # 检查是否成功（MiniMax返回"Success"和Demo audio URL）
                    elif 'Success' in response_text and 'Demo audio URL' in response_text:
                        # MiniMax成功响应格式：Success. Demo audio URL: https://...
                        # 使用原始的voice_id作为克隆ID
                        logger.info(f"✅ MiniMax语音克隆成功: voice_id={voice_id}")
                        logger.info(f"🎵 演示音频: {response_text}")

                        # 提取演示音频URL
                        import re
                        demo_url_match = re.search(r'Demo audio URL: (https?://[^\s]+)', response_text)
                        demo_url = demo_url_match.group(1) if demo_url_match else None

                        # 保存克隆声音到本地存储
                        from ..storage.clone_voices import clone_voice_storage
                        clone_data = {
                            "voice_id": voice_id,
                            "name": name,
                            "provider": "minimax",
                            "demo_url": demo_url,
                            "message": response_text
                        }
                        clone_voice_storage.add_clone_voice(clone_data)

                        return {
                            "success": True,
                            "voice_id": voice_id,  # 使用我们生成的voice_id
                            "status": "created",
                            "message": response_text,
                            "demo_url": demo_url
                        }

                    # 检查其他成功模式
                    elif 'success' in response_text.lower() and 'voice_id' in response_text.lower():
                        # 尝试从响应中提取真正的voice_id
                        import re

                        voice_id_match = re.search(r'voice_id["\s:]+([a-zA-Z0-9_]+)', response_text)
                        if voice_id_match:
                            actual_voice_id = voice_id_match.group(1)
                            logger.info(f"✅ MiniMax语音克隆成功: voice_id={actual_voice_id}")

                            return {
                                "success": True,
                                "voice_id": actual_voice_id,
                                "status": "created",
                                "message": response_text
                            }
                        else:
                            logger.warning(f"⚠️ 响应显示成功但无法提取voice_id: {response_text}")
                            return {"success": False, "error": "克隆成功但无法获取voice_id"}

                    else:
                        logger.error(f"❌ MiniMax语音克隆失败: {response_text}")
                        return {"success": False, "error": f"Failed to clone voice: {response_text}"}
        else:
            logger.error(f"❌ MiniMax API响应格式异常: {result}")

        logger.error("❌ MiniMax API响应格式不符合预期")
        return {"success": False, "error": "Unexpected response format"}

    except Exception as e:
        error_msg = str(e)
        logger.error(f"❌ MiniMax语音克隆异常: {error_msg}")
        logger.error(f"❌ 异常类型: {type(e).__name__}")

        # 🔑 增强错误处理：提供更友好的错误信息
        if 'timeout' in error_msg.lower():
            return {
                "success": False,
                "error": "MiniMax语音克隆请求超时",
                "details": "请求处理时间超过2分钟，请稍后重试或检查网络连接"
            }
        elif 'connection' in error_msg.lower():
            return {
                "success": False,
                "error": "MiniMax服务连接失败",
                "details": "无法连接到MiniMax服务，请检查网络连接或稍后重试"
            }
        elif 'not connected' in error_msg.lower():
            return {
                "success": False,
                "error": "MiniMax MCP服务未连接",
                "details": "MCP连接已断开，请刷新页面重试"
            }
        else:
            return {
                "success": False,
                "error": f"MiniMax语音克隆失败: {error_msg}",
                "details": "请检查参数设置或稍后重试"
            }

# 创建FastAPI应用实例（用于测试和独立运行）
from fastapi import FastAPI

app = FastAPI(title="DaVinci AI Co-pilot Pro API", version="1.0.0")
app.include_router(router)
