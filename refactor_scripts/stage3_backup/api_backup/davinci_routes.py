"""
DaVinci Resolve API路由
提供DaVinci Resolve集成的REST API接口
"""

import logging
import time
from typing import Dict, List, Optional, Any
from fastapi import APIRouter, HTTPException, UploadFile, File, Form
from pydantic import BaseModel, Field

from ..davinci import resolve_api, ResolveAPIError, RESOLVE_AVAILABLE
from ..core import APIError, ErrorCode

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/davinci", tags=["DaVinci Resolve"])

# 请求模型
class ProjectCreateRequest(BaseModel):
    name: str = Field(..., description="项目名称")
    settings: Dict[str, Any] = Field(default_factory=dict, description="项目设置")

class TimelineCreateRequest(BaseModel):
    name: str = Field(..., description="时间线名称")
    settings: Dict[str, Any] = Field(default_factory=dict, description="时间线设置")

class AIMediaImportRequest(BaseModel):
    file_paths: List[str] = Field(..., description="AI生成的媒体文件路径列表")
    content_type: str = Field(default="general", description="内容类型：speech, video, image, general")

class AITimelineCreateRequest(BaseModel):
    timeline_name: str = Field(..., description="时间线名称")
    ai_scenes: List[Dict[str, Any]] = Field(..., description="AI分析的场景列表")

class FrameExportRequest(BaseModel):
    output_dir: Optional[str] = Field(None, description="输出目录路径")
    filename: Optional[str] = Field(None, description="文件名")
    format: str = Field(default="PNG", description="导出格式：PNG, JPEG")
    quality: str = Field(default="Best", description="导出质量：Best, High, Medium, Low")

class AIFrameRequest(BaseModel):
    ai_service_type: str = Field(default="image_generation", description="AI服务类型：image_generation, video_generation")

class AudioExtractionRequest(BaseModel):
    track_index: int = Field(default=1, description="音轨索引，从1开始")

class SubtitleGenerationRequest(BaseModel):
    track_index: int = Field(default=1, description="音频轨道索引")
    language: str = Field(default="zh-CN", description="字幕语言")
    ai_service: str = Field(default="deepseek", description="使用的AI服务")

class SubtitleTranslationRequest(BaseModel):
    source_language: str = Field(..., description="源语言")
    target_language: str = Field(..., description="目标语言")
    subtitle_file: Optional[str] = Field(None, description="字幕文件路径")

class SmartMarkingRequest(BaseModel):
    analysis_type: str = Field(default="comprehensive", description="分析类型：comprehensive, content_only, quality_only")
    confidence_threshold: float = Field(default=0.7, description="置信度阈值")
    auto_apply: bool = Field(default=False, description="是否自动应用标记")

class MediaImportRequest(BaseModel):
    file_paths: List[str] = Field(..., description="媒体文件路径列表")
    target_folder: Optional[str] = Field(None, description="目标文件夹名称")

class ClipInfo(BaseModel):
    name: str = Field(..., description="片段名称")
    track: int = Field(1, description="轨道索引")
    start_frame: int = Field(0, description="开始帧")

class TimelineAddClipsRequest(BaseModel):
    clips: List[ClipInfo] = Field(..., description="要添加的片段信息")

class ExportRequest(BaseModel):
    export_path: str = Field(..., description="导出路径")
    filename: str = Field(..., description="文件名")
    format: str = Field("mp4", description="导出格式")
    export_video: bool = Field(True, description="是否导出视频")
    export_audio: bool = Field(True, description="是否导出音频")
    render_settings: Dict[str, Any] = Field(default_factory=dict, description="渲染设置")

# API端点
@router.get("/status")
async def get_davinci_status():
    """获取DaVinci Resolve连接状态 - 重构版本"""
    try:
        if not RESOLVE_AVAILABLE:
            return {
                "success": False,
                "message": "DaVinci Resolve Python API not available",
                "data": {
                    "connected": False,
                    "api_available": False,
                    "project_loaded": False,
                    "connection_state": "error",
                    "api_version": "unknown"
                }
            }

        # 使用新的状态查询方法
        status = {
            "connected": resolve_api.is_connected(),
            "api_available": RESOLVE_AVAILABLE,
            "project_loaded": resolve_api.is_project_loaded(),
            "connection_state": resolve_api.get_connection_state().value,
            "api_version": resolve_api.get_api_version().value,
            "last_error": resolve_api.get_last_error()
        }

        if resolve_api.is_connected():
            try:
                system_info = await resolve_api.get_system_info()
                status.update(system_info)
            except Exception as e:
                logger.warning(f"Failed to get system info: {e}")

        return {
            "success": True,
            "data": status
        }

    except Exception as e:
        logger.error(f"Error getting DaVinci status: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get status: {str(e)}")

@router.post("/connect")
async def connect_to_davinci():
    """连接到DaVinci Resolve"""
    try:
        if not RESOLVE_AVAILABLE:
            raise HTTPException(
                status_code=400,
                detail="DaVinci Resolve Python API not available"
            )

        success = await resolve_api.initialize()

        if success:
            system_info = await resolve_api.get_system_info()
            return {
                "success": True,
                "message": "Connected to DaVinci Resolve successfully",
                "data": system_info
            }
        else:
            raise HTTPException(
                status_code=500,
                detail="Failed to connect to DaVinci Resolve"
            )

    except ResolveAPIError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error connecting to DaVinci: {e}")
        raise HTTPException(status_code=500, detail=f"Connection failed: {str(e)}")

@router.get("/project/info")
async def get_project_info():
    """获取当前项目信息"""
    try:
        if not resolve_api.is_project_loaded():
            raise HTTPException(status_code=400, detail="No project loaded")

        project_info = await resolve_api.get_project_info()

        return {
            "success": True,
            "data": project_info
        }

    except ResolveAPIError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error getting project info: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get project info: {str(e)}")

# 项目管理功能已移除 - 不是核心功能，且API不稳定

# 项目创建和加载功能已移除 - 不是核心功能，且API不稳定

@router.post("/media/import")
async def import_media(request: MediaImportRequest):
    """导入媒体文件"""
    try:
        if not resolve_api.is_project_loaded():
            raise HTTPException(status_code=400, detail="No project loaded")

        # 注意：这个方法在新API中已被移除，需要使用import_ai_generated_media
        # 这里保留兼容性，但建议使用AI专用导入方法
        result = await resolve_api.import_ai_generated_media(request.file_paths, "legacy")

        return {
            "success": True,
            "message": f"Imported {result['imported_count']} files, {result['failed_count']} failed",
            "data": result
        }

    except ResolveAPIError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error importing media: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to import media: {str(e)}")

@router.post("/timeline/create")
async def create_timeline(request: TimelineCreateRequest):
    """创建新时间线"""
    try:
        if not resolve_api.is_project_loaded():
            raise HTTPException(status_code=400, detail="No project loaded")

        success = await resolve_api.create_timeline(request.name, request.settings)

        if success:
            project_info = await resolve_api.get_project_info()
            return {
                "success": True,
                "message": f"Timeline '{request.name}' created successfully",
                "data": project_info
            }
        else:
            raise HTTPException(status_code=500, detail="Failed to create timeline")

    except ResolveAPIError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error creating timeline: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to create timeline: {str(e)}")

@router.post("/timeline/add-clips")
async def add_clips_to_timeline(request: TimelineAddClipsRequest):
    """添加片段到时间线"""
    try:
        if not resolve_api.timeline:
            raise HTTPException(status_code=400, detail="No timeline available")

        clips_info = [clip.dict() for clip in request.clips]
        result = await resolve_api.add_clips_to_timeline(clips_info)

        return {
            "success": True,
            "message": f"Added {result['added_count']} clips, {result['failed_count']} failed",
            "data": result
        }

    except ResolveAPIError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error adding clips to timeline: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to add clips: {str(e)}")

@router.post("/timeline/export")
async def export_timeline(request: ExportRequest):
    """导出时间线"""
    try:
        if not resolve_api.timeline:
            raise HTTPException(status_code=400, detail="No timeline available")

        export_settings = request.dict()
        result = await resolve_api.export_timeline(export_settings)

        return {
            "success": True,
            "message": "Export started successfully",
            "data": result
        }

    except ResolveAPIError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error exporting timeline: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to export timeline: {str(e)}")

@router.get("/render/status")
async def get_render_status(job_id: Optional[str] = None):
    """获取渲染状态"""
    try:
        if not resolve_api.is_project_loaded():
            raise HTTPException(status_code=400, detail="No project loaded")

        status = await resolve_api.get_render_status(job_id)

        return {
            "success": True,
            "data": status
        }

    except ResolveAPIError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error getting render status: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get render status: {str(e)}")

@router.get("/health")
async def davinci_health_check():
    """DaVinci Resolve健康检查"""
    try:
        health_status = {
            "api_available": RESOLVE_AVAILABLE,
            "connected": False,
            "project_loaded": False,
            "system_info": None
        }

        if RESOLVE_AVAILABLE and resolve_api.is_connected():
            health_status["connected"] = True
            health_status["project_loaded"] = resolve_api.is_project_loaded()

            try:
                health_status["system_info"] = await resolve_api.get_system_info()
            except Exception as e:
                logger.warning(f"Failed to get system info in health check: {e}")

        return {
            "success": True,
            "data": health_status
        }

    except Exception as e:
        logger.error(f"Error in DaVinci health check: {e}")
        return {
            "success": False,
            "error": str(e),
            "data": {
                "api_available": RESOLVE_AVAILABLE,
                "connected": False,
                "project_loaded": False
            }
        }

# ==================== AI集成端点 ====================

@router.post("/ai/import-media")
async def import_ai_media(request: AIMediaImportRequest):
    """导入AI生成的媒体文件"""
    try:
        if not resolve_api.is_project_loaded():
            raise HTTPException(status_code=400, detail="No project loaded")

        result = await resolve_api.import_ai_generated_media(
            request.file_paths,
            request.content_type
        )

        return {
            "success": True,
            "message": f"Imported {result['imported_count']} AI-generated media files",
            "data": result
        }

    except ResolveAPIError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error importing AI media: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to import AI media: {str(e)}")

@router.post("/ai/create-timeline")
async def create_ai_timeline(request: AITimelineCreateRequest):
    """基于AI分析创建时间线"""
    try:
        if not resolve_api.is_project_loaded():
            raise HTTPException(status_code=400, detail="No project loaded")

        success = await resolve_api.create_ai_timeline(
            request.timeline_name,
            request.ai_scenes
        )

        if success:
            timeline_info = await resolve_api.get_timeline_info()
            return {
                "success": True,
                "message": f"Created AI timeline: {request.timeline_name}",
                "data": {
                    "timeline_name": request.timeline_name,
                    "scenes_count": len(request.ai_scenes),
                    "timeline_info": timeline_info.__dict__ if timeline_info else None
                }
            }
        else:
            raise HTTPException(status_code=500, detail="Failed to create AI timeline")

    except ResolveAPIError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error creating AI timeline: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to create AI timeline: {str(e)}")

@router.get("/ai/status")
async def get_ai_integration_status():
    """获取AI集成状态"""
    try:
        if not resolve_api.is_connected():
            return {
                "success": False,
                "message": "Not connected to DaVinci Resolve",
                "data": {
                    "ai_folder_exists": False,
                    "ai_clips_count": 0,
                    "ai_timelines_count": 0
                }
            }

        system_info = await resolve_api.get_system_info()

        return {
            "success": True,
            "data": {
                "ai_folder_exists": system_info.get('ai_folder_exists', False),
                "ai_clips_count": system_info.get('media_pool_clips', 0),
                "connection_state": system_info.get('connection_state', 'unknown'),
                "api_version": system_info.get('api_version', 'unknown'),
                "current_project": system_info.get('current_project')
            }
        }

    except Exception as e:
        logger.error(f"Error getting AI integration status: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get AI status: {str(e)}")

# ==================== 静帧联动端点 ====================

@router.get("/playhead/info")
async def get_playhead_info():
    """获取当前播放头信息"""
    try:
        if not resolve_api.is_project_loaded():
            raise HTTPException(status_code=400, detail="No project loaded")

        playhead_info = await resolve_api.get_playhead_info()

        if playhead_info:
            return {
                "success": True,
                "data": {
                    "current_frame": playhead_info.current_frame,
                    "current_timecode": playhead_info.current_timecode,
                    "timeline_duration": playhead_info.timeline_duration,
                    "timeline_start": playhead_info.timeline_start,
                    "timeline_end": playhead_info.timeline_end
                }
            }
        else:
            raise HTTPException(status_code=404, detail="No timeline or playhead information available")

    except ResolveAPIError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error getting playhead info: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get playhead info: {str(e)}")

@router.post("/frame/export")
async def export_current_frame(request: FrameExportRequest):
    """导出当前播放头位置的静帧"""
    try:
        if not resolve_api.is_project_loaded():
            raise HTTPException(status_code=400, detail="No project loaded")

        # 更新导出设置
        if request.format:
            resolve_api._frame_export_format = request.format.upper()
        if request.quality:
            resolve_api._frame_export_quality = request.quality

        frame_info = await resolve_api.export_current_frame(
            request.output_dir,
            request.filename
        )

        if frame_info:
            return {
                "success": True,
                "message": f"Frame exported successfully",
                "data": {
                    "frame_number": frame_info.frame_number,
                    "timecode": frame_info.timecode,
                    "file_path": frame_info.file_path,
                    "width": frame_info.width,
                    "height": frame_info.height,
                    "format": frame_info.format
                }
            }
        else:
            raise HTTPException(status_code=500, detail="Failed to export frame")

    except ResolveAPIError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error exporting frame: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to export frame: {str(e)}")

@router.post("/frame/export-for-ai")
async def export_frame_for_ai(request: AIFrameRequest):
    """导出当前帧用于AI服务"""
    try:
        if not resolve_api.is_project_loaded():
            raise HTTPException(status_code=400, detail="No project loaded")

        file_path = await resolve_api.export_frame_for_ai(request.ai_service_type)

        if file_path:
            # 获取播放头信息用于响应
            playhead_info = await resolve_api.get_playhead_info()

            return {
                "success": True,
                "message": f"Frame exported for AI service: {request.ai_service_type}",
                "data": {
                    "file_path": file_path,
                    "ai_service_type": request.ai_service_type,
                    "frame_number": playhead_info.current_frame if playhead_info else None,
                    "timecode": playhead_info.current_timecode if playhead_info else None,
                    "ready_for_ai": True
                }
            }
        else:
            raise HTTPException(status_code=500, detail="Failed to export frame for AI")

    except ResolveAPIError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error exporting frame for AI: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to export frame for AI: {str(e)}")

@router.get("/frame/status")
async def get_frame_export_status():
    """获取静帧导出功能状态"""
    try:
        if not resolve_api.is_connected():
            return {
                "success": False,
                "message": "Not connected to DaVinci Resolve",
                "data": {
                    "frame_export_available": False,
                    "timeline_loaded": False
                }
            }

        timeline_loaded = resolve_api.timeline is not None
        playhead_info = await resolve_api.get_playhead_info() if timeline_loaded else None

        return {
            "success": True,
            "data": {
                "frame_export_available": timeline_loaded,
                "timeline_loaded": timeline_loaded,
                "current_frame": playhead_info.current_frame if playhead_info else None,
                "current_timecode": playhead_info.current_timecode if playhead_info else None,
                "export_format": resolve_api._frame_export_format,
                "export_quality": resolve_api._frame_export_quality
            }
        }

    except Exception as e:
        logger.error(f"Error getting frame export status: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get frame export status: {str(e)}")

# ==================== 深度集成端点 ====================

@router.post("/deep-integration/extract-audio")
async def extract_audio_for_ai(request: AudioExtractionRequest):
    """提取音频轨道用于AI分析 - 带备用方法"""
    try:
        if not resolve_api.is_project_loaded():
            raise HTTPException(status_code=400, detail="No project loaded")

        # 使用标准音频提取方法
        try:
            logger.info(f"🎵 Extracting audio for track {request.track_index}")
            audio_path = await resolve_api.extract_audio_for_ai(request.track_index)
        except Exception as e:
            logger.error(f"❌ Audio extraction failed: {e}")
            raise HTTPException(
                status_code=500,
                detail=f"Failed to extract audio: {str(e)}"
            )

        if audio_path:
            # 获取音频文件信息
            from pathlib import Path
            audio_file = Path(audio_path)
            file_size = audio_file.stat().st_size if audio_file.exists() else 0

            # 获取项目信息
            project_info = await resolve_api.get_project_info()

            logger.info(f"✅ Audio extraction successful: {audio_path} ({file_size} bytes)")

            return {
                "success": True,
                "message": f"Audio extracted from track {request.track_index}",
                "data": {
                    "audio_path": audio_path,
                    "filename": audio_file.name,
                    "file_size": file_size,
                    "format": "wav",
                    "track_index": request.track_index,
                    "project_name": project_info.name if project_info else "Unknown",
                    "ready_for_ai": True,
                    "timestamp": int(time.time())
                }
            }
        else:
            raise HTTPException(status_code=500, detail="Failed to extract audio with all methods")

    except ResolveAPIError as e:
        logger.error(f"❌ ResolveAPIError: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        logger.error(f"❌ Unexpected error extracting audio: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to extract audio: {str(e)}")

@router.post("/deep-integration/generate-subtitles")
async def generate_ai_subtitles(request: SubtitleGenerationRequest):
    """生成AI字幕"""
    try:
        if not resolve_api.is_project_loaded():
            raise HTTPException(status_code=400, detail="No project loaded")

        # 首先提取音频
        audio_path = await resolve_api.extract_audio_for_ai(request.track_index)

        if not audio_path:
            raise HTTPException(status_code=500, detail="Failed to extract audio for subtitle generation")

        # 这里应该调用AI服务进行语音识别
        # 暂时返回示例数据
        sample_subtitles = [
            {
                "text": "欢迎来到AI视频制作工作流演示",
                "start_time": 0.0,
                "end_time": 3.0,
                "track_index": request.track_index,
                "language": request.language
            },
            {
                "text": "我们将展示如何使用AI分析和生成内容",
                "start_time": 3.5,
                "end_time": 7.0,
                "track_index": request.track_index,
                "language": request.language
            }
        ]

        return {
            "success": True,
            "message": f"Generated {len(sample_subtitles)} subtitle entries",
            "data": {
                "subtitles": sample_subtitles,
                "audio_path": audio_path,
                "language": request.language,
                "ai_service": request.ai_service
            }
        }

    except ResolveAPIError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error generating subtitles: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to generate subtitles: {str(e)}")

@router.post("/deep-integration/analyze-content")
async def analyze_timeline_content(request: SmartMarkingRequest):
    """分析时间线内容并生成智能标记"""
    try:
        if not resolve_api.is_project_loaded():
            raise HTTPException(status_code=400, detail="No project loaded")

        analysis = await resolve_api.analyze_timeline_content()

        if request.auto_apply and analysis.get('suggested_markers'):
            # 自动应用标记
            markers_to_apply = [
                marker for marker in analysis['suggested_markers']
                if marker.confidence >= request.confidence_threshold
            ]

            if markers_to_apply:
                success = await resolve_api.add_smart_markers(markers_to_apply)
                analysis['markers_applied'] = len(markers_to_apply) if success else 0
            else:
                analysis['markers_applied'] = 0

        return {
            "success": True,
            "message": f"Analyzed timeline content",
            "data": {
                "analysis": analysis,
                "analysis_type": request.analysis_type,
                "confidence_threshold": request.confidence_threshold,
                "auto_applied": request.auto_apply
            }
        }

    except ResolveAPIError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error analyzing content: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to analyze content: {str(e)}")

@router.get("/deep-integration/capabilities")
async def get_deep_integration_capabilities():
    """获取深度集成功能能力"""
    try:
        if not resolve_api.is_connected():
            return {
                "success": False,
                "message": "Not connected to DaVinci Resolve",
                "data": {
                    "available_features": []
                }
            }

        capabilities = {
            "subtitle_generation": {
                "available": True,
                "supported_languages": ["zh-CN", "en-US", "ja-JP", "ko-KR"],
                "ai_services": ["deepseek", "openai", "azure"]
            },
            "audio_analysis": {
                "available": True,
                "supported_formats": ["WAV", "MP3", "AAC"],
                "analysis_types": ["transcription", "emotion", "quality"]
            },
            "smart_marking": {
                "available": True,
                "marker_types": ["content", "scene_change", "quality_issue", "person", "object"],
                "confidence_levels": [0.5, 0.7, 0.8, 0.9]
            },
            "frame_capture": {
                "available": True,
                "export_formats": ["PNG", "JPEG"],
                "ai_integration": ["image_generation", "video_generation"]
            }
        }

        # 检查项目状态
        project_loaded = resolve_api.is_project_loaded()
        timeline_available = resolve_api.timeline is not None

        return {
            "success": True,
            "data": {
                "capabilities": capabilities,
                "current_status": {
                    "project_loaded": project_loaded,
                    "timeline_available": timeline_available,
                    "api_version": resolve_api.get_api_version().value,
                    "connection_state": resolve_api.get_connection_state().value
                }
            }
        }

    except Exception as e:
        logger.error(f"Error getting capabilities: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get capabilities: {str(e)}")

@router.get("/render-formats")
async def get_render_formats():
    """获取可用的渲染格式"""
    try:
        if not RESOLVE_AVAILABLE:
            raise HTTPException(
                status_code=400,
                detail="DaVinci Resolve Python API not available"
            )

        if not resolve_api.is_connected():
            raise HTTPException(
                status_code=400,
                detail="Not connected to DaVinci Resolve"
            )

        if not resolve_api.is_project_loaded():
            raise HTTPException(
                status_code=400,
                detail="No project loaded"
            )

        # 获取可用格式
        formats = resolve_api.current_project.GetRenderFormats()
        return formats

    except Exception as e:
        logger.error(f"Error getting render formats: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get render formats: {str(e)}")

@router.get("/render-codecs")
async def get_render_codecs(format: str):
    """获取指定格式的可用编码器"""
    try:
        if not RESOLVE_AVAILABLE:
            raise HTTPException(
                status_code=400,
                detail="DaVinci Resolve Python API not available"
            )

        if not resolve_api.is_connected():
            raise HTTPException(
                status_code=400,
                detail="Not connected to DaVinci Resolve"
            )

        if not resolve_api.is_project_loaded():
            raise HTTPException(
                status_code=400,
                detail="No project loaded"
            )

        # 获取指定格式的编码器
        codecs = resolve_api.current_project.GetRenderCodecs(format)
        return codecs

    except Exception as e:
        logger.error(f"Error getting render codecs for {format}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get render codecs: {str(e)}")
