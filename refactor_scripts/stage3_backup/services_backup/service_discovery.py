"""
服务能力发现和管理
动态发现和管理AI服务的能力和特性
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Any, Set
from dataclasses import dataclass, field

from .base_types import AIServiceInterface, ServiceType, ServiceProvider

logger = logging.getLogger(__name__)

@dataclass
class ServiceProviderMetadata:
    """服务提供商元数据"""
    provider_name: str
    supported_capabilities: List[str]
    available_tools: List[str]
    connection_status: str
    metadata: Optional[Dict[str, Any]] = None

@dataclass
class ServiceCapability:
    """服务能力描述"""
    service_type: ServiceType
    provider: ServiceProvider
    supported_models: List[str] = field(default_factory=list)
    supported_languages: List[str] = field(default_factory=list)
    max_input_length: Optional[int] = None
    max_output_length: Optional[int] = None
    supported_formats: List[str] = field(default_factory=list)
    quality_levels: List[str] = field(default_factory=list)
    pricing_tier: str = "unknown"
    response_time_range: tuple = (0, 0)  # (min, max) in seconds
    reliability_score: float = 0.0
    features: Dict[str, Any] = field(default_factory=dict)
    last_updated: float = field(default_factory=time.time)

@dataclass
class ServiceCompatibility:
    """服务兼容性信息"""
    can_chain: bool = False  # 是否可以与其他服务链式调用
    input_types: Set[str] = field(default_factory=set)
    output_types: Set[str] = field(default_factory=set)
    dependencies: List[ServiceType] = field(default_factory=list)
    conflicts: List[ServiceProvider] = field(default_factory=list)

class ServiceDiscovery:
    """服务能力发现管理器"""

    def __init__(self):
        self.capabilities: Dict[str, ServiceCapability] = {}
        self.compatibility: Dict[str, ServiceCompatibility] = {}
        self.service_metadata: Dict[str, Dict[str, Any]] = {}
        self.discovery_cache: Dict[str, Any] = {}
        self.cache_ttl = 300  # 5分钟缓存
        self._discovery_tasks: Dict[str, asyncio.Task] = {}

    async def discover_service_capabilities(self, service: AIServiceInterface) -> ServiceCapability:
        """发现服务能力"""
        service_key = f"{str(service.provider)}"

        # 检查缓存
        if service_key in self.discovery_cache:
            cache_entry = self.discovery_cache[service_key]
            if time.time() - cache_entry['timestamp'] < self.cache_ttl:
                return cache_entry['capability']

        logger.info(f"Discovering capabilities for service: {service_key}")

        try:
            # 获取服务支持的服务类型
            supported_services = service.get_supported_services()

            # 为每个服务类型创建能力描述
            capabilities = []
            for service_type in supported_services:
                capability = await self._probe_service_capability(service, service_type)
                capabilities.append(capability)

                # 存储能力信息
                capability_key = f"{str(service.provider)}_{str(service_type)}"
                self.capabilities[capability_key] = capability

            # 发现服务兼容性
            compatibility = await self._discover_service_compatibility(service, supported_services)
            self.compatibility[service_key] = compatibility

            # 缓存结果
            primary_capability = capabilities[0] if capabilities else None
            if primary_capability:
                self.discovery_cache[service_key] = {
                    'capability': primary_capability,
                    'timestamp': time.time()
                }

            return primary_capability

        except Exception as e:
            logger.error(f"Failed to discover capabilities for {service_key}: {e}")
            # 返回基础能力信息
            return ServiceCapability(
                service_type=ServiceType("text_generation"),
                provider=service.provider,
                reliability_score=0.5
            )

    async def _probe_service_capability(self, service: AIServiceInterface, service_type: ServiceType) -> ServiceCapability:
        """探测特定服务类型的能力"""
        capability = ServiceCapability(
            service_type=service_type,
            provider=service.provider
        )

        try:
            # 获取服务统计信息
            stats = service.get_stats() if hasattr(service, 'get_stats') else {}
            capability.reliability_score = stats.get('success_rate', 0.5)

            # 根据服务提供商和类型设置特定能力
            provider_str = str(service.provider)
            if provider_str == "deepseek":
                capability = await self._probe_deepseek_capability(service, service_type, capability)
            elif provider_str == "minimax":
                capability = await self._probe_minimax_capability(service, service_type, capability)
            elif provider_str == "volcano":
                capability = await self._probe_volcano_capability(service, service_type, capability)

            # 通用能力探测
            await self._probe_common_capabilities(service, capability)

        except Exception as e:
            logger.warning(f"Failed to probe {str(service_type)} capability for {str(service.provider)}: {e}")

        return capability

    async def _probe_deepseek_capability(self, service: AIServiceInterface, service_type: ServiceType, capability: ServiceCapability) -> ServiceCapability:
        """探测DeepSeek服务能力"""
        if str(service_type) == "text_generation":
            capability.supported_models = ["deepseek-chat", "deepseek-coder"]
            capability.supported_languages = ["zh", "en", "ja", "ko"]
            capability.max_input_length = 32000
            capability.max_output_length = 4000
            capability.pricing_tier = "low"
            capability.response_time_range = (1, 5)
            capability.features = {
                "streaming": True,
                "function_calling": False,
                "code_generation": True,
                "multilingual": True
            }
        elif str(service_type) == "text_analysis":
            capability.supported_languages = ["zh", "en"]
            capability.max_input_length = 8000
            capability.features = {
                "scene_analysis": True,
                "keyword_extraction": True,
                "emotion_analysis": True
            }
        elif str(service_type) == "translation":
            capability.supported_languages = ["zh", "en", "ja", "ko", "fr", "de", "es", "ru"]
            capability.max_input_length = 10000
            capability.features = {
                "auto_detect": True,
                "context_aware": True,
                "professional_terms": True
            }

        return capability

    async def _probe_minimax_capability(self, service: AIServiceInterface, service_type: ServiceType, capability: ServiceCapability) -> ServiceCapability:
        """探测Minimax服务能力"""
        if str(service_type) == "text_generation":
            capability.supported_models = ["abab6.5s-chat", "abab6.5-chat"]
            capability.supported_languages = ["zh", "en"]
            capability.max_input_length = 16000
            capability.max_output_length = 2000
            capability.pricing_tier = "medium"
            capability.response_time_range = (2, 8)
            capability.features = {
                "streaming": True,
                "function_calling": True,
                "role_playing": True
            }
        elif str(service_type) == "speech_synthesis":
            capability.supported_languages = ["zh", "en"]
            capability.supported_formats = ["mp3", "wav"]
            capability.quality_levels = ["standard", "high"]
            capability.features = {
                "voice_cloning": False,
                "emotion_control": True,
                "speed_control": True,
                "pitch_control": True
            }
        elif str(service_type) == "video_generation":
            capability.supported_formats = ["mp4"]
            capability.max_output_length = 30  # seconds
            capability.quality_levels = ["720p", "1080p"]
            capability.response_time_range = (30, 300)
            capability.features = {
                "aspect_ratios": ["16:9", "9:16", "1:1"],
                "duration_control": True,
                "style_control": False
            }
        elif str(service_type) == "text_analysis":
            capability.supported_languages = ["zh", "en"]
            capability.max_input_length = 8000
            capability.features = {
                "scene_analysis": True,
                "content_structure": True,
                "sentiment_analysis": True
            }
        elif str(service_type) == "image_generation":
            capability.supported_formats = ["jpg", "png"]
            capability.quality_levels = ["512x512", "768x768", "1024x1024"]
            capability.response_time_range = (15, 90)
            capability.features = {
                "style_control": True,
                "negative_prompts": True,
                "batch_generation": True,
                "aspect_ratios": ["1:1", "16:9", "9:16", "4:3"]
            }
        elif str(service_type) == "translation":
            capability.supported_languages = ["zh", "en", "ja", "ko"]
            capability.max_input_length = 5000
            capability.features = {
                "auto_detect": True,
                "context_aware": True,
                "creative_translation": True
            }

        return capability

    async def _probe_volcano_capability(self, service: AIServiceInterface, service_type: ServiceType, capability: ServiceCapability) -> ServiceCapability:
        """探测火山引擎服务能力"""
        if str(service_type) == "text_generation":
            capability.supported_models = ["doubao-pro-4k", "doubao-lite-4k"]
            capability.supported_languages = ["zh", "en"]
            capability.max_input_length = 4000
            capability.max_output_length = 2000
            capability.pricing_tier = "low"
            capability.response_time_range = (1, 6)
            capability.features = {
                "streaming": True,
                "function_calling": False,
                "knowledge_base": True
            }
        elif str(service_type) == "speech_synthesis":
            capability.supported_languages = ["zh", "en"]
            capability.supported_formats = ["mp3", "wav"]
            capability.quality_levels = ["standard", "high", "premium"]
            capability.features = {
                "voice_cloning": True,
                "emotion_control": True,
                "multi_speaker": True
            }
        elif str(service_type) == "image_generation":
            capability.supported_formats = ["jpg", "png"]
            capability.quality_levels = ["512x512", "768x768", "1024x1024"]
            capability.response_time_range = (10, 60)
            capability.features = {
                "style_control": True,
                "negative_prompts": True,
                "batch_generation": False
            }
        elif str(service_type) == "translation":
            capability.supported_languages = ["zh", "en", "ja", "ko", "fr", "de", "es"]
            capability.max_input_length = 5000
            capability.features = {
                "auto_detect": True,
                "batch_translation": True,
                "domain_specific": False
            }
        elif str(service_type) == "text_analysis":
            capability.supported_languages = ["zh", "en"]
            capability.max_input_length = 10000
            capability.features = {
                "scene_analysis": True,
                "content_structure": True,
                "sentiment_analysis": True,
                "keyword_extraction": True
            }
        elif str(service_type) == "video_generation":
            capability.supported_formats = ["mp4"]
            capability.quality_levels = ["720p", "1080p"]
            capability.max_output_length = 10  # seconds
            capability.response_time_range = (60, 300)
            capability.features = {
                "aspect_ratios": ["16:9", "9:16", "1:1"],
                "duration_control": True,
                "style_control": True,
                "motion_control": True
            }

        return capability

    async def _probe_common_capabilities(self, service: AIServiceInterface, capability: ServiceCapability):
        """探测通用能力"""
        try:
            # 测试健康检查响应时间
            start_time = time.time()
            is_healthy = await service.health_check()
            response_time = time.time() - start_time

            if is_healthy:
                # 更新响应时间范围
                min_time, max_time = capability.response_time_range
                capability.response_time_range = (
                    min(min_time, response_time) if min_time > 0 else response_time,
                    max(max_time, response_time)
                )

        except Exception as e:
            logger.debug(f"Common capability probe failed: {e}")

    async def _discover_service_compatibility(self, service: AIServiceInterface, supported_services: List[ServiceType]) -> ServiceCompatibility:
        """发现服务兼容性"""
        compatibility = ServiceCompatibility()

        # 设置输入输出类型
        for service_type in supported_services:
            if str(service_type) == "text_generation":
                compatibility.input_types.add("text")
                compatibility.output_types.add("text")
            elif str(service_type) == "speech_synthesis":
                compatibility.input_types.add("text")
                compatibility.output_types.add("audio")
            elif str(service_type) == "video_generation":
                compatibility.input_types.add("text")
                compatibility.output_types.add("video")
            elif str(service_type) == "image_generation":
                compatibility.input_types.add("text")
                compatibility.output_types.add("image")
            elif str(service_type) == "translation":
                compatibility.input_types.add("text")
                compatibility.output_types.add("text")

        # 检查是否可以链式调用
        compatibility.can_chain = len(compatibility.input_types & compatibility.output_types) > 0

        return compatibility

    def get_service_capabilities(self, provider: ServiceProvider, service_type: ServiceType) -> Optional[ServiceCapability]:
        """获取服务能力"""
        capability_key = f"{str(provider)}_{str(service_type)}"
        return self.capabilities.get(capability_key)

    def get_compatible_services(self, output_type: str) -> List[ServiceCapability]:
        """获取兼容的服务"""
        compatible = []

        for capability in self.capabilities.values():
            service_key = f"{str(capability.provider)}"
            if service_key in self.compatibility:
                compatibility = self.compatibility[service_key]
                if output_type in compatibility.input_types:
                    compatible.append(capability)

        return compatible

    def find_best_service_for_task(self, service_type: ServiceType, requirements: Dict[str, Any]) -> Optional[ServiceCapability]:
        """为特定任务找到最佳服务"""
        candidates = []

        # 收集候选服务
        for capability in self.capabilities.values():
            if capability.service_type == service_type:
                score = self._calculate_task_fitness_score(capability, requirements)
                candidates.append((capability, score))

        if not candidates:
            return None

        # 按评分排序，返回最佳服务
        candidates.sort(key=lambda x: x[1], reverse=True)
        return candidates[0][0]

    def _calculate_task_fitness_score(self, capability: ServiceCapability, requirements: Dict[str, Any]) -> float:
        """计算任务适应性评分"""
        score = capability.reliability_score  # 基础评分

        # 语言要求
        required_language = requirements.get('language')
        if required_language and required_language in capability.supported_languages:
            score += 0.2

        # 输入长度要求
        input_length = requirements.get('input_length', 0)
        if capability.max_input_length and input_length <= capability.max_input_length:
            score += 0.1

        # 质量要求
        required_quality = requirements.get('quality')
        if required_quality and required_quality in capability.quality_levels:
            score += 0.1

        # 响应时间要求
        max_response_time = requirements.get('max_response_time')
        if max_response_time and capability.response_time_range[1] <= max_response_time:
            score += 0.1

        # 特性要求
        required_features = requirements.get('features', [])
        for feature in required_features:
            if feature in capability.features and capability.features[feature]:
                score += 0.05

        # 价格偏好
        price_preference = requirements.get('price_preference', 'medium')
        if capability.pricing_tier == price_preference:
            score += 0.1

        return min(score, 1.0)  # 最大评分1.0

    def get_service_discovery_stats(self) -> Dict[str, Any]:
        """获取服务发现统计信息"""
        return {
            'total_capabilities': len(self.capabilities),
            'total_services': len(set(cap.provider for cap in self.capabilities.values())),
            'service_types_covered': len(set(cap.service_type for cap in self.capabilities.values())),
            'cache_entries': len(self.discovery_cache),
            'compatibility_entries': len(self.compatibility),
            'capabilities_by_provider': {
                str(provider): len([cap for cap in self.capabilities.values() if str(cap.provider) == str(provider)])
                for provider in set(cap.provider for cap in self.capabilities.values())
            },
            'capabilities_by_type': {
                str(service_type): len([cap for cap in self.capabilities.values() if str(cap.service_type) == str(service_type)])
                for service_type in set(cap.service_type for cap in self.capabilities.values())
            }
        }

    async def refresh_all_capabilities(self, services: List[AIServiceInterface]):
        """刷新所有服务能力"""
        logger.info("Refreshing all service capabilities...")

        tasks = []
        for service in services:
            task = asyncio.create_task(self.discover_service_capabilities(service))
            tasks.append(task)

        try:
            await asyncio.gather(*tasks, return_exceptions=True)
            logger.info("Service capabilities refresh completed")
        except Exception as e:
            logger.error(f"Error during capabilities refresh: {e}")

    def clear_cache(self):
        """清除发现缓存"""
        self.discovery_cache.clear()
        logger.info("Service discovery cache cleared")
