"""
异步任务管理器
用于管理长时间运行的AI生成任务
"""

import asyncio
import uuid
import time
import logging
from typing import Dict, Any, Optional, List
from enum import Enum
from dataclasses import dataclass, field
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"      # 等待中
    RUNNING = "running"      # 运行中
    COMPLETED = "completed"  # 已完成
    FAILED = "failed"        # 失败
    CANCELLED = "cancelled"  # 已取消

@dataclass
class AsyncTask:
    """异步任务数据类"""
    task_id: str
    service_type: str
    provider: str
    prompt: str
    parameters: Dict[str, Any]
    status: TaskStatus = TaskStatus.PENDING
    created_at: datetime = field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    progress: float = 0.0
    metadata: Dict[str, Any] = field(default_factory=dict)

class AsyncTaskManager:
    """异步任务管理器"""

    def __init__(self, max_concurrent_tasks: int = 5, task_timeout: int = 3600):
        self.max_concurrent_tasks = max_concurrent_tasks
        self.task_timeout = task_timeout  # 任务超时时间（秒）
        self.tasks: Dict[str, AsyncTask] = {}
        self.running_tasks: Dict[str, asyncio.Task] = {}
        self._cleanup_interval = 300  # 清理间隔（秒）
        self._last_cleanup = time.time()

    def create_task(
        self,
        service_type: str,
        provider: str,
        prompt: str,
        parameters: Dict[str, Any],
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """创建新任务"""
        task_id = str(uuid.uuid4())

        task = AsyncTask(
            task_id=task_id,
            service_type=service_type,
            provider=provider,
            prompt=prompt,
            parameters=parameters,
            metadata=metadata or {}
        )

        self.tasks[task_id] = task
        logger.info(f"Created async task {task_id} for {service_type}")

        return task_id

    async def start_task(self, task_id: str, task_func, *args, **kwargs) -> bool:
        """启动任务"""
        if task_id not in self.tasks:
            logger.error(f"Task {task_id} not found")
            return False

        task = self.tasks[task_id]

        # 检查并发任务数量
        if len(self.running_tasks) >= self.max_concurrent_tasks:
            logger.warning(f"Max concurrent tasks reached, task {task_id} remains pending")
            return False

        # 更新任务状态
        task.status = TaskStatus.RUNNING
        task.started_at = datetime.now()

        # 创建异步任务
        async_task = asyncio.create_task(self._run_task(task_id, task_func, *args, **kwargs))
        self.running_tasks[task_id] = async_task

        logger.info(f"Started async task {task_id}")
        return True

    async def _run_task(self, task_id: str, task_func, *args, **kwargs):
        """运行任务的内部方法"""
        task = self.tasks[task_id]

        try:
            # 添加进度回调
            progress_callback = lambda p: setattr(task, 'progress', p)

            # 执行任务，检查是否支持进度回调
            if asyncio.iscoroutinefunction(task_func):
                # 检查函数是否接受progress_callback参数
                import inspect
                sig = inspect.signature(task_func)
                if 'progress_callback' in sig.parameters:
                    result = await asyncio.wait_for(
                        task_func(*args, progress_callback=progress_callback, **kwargs),
                        timeout=self.task_timeout
                    )
                else:
                    result = await asyncio.wait_for(
                        task_func(*args, **kwargs),
                        timeout=self.task_timeout
                    )
            else:
                # 对于同步函数，在线程池中执行
                loop = asyncio.get_event_loop()
                result = await asyncio.wait_for(
                    loop.run_in_executor(None, task_func, *args, **kwargs),
                    timeout=self.task_timeout
                )

            # 更新任务结果
            task.status = TaskStatus.COMPLETED
            task.completed_at = datetime.now()
            task.result = result
            task.progress = 1.0

            logger.info(f"Task {task_id} completed successfully")

        except asyncio.TimeoutError:
            task.status = TaskStatus.FAILED
            task.completed_at = datetime.now()
            task.error = "Task timeout"
            logger.error(f"Task {task_id} timed out")

        except asyncio.CancelledError:
            task.status = TaskStatus.CANCELLED
            task.completed_at = datetime.now()
            task.error = "Task cancelled"
            logger.info(f"Task {task_id} was cancelled")

        except Exception as e:
            task.status = TaskStatus.FAILED
            task.completed_at = datetime.now()
            task.error = str(e)
            logger.error(f"Task {task_id} failed: {e}", exc_info=True)

        finally:
            # 清理运行中的任务
            if task_id in self.running_tasks:
                del self.running_tasks[task_id]

    def get_task(self, task_id: str) -> Optional[AsyncTask]:
        """获取任务信息"""
        return self.tasks.get(task_id)

    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务状态"""
        task = self.get_task(task_id)
        if not task:
            return None

        return {
            "task_id": task.task_id,
            "status": task.status.value,
            "service_type": task.service_type,
            "provider": task.provider,
            "created_at": task.created_at.isoformat(),
            "started_at": task.started_at.isoformat() if task.started_at else None,
            "completed_at": task.completed_at.isoformat() if task.completed_at else None,
            "progress": task.progress,
            "result": task.result,
            "error": task.error,
            "metadata": task.metadata
        }

    def list_tasks(
        self,
        status: Optional[TaskStatus] = None,
        service_type: Optional[str] = None,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """列出任务"""
        tasks = list(self.tasks.values())

        # 过滤条件
        if status:
            tasks = [t for t in tasks if t.status == status]
        if service_type:
            tasks = [t for t in tasks if t.service_type == service_type]

        # 按创建时间倒序排序
        tasks.sort(key=lambda t: t.created_at, reverse=True)

        # 限制数量
        tasks = tasks[:limit]

        return [self.get_task_status(t.task_id) for t in tasks]

    def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        if task_id not in self.tasks:
            return False

        task = self.tasks[task_id]

        # 如果任务正在运行，取消异步任务
        if task_id in self.running_tasks:
            async_task = self.running_tasks[task_id]
            async_task.cancel()
            del self.running_tasks[task_id]

        # 更新任务状态
        task.status = TaskStatus.CANCELLED
        task.completed_at = datetime.now()

        logger.info(f"Cancelled task {task_id}")
        return True

    def cleanup_old_tasks(self, max_age_hours: int = 24):
        """清理旧任务"""
        current_time = time.time()

        # 检查是否需要清理
        if current_time - self._last_cleanup < self._cleanup_interval:
            return

        cutoff_time = datetime.now() - timedelta(hours=max_age_hours)
        tasks_to_remove = []

        for task_id, task in self.tasks.items():
            # 跳过正在运行的任务
            if task.status == TaskStatus.RUNNING:
                continue

            # 检查任务年龄
            if task.created_at < cutoff_time:
                tasks_to_remove.append(task_id)

        # 删除旧任务
        for task_id in tasks_to_remove:
            del self.tasks[task_id]
            logger.info(f"Cleaned up old task {task_id}")

        self._last_cleanup = current_time

        if tasks_to_remove:
            logger.info(f"Cleaned up {len(tasks_to_remove)} old tasks")

    def get_stats(self) -> Dict[str, Any]:
        """获取任务统计信息"""
        total_tasks = len(self.tasks)
        running_tasks = len(self.running_tasks)

        status_counts = {}
        for status in TaskStatus:
            status_counts[status.value] = sum(
                1 for task in self.tasks.values() if task.status == status
            )

        return {
            "total_tasks": total_tasks,
            "running_tasks": running_tasks,
            "max_concurrent_tasks": self.max_concurrent_tasks,
            "status_counts": status_counts,
            "task_timeout": self.task_timeout
        }

# 全局任务管理器实例
task_manager = AsyncTaskManager()
