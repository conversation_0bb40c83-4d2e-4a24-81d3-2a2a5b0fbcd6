"""
AI服务模块
提供各种AI服务的统一接口
"""

# 导入基础类型（避免循环导入）
from .base_types import ServiceType, AIRequest, AIResponse

# 导入简化的组件
try:
    from .simplified_types import ServiceRequest, ServiceCapability, ServiceProvider
    from .simplified_ai_services import simplified_ai_service_manager
    from .deepseek import deepseek_service
    SIMPLIFIED_ARCHITECTURE_AVAILABLE = True
except ImportError as e:
    print(f"Warning: Simplified architecture not available: {e}")
    SIMPLIFIED_ARCHITECTURE_AVAILABLE = False
    simplified_ai_service_manager = None
    deepseek_service = None

# 向后兼容的导入
try:
    from .enhanced_ai_services import enhanced_ai_service_manager
    ENHANCED_SERVICES_AVAILABLE = True
except ImportError as e:
    print(f"Warning: Enhanced services not available: {e}")
    ENHANCED_SERVICES_AVAILABLE = False
    enhanced_ai_service_manager = None

__all__ = [
    'ServiceType',
    'AIRequest',
    'AIResponse',
    'simplified_ai_service_manager',
    'enhanced_ai_service_manager',
    'deepseek_service',
    'SIMPLIFIED_ARCHITECTURE_AVAILABLE',
    'ENHANCED_SERVICES_AVAILABLE'
]

# 条件性添加简化架构组件
if SIMPLIFIED_ARCHITECTURE_AVAILABLE:
    __all__.extend(['ServiceRequest', 'ServiceCapability', 'ServiceProvider'])
