"""
直接服务适配器 - 简化的3层架构
Frontend → DirectServiceAdapter → MCP → Frontend
移除复杂的配置映射，使用代码直接处理
"""
import time
import logging
from typing import Dict, Any, Optional

from .simplified_types import (
    ServiceRequest, ServiceResponse, ServiceCapability, ServiceProvider,
    get_mcp_tool_name, is_capability_supported
)
from .fastmcp_server import fastmcp_manager
from .deepseek import deepseek_service

logger = logging.getLogger(__name__)

class DirectServiceAdapter:
    """直接服务适配器 - 简化的MCP调用处理"""

    def __init__(self):
        self.mcp_manager = fastmcp_manager
        self._initialized = False

    async def _ensure_initialized(self):
        """确保MCP管理器已初始化"""
        if not self._initialized:
            logger.info("🚀 [DIRECT_ADAPTER] Initializing MCP manager...")
            try:
                await self.mcp_manager.initialize()
                self._initialized = True
                logger.info(f"✅ [DIRECT_ADAPTER] MCP manager initialized with {len(self.mcp_manager.clients)} clients")
            except Exception as e:
                logger.error(f"❌ [DIRECT_ADAPTER] Failed to initialize MCP manager: {e}")
                raise

    async def execute_request(self, request: ServiceRequest) -> ServiceResponse:
        """执行服务请求 - 简化的3层处理"""
        start_time = time.time()

        try:
            # 特殊处理：DeepSeek使用直接API调用而不是MCP
            if request.provider == ServiceProvider.DEEPSEEK:
                logger.info(f"🔄 [DIRECT_ADAPTER] Using direct DeepSeek API for {request.capability.value}")
                return await deepseek_service.process_request(request)

            # 其他服务使用MCP
            # 0. 确保MCP管理器已初始化
            await self._ensure_initialized()

            # 1. 验证请求
            if not is_capability_supported(request.provider, request.capability):
                return ServiceResponse.error_response(
                    error=f"Provider {request.provider.value} does not support {request.capability.value}",
                    provider=request.provider,
                    capability=request.capability,
                    processing_time=time.time() - start_time
                )

            # 2. 获取MCP工具名称
            tool_name = get_mcp_tool_name(request.provider, request.capability)
            if not tool_name:
                return ServiceResponse.error_response(
                    error=f"No MCP tool mapping found for {request.provider.value}.{request.capability.value}",
                    provider=request.provider,
                    capability=request.capability,
                    processing_time=time.time() - start_time
                )

            # 3. 准备MCP参数
            mcp_params = self._prepare_mcp_parameters(request)

            # 4. 调用MCP服务
            logger.info(f"🔄 [DIRECT_ADAPTER] Calling {request.provider.value}.{tool_name}")
            mcp_result = await self.mcp_manager.call_tool(
                server_name=request.provider.value,
                tool_name=tool_name,
                parameters=mcp_params,
                timeout=60
            )

            # 5. 处理响应
            if mcp_result is None:
                return ServiceResponse.error_response(
                    error="MCP call returned None - service may not be available",
                    provider=request.provider,
                    capability=request.capability,
                    processing_time=time.time() - start_time
                )

            if mcp_result.is_error:
                error_msg = str(mcp_result.content[0].text) if mcp_result.content else "Unknown MCP error"
                return ServiceResponse.error_response(
                    error=f"MCP call failed: {error_msg}",
                    provider=request.provider,
                    capability=request.capability,
                    processing_time=time.time() - start_time
                )

            # 6. 提取并格式化结果
            response_data = self._extract_response_data(request, mcp_result)

            processing_time = time.time() - start_time
            logger.info(f"✅ [DIRECT_ADAPTER] {request.provider.value}.{request.capability.value} completed in {processing_time:.2f}s")

            return ServiceResponse.success_response(
                data=response_data,
                provider=request.provider,
                capability=request.capability,
                processing_time=processing_time
            )

        except Exception as e:
            logger.error(f"❌ [DIRECT_ADAPTER] Error processing request: {e}")
            return ServiceResponse.error_response(
                error=str(e),
                provider=request.provider,
                capability=request.capability,
                processing_time=time.time() - start_time
            )

    def _prepare_mcp_parameters(self, request: ServiceRequest) -> Dict[str, Any]:
        """准备MCP参数 - 简化的直接映射"""
        base_params = request.parameters.copy()

        # 根据服务商和能力进行特殊处理
        if request.provider == ServiceProvider.DEEPSEEK:
            return self._prepare_deepseek_params(request, base_params)
        elif request.provider == ServiceProvider.MINIMAX:
            return self._prepare_minimax_params(request, base_params)
        elif request.provider == ServiceProvider.ELEVENLABS:
            return self._prepare_elevenlabs_params(request, base_params)
        elif request.provider == ServiceProvider.DOUBAO:
            return self._prepare_doubao_params(request, base_params)
        elif request.provider == ServiceProvider.VIDU:
            return self._prepare_vidu_params(request, base_params)
        else:
            # 默认处理
            return {
                "prompt": request.content,
                **base_params
            }

    def _prepare_deepseek_params(self, request: ServiceRequest, params: Dict[str, Any]) -> Dict[str, Any]:
        """DeepSeek参数准备"""
        if request.capability == ServiceCapability.TRANSLATION:
            # 翻译功能特殊处理
            source_lang = params.get('source_language', 'auto')
            target_lang = params.get('target_language', 'en')

            if source_lang == 'auto':
                translation_prompt = f"""请将以下文本翻译成{target_lang}语言，保持原文的语气和风格：

{request.content}

请直接返回翻译结果，不需要额外说明。"""
            else:
                translation_prompt = f"""请将以下{source_lang}文本翻译成{target_lang}语言：

{request.content}

请直接返回翻译结果，不需要额外说明。"""

            return {
                "messages": [{"role": "user", "content": translation_prompt}],
                "model": "deepseek-chat",
                "max_tokens": params.get('max_tokens', 1000),
                "temperature": params.get('temperature', 0.3)
            }

        elif request.capability == ServiceCapability.TEXT_ANALYSIS:
            # 文本分析/提示词优化
            task = params.get('task', 'content_analysis')
            if task == 'prompt_enhancement':
                target_type = params.get('target_type', 'general')
                enhancement_prompt = self._get_enhancement_prompt(request.content, target_type)
            else:
                enhancement_prompt = f"请分析以下文本内容：\n\n{request.content}"

            return {
                "messages": [{"role": "user", "content": enhancement_prompt}],
                "model": "deepseek-chat",
                "max_tokens": params.get('max_tokens', 2000),
                "temperature": params.get('temperature', 0.7)
            }

        else:
            # 默认文本生成
            return {
                "messages": [{"role": "user", "content": request.content}],
                "model": "deepseek-chat",
                "max_tokens": params.get('max_tokens', 2000),
                "temperature": params.get('temperature', 0.7)
            }

    def _prepare_minimax_params(self, request: ServiceRequest, params: Dict[str, Any]) -> Dict[str, Any]:
        """MiniMax参数准备"""
        if request.capability == ServiceCapability.SPEECH_SYNTHESIS:
            return {
                "text": request.content,
                "voice_id": params.get('voice_id', 'male-qn-qingse'),
                **{k: v for k, v in params.items() if k not in ['voice_id']}
            }
        else:
            return {
                "prompt": request.content,
                **params
            }

    def _prepare_elevenlabs_params(self, request: ServiceRequest, params: Dict[str, Any]) -> Dict[str, Any]:
        """ElevenLabs参数准备"""
        return {
            "text": request.content,
            **params
        }

    def _prepare_doubao_params(self, request: ServiceRequest, params: Dict[str, Any]) -> Dict[str, Any]:
        """Doubao参数准备"""
        return {
            "prompt": request.content,
            **params
        }

    def _prepare_vidu_params(self, request: ServiceRequest, params: Dict[str, Any]) -> Dict[str, Any]:
        """Vidu参数准备"""
        return {
            "prompt": request.content,
            **params
        }

    def _extract_response_data(self, request: ServiceRequest, mcp_result) -> Dict[str, Any]:
        """提取响应数据 - 简化的直接提取"""
        # 从CallToolResult对象中提取文本内容
        if hasattr(mcp_result, 'content') and mcp_result.content:
            text_content = mcp_result.content[0].text if mcp_result.content[0].text else ""
        else:
            text_content = ""

        # 根据能力类型返回适当的字段
        if request.capability == ServiceCapability.TEXT_GENERATION:
            return {"generated_text": text_content}

        elif request.capability == ServiceCapability.TRANSLATION:
            return {"translated_text": text_content}

        elif request.capability == ServiceCapability.TEXT_ANALYSIS:
            task = request.parameters.get('task', 'content_analysis')
            if task == 'prompt_enhancement':
                return {"enhanced_prompt": text_content}
            else:
                return {"analysis_result": text_content}

        elif request.capability == ServiceCapability.SPEECH_SYNTHESIS:
            return {"audio_url": text_content}

        elif request.capability in [ServiceCapability.IMAGE_GENERATION, ServiceCapability.VIDEO_GENERATION]:
            return {"media_url": text_content}

        else:
            # 默认返回原始数据
            return {"result": text_content}

    def _extract_text_content(self, result_data: Dict[str, Any]) -> str:
        """提取文本内容"""
        # 尝试多种可能的字段名
        for field in ['text', 'content', 'message', 'response']:
            if field in result_data:
                return str(result_data[field])

        # 如果没有找到，返回整个结果的字符串表示
        return str(result_data)

    def _get_enhancement_prompt(self, original_prompt: str, target_type: str) -> str:
        """获取提示词优化的提示"""
        if target_type == "image_generation":
            return f"""请将以下简单的图像描述优化为详细的AI绘画提示词，包含具体的视觉细节、风格、构图、光线等元素：

原始提示词：{original_prompt}

请返回优化后的详细提示词，适合用于AI图像生成。"""

        elif target_type == "video_generation":
            return f"""请将以下视频描述优化为详细的AI视频生成提示词，包含动作、场景、镜头运动、时长等元素：

原始提示词：{original_prompt}

请返回优化后的详细提示词，适合用于AI视频生成。"""

        else:
            return f"""请优化以下提示词，使其更加清晰、具体和有效：

原始提示词：{original_prompt}

请返回优化后的提示词。"""

# 全局实例
direct_service_adapter = DirectServiceAdapter()
