"""
增强的MCP服务适配器
支持动态服务提供商和标准化的MCP服务集成
"""

import logging
import time
from typing import Dict, List, Any

from .fastmcp_server import fastmcp_manager as mcp_manager
from .dynamic_service_provider import dynamic_provider_manager
from ..core import APIError, ErrorCode, get_config
from .universal_result_parser import universal_result_parser
from .base_types import ServiceType, AIRequest, AIResponse, AIServiceInterface
from .complete_dynamic_type_manager import complete_dynamic_type_manager

logger = logging.getLogger(__name__)

class EnhancedMCPServiceAdapter(AIServiceInterface):
    """增强的MCP服务适配器"""

    def __init__(self, provider_name: str, server_name: str):
        super().__init__(provider_name, server_name)
        self._initialized = False

        # 使用统一的结果解析器
        self.result_parser = universal_result_parser

        # 从动态提供商管理器获取能力映射
        self.provider_metadata = dynamic_provider_manager.get_provider(provider_name)
        if not self.provider_metadata:
            logger.warning(f"No provider metadata found for {provider_name}, using default empty metadata")
            # 创建默认的元数据以避免None错误
            from .dynamic_service_provider import ServiceProviderMetadata
            self.provider_metadata = ServiceProviderMetadata(
                name=provider_name,
                display_name=provider_name.title(),
                description=f"Default metadata for {provider_name}",
                supported_capabilities=[],
                supported_tools={}
            )

        # 动态服务能力映射
        self.capability_mapping = self._build_capability_mapping()

    def _build_capability_mapping(self) -> Dict[str, str]:
        """构建动态能力映射"""
        mapping = {}
        try:
            service_types = complete_dynamic_type_manager.get_all_service_types()
            for service_type in service_types:
                # 直接使用字符串映射
                mapping[service_type.name] = service_type.name
        except Exception as e:
            logger.warning(f"构建动态能力映射失败，请修复！")
        return mapping

    async def initialize(self) -> bool:
        """初始化服务"""
        if self._initialized:
            return self.is_available

        try:
            # 确保MCP管理器已初始化
            if not mcp_manager._initialized:
                await mcp_manager.initialize()

            # 检查服务器是否可用
            available_servers = mcp_manager.get_available_servers()
            if self.server_name not in available_servers:
                logger.warning(f"MCP server '{self.server_name}' is not available")
                self.is_available = False
                return False

            # 获取服务器能力
            capabilities = mcp_manager.get_server_capabilities(self.server_name)
            tools = mcp_manager.get_server_tools(self.server_name)

            # 使用动态提供商管理器中的元数据，而不是创建新的
            # 这样可以确保我们有完整的工具映射信息
            self.provider_metadata = dynamic_provider_manager.get_provider(self.provider_name)
            if not self.provider_metadata:
                logger.warning(f"No provider metadata found for {self.provider_name}")
                self.is_available = False
                return False

            logger.info(f"✅ Enhanced MCP service '{self.provider_name}/{self.server_name}' initialized")
            logger.info(f"   Capabilities: {capabilities}")
            logger.info(f"   Tools: {tools}")

            self.is_available = True
            self._initialized = True
            return True

        except Exception as e:
            logger.error(f"❌ Failed to initialize Enhanced MCP service '{self.provider_name}/{self.server_name}': {e}")
            self.is_available = False
            return False

    async def health_check(self) -> bool:
        """健康检查"""
        try:
            if not self._initialized:
                await self.initialize()

            # 检查服务器是否在可用列表中
            available_servers = mcp_manager.get_available_servers()
            is_available = self.server_name in available_servers

            # 更新提供商实例状态
            instance = dynamic_provider_manager.get_instance(self.provider_name, self.server_name)
            if instance:
                instance.is_active = is_available
                instance.last_health_check = time.time()

            return is_available

        except Exception as e:
            logger.error(f"Enhanced MCP service '{self.provider_name}/{self.server_name}' health check failed: {e}")
            return False

    def get_supported_services(self) -> List[ServiceType]:
        """获取支持的服务类型 - 已弃用，现在从配置文件读取"""
        # 这个方法现在不再被使用，服务能力从配置文件读取
        # 保留此方法仅为向后兼容
        logger.debug(f"⚠️ get_supported_services() is deprecated for {self.provider_name}/{self.server_name}")
        return []

    async def process_request(self, request: AIRequest) -> AIResponse:
        """处理AI请求"""
        logger.debug(f"🔍 Processing request for {self.provider_name}/{self.server_name}")
        logger.debug(f"   Service type: {request.service_type}")
        logger.debug(f"   Is available: {self.is_available}")
        logger.debug(f"   Is initialized: {self._initialized}")

        # 如果服务不可用，尝试重新初始化
        if not self.is_available:
            logger.info(f"🔄 Service {self.provider_name}/{self.server_name} not available, attempting re-initialization...")
            try:
                if await self.initialize():
                    logger.info(f"✅ Service {self.provider_name}/{self.server_name} re-initialized successfully")
                else:
                    logger.warning(f"⚠️ Service {self.provider_name}/{self.server_name} re-initialization failed")
                    raise APIError("Enhanced MCP service is not available", ErrorCode.SERVICE_UNAVAILABLE)
            except Exception as e:
                logger.error(f"❌ Service {self.provider_name}/{self.server_name} re-initialization error: {e}")
                raise APIError("Enhanced MCP service is not available", ErrorCode.SERVICE_UNAVAILABLE)

        # 根据服务类型路由到相应的处理方法
        service_type_str = str(request.service_type)

        if service_type_str == 'speech_synthesis':
            return await self._synthesize_speech(request)
        elif service_type_str == 'image_generation':
            return await self._generate_image(request)
        elif service_type_str == 'video_generation':
            return await self._generate_video(request)
        elif service_type_str in ['text_generation', 'chat']:
            return await self._generate_text(request)
        elif service_type_str == 'text_analysis':
            return await self._analyze_text(request)
        elif service_type_str == 'translation':
            return await self._translate_text(request)
        elif service_type_str == 'voice_cloning':
            return await self._clone_voice(request)
        elif service_type_str == 'utility':
            return await self._handle_utility(request)
        else:
            raise APIError(f"Unsupported service type: {request.service_type}", ErrorCode.INVALID_REQUEST)

    async def _synthesize_speech(self, request: AIRequest) -> AIResponse:
        """语音合成 - 统一处理不同提供商"""
        try:
            # 根据提供商选择合适的工具
            if self.provider_name == 'elevenlabs':
                tool_name = 'text_to_speech'
            elif self.provider_name == 'minimax':
                tool_name = 'text_to_audio'
            else:
                # 从提供商元数据中查找合适的工具
                tool_name = self._find_tool_for_capability('speech_synthesis')
                if not tool_name:
                    raise APIError(f"No speech synthesis tool found for provider {self.provider_name}", ErrorCode.SERVICE_UNAVAILABLE)

            # 准备工具参数
            tool_params = self._prepare_speech_params(request)

            # 调用MCP工具
            result = await mcp_manager.call_tool(self.server_name, tool_name, tool_params)

            # 使用统一解析器处理结果
            parsed_result = self.result_parser.parse_result(
                self._extract_content_text(result),
                self.provider_name,
                "speech_synthesis",
                tool_params
            )

            # 转换为AIResponse格式
            return self._convert_parsed_result_to_ai_response(parsed_result, str(request.service_type))

        except Exception as e:
            logger.error(f"Speech synthesis failed for {self.provider_name}: {e}")
            raise APIError(f"Speech synthesis failed: {str(e)}", ErrorCode.API_SERVER_ERROR)

    async def _generate_image(self, request: AIRequest) -> AIResponse:
        """图像生成"""
        try:
            tool_name = self._find_tool_for_capability('image_generation')
            if not tool_name:
                raise APIError(f"No image generation tool found for provider {self.provider_name}", ErrorCode.SERVICE_UNAVAILABLE)

            tool_params = self._prepare_image_params(request)
            result = await mcp_manager.call_tool(self.server_name, tool_name, tool_params)

            # 使用统一解析器处理结果
            parsed_result = self.result_parser.parse_result(
                self._extract_content_text(result),
                self.provider_name,
                "image_generation",
                tool_params
            )

            # 转换为AIResponse格式
            return self._convert_parsed_result_to_ai_response(parsed_result, str(request.service_type))

        except Exception as e:
            logger.error(f"Image generation failed for {self.provider_name}: {e}")
            raise APIError(f"Image generation failed: {str(e)}", ErrorCode.API_SERVER_ERROR)

    async def _generate_video(self, request: AIRequest) -> AIResponse:
        """视频生成"""
        try:
            tool_name = self._find_tool_for_capability('video_generation')
            if not tool_name:
                raise APIError(f"No video generation tool found for provider {self.provider_name}", ErrorCode.SERVICE_UNAVAILABLE)

            tool_params = self._prepare_video_params(request)
            result = await mcp_manager.call_tool(self.server_name, tool_name, tool_params)

            # 使用统一解析器处理结果
            parsed_result = self.result_parser.parse_result(
                self._extract_content_text(result),
                self.provider_name,
                "video_generation",
                tool_params
            )

            # 转换为AIResponse格式
            return self._convert_parsed_result_to_ai_response(parsed_result, str(request.service_type))

        except Exception as e:
            logger.error(f"Video generation failed for {self.provider_name}: {e}")
            raise APIError(f"Video generation failed: {str(e)}", ErrorCode.API_SERVER_ERROR)

    async def _generate_text(self, request: AIRequest) -> AIResponse:
        """文本生成/对话"""
        try:
            # 根据请求类型选择工具
            if str(request.service_type) == 'chat':
                tool_name = self._find_tool_for_capability('chat')
            else:
                tool_name = self._find_tool_for_capability('text_generation')

            if not tool_name:
                raise APIError(f"No text generation tool found for provider {self.provider_name}", ErrorCode.SERVICE_UNAVAILABLE)

            tool_params = self._prepare_text_params(request)
            logger.debug(f"🔧 DeepSeek tool_params: {tool_params}")
            result = await mcp_manager.call_tool(self.server_name, tool_name, tool_params)
            logger.debug(f"🔧 DeepSeek raw result: {result}")
            logger.debug(f"🔧 DeepSeek result type: {type(result)}")
            if hasattr(result, 'content'):
                logger.debug(f"🔧 DeepSeek result.content: {result.content}")
                logger.debug(f"🔧 DeepSeek result.content type: {type(result.content)}")
                if result.content:
                    for i, content in enumerate(result.content):
                        logger.debug(f"🔧 DeepSeek content[{i}]: {content}")
                        logger.debug(f"🔧 DeepSeek content[{i}] type: {type(content)}")
                        if hasattr(content, 'text'):
                            logger.debug(f"🔧 DeepSeek content[{i}].text: {content.text}")
            else:
                logger.debug(f"🔧 DeepSeek result has no content attribute")

            # 使用统一解析器处理结果
            parsed_result = self.result_parser.parse_result(
                self._extract_content_text(result),
                self.provider_name,
                "text_generation",
                tool_params
            )

            # 转换为AIResponse格式
            return self._convert_parsed_result_to_ai_response(parsed_result, str(request.service_type))

        except Exception as e:
            logger.error(f"Text generation failed for {self.provider_name}: {e}")
            raise APIError(f"Text generation failed: {str(e)}", ErrorCode.API_SERVER_ERROR)

    async def _analyze_text(self, request: AIRequest) -> AIResponse:
        """文本分析 - 使用DeepSeek的chat_completion工具"""
        try:
            # 对于DeepSeek，使用chat_completion工具进行文本分析
            if self.provider_name == 'deepseek':
                tool_name = 'chat_completion'

                # 根据任务类型构建分析提示词
                task = request.parameters.get('task', 'content_analysis')

                if task == 'prompt_enhancement':
                    # 提示词优化
                    target_type = request.parameters.get('target_type', 'general')
                    enhancement_style = request.parameters.get('enhancement_style', 'detailed')

                    # 根据目标类型生成不同的优化提示词
                    if target_type == 'image' or target_type == 'image_generation':
                        analysis_prompt = f"""请优化以下图像生成提示词，使其更加详细、准确和有效。

原始提示词：
{request.prompt}

优化要求：
- 目标：图像生成AI模型（如Stable Diffusion、DALL-E等）
- 风格：{enhancement_style}
- 添加具体的视觉描述：颜色、光线、构图、风格、质量等
- 使用英文关键词和专业术语
- 控制长度在200字符以内，避免过长
- 格式：主体描述 + 风格修饰 + 质量标签

请直接输出优化后的提示词："""
                    elif target_type == 'video' or target_type == 'video_generation':
                        analysis_prompt = f"""请优化以下视频生成提示词，使其更加详细、准确和有效。

原始提示词：
{request.prompt}

优化要求：
- 目标：视频生成AI模型
- 风格：{enhancement_style}
- 添加动作描述：运动方式、镜头运动、时间流程
- 描述场景环境：背景、光线、氛围
- 控制长度在150字符以内
- 避免过于复杂的描述

请直接输出优化后的提示词："""
                    elif target_type == 'speech' or target_type == 'speech_synthesis':
                        analysis_prompt = f"""请优化以下语音合成文本，使其更加自然、流畅。

原始文本：
{request.prompt}

优化要求：
- 目标：语音合成系统
- 风格：{enhancement_style}
- 调整语言表达，使其更适合口语化
- 添加适当的标点符号和停顿
- 确保发音清晰易懂
- 保持原意不变

请直接输出优化后的文本："""
                    else:
                        # 通用提示词优化
                        analysis_prompt = f"""请优化以下提示词，使其更加详细、准确和有效。

原始提示词：
{request.prompt}

优化要求：
- 目标类型：{target_type}
- 优化风格：{enhancement_style}
- 保持原意的同时，增加更多细节和上下文
- 使提示词更加清晰和具体
- 控制合适的长度

请直接输出优化后的提示词："""
                else:
                    # 通用文案分析
                    analysis_prompt = f"""请分析以下文案内容，将其拆解为适合视频制作的场景。对于每个场景，请提供：

1. 场景描述
2. 关键词
3. 建议的视觉元素
4. 预估时长（秒）
5. 情感色调

文案内容：
{request.prompt}

请按照以下格式输出分析结果：

## 场景1
- **描述**：[场景描述]
- **关键词**：[关键词列表]
- **视觉元素**：[建议的视觉元素]
- **时长**：[预估秒数]
- **情感**：[情感色调]

## 场景2
...（如有更多场景）"""

                tool_params = {
                    'messages': [{"role": "user", "content": analysis_prompt}],
                    'model': 'deepseek-chat',
                    'max_tokens': request.parameters.get('max_tokens', 3000),
                    'temperature': request.parameters.get('temperature', 0.7)
                }

                result = await mcp_manager.call_tool(self.server_name, tool_name, tool_params)

                # 解析结果
                analysis_result = self._extract_content_text(result)

                return AIResponse(
                    success=True,
                    data={
                        'analysis': analysis_result,
                        'original_text': request.prompt,
                        'task_type': task,
                        'word_count': len(request.prompt)
                    },
                    provider=self.provider_name,
                    service_type=ServiceType("text_analysis"),
                    metadata={
                        'model_used': 'deepseek-chat',
                        'tool_used': tool_name,
                        'task': task
                    }
                )
            else:
                # 其他提供商的文本分析逻辑
                tool_name = self._find_tool_for_capability('text_analysis')
                if not tool_name:
                    raise APIError(f"No text analysis tool found for provider {self.provider_name}", ErrorCode.SERVICE_UNAVAILABLE)

                tool_params = {'text': request.prompt, **request.parameters}
                result = await mcp_manager.call_tool(self.server_name, tool_name, tool_params)

                return AIResponse(
                    success=True,
                    data=self._extract_content_text(result),
                    metadata={
                        'provider': self.provider_name,
                        'server_name': self.server_name,
                        'tool_used': tool_name
                    }
                )

        except Exception as e:
            logger.error(f"Text analysis failed for {self.provider_name}: {e}")
            raise APIError(f"Text analysis failed: {str(e)}", ErrorCode.API_SERVER_ERROR)

    async def _translate_text(self, request: AIRequest) -> AIResponse:
        """文本翻译 - 使用DeepSeek的chat_completion工具"""
        try:
            # 对于DeepSeek，使用chat_completion工具进行翻译
            if self.provider_name == 'deepseek':
                tool_name = 'chat_completion'

                # 构建翻译提示词
                source_lang = request.parameters.get('source_language', 'auto')
                target_lang = request.parameters.get('target_language', 'zh')

                # 语言映射
                lang_map = {
                    'zh': '中文', 'en': '英文', 'ja': '日文', 'ko': '韩文',
                    'fr': '法文', 'de': '德文', 'es': '西班牙文', 'ru': '俄文'
                }

                source_lang_name = lang_map.get(source_lang, source_lang)
                target_lang_name = lang_map.get(target_lang, target_lang)

                if source_lang == 'auto':
                    translation_prompt = f"""请将以下文本翻译成{target_lang_name}，保持原文的语气和风格：

{request.prompt}

请直接输出翻译结果，不要添加额外的解释。"""
                else:
                    translation_prompt = f"""请将以下{source_lang_name}文本翻译成{target_lang_name}，保持原文的语气和风格：

{request.prompt}

请直接输出翻译结果，不要添加额外的解释。"""

                tool_params = {
                    'messages': [{"role": "user", "content": translation_prompt}],
                    'model': 'deepseek-chat',
                    'max_tokens': request.parameters.get('max_tokens', 2000),
                    'temperature': request.parameters.get('temperature', 0.3)
                }

                result = await mcp_manager.call_tool(self.server_name, tool_name, tool_params)

                # 解析结果
                translated_text = self._extract_content_text(result)

                return AIResponse(
                    success=True,
                    data={
                        'translated_text': translated_text,
                        'original_text': request.prompt,
                        'source_language': source_lang,
                        'target_language': target_lang,
                        'character_count': len(request.prompt)
                    },
                    provider=self.provider_name,
                    service_type=ServiceType("translation"),
                    metadata={
                        'model_used': 'deepseek-chat',
                        'tool_used': tool_name
                    }
                )
            else:
                # 其他提供商的翻译逻辑
                tool_name = self._find_tool_for_capability('translation')
                if not tool_name:
                    raise APIError(f"No translation tool found for provider {self.provider_name}", ErrorCode.SERVICE_UNAVAILABLE)

                tool_params = {'text': request.prompt, **request.parameters}
                result = await mcp_manager.call_tool(self.server_name, tool_name, tool_params)

                return AIResponse(
                    success=True,
                    data=self._extract_content_text(result),
                    provider=self.provider_name,
                    service_type=ServiceType("translation"),
                    metadata={'tool_used': tool_name}
                )

        except Exception as e:
            logger.error(f"Translation failed for {self.provider_name}: {e}")
            raise APIError(f"Translation failed: {str(e)}", ErrorCode.API_SERVER_ERROR)

    async def _clone_voice(self, request: AIRequest) -> AIResponse:
        """语音克隆"""
        try:
            tool_name = self._find_tool_for_capability('voice_cloning')
            if not tool_name:
                raise APIError(f"No voice cloning tool found for provider {self.provider_name}", ErrorCode.SERVICE_UNAVAILABLE)

            tool_params = request.parameters.copy()
            result = await mcp_manager.call_tool(self.server_name, tool_name, tool_params)

            return AIResponse(
                success=True,
                data=self._extract_content_text(result),
                metadata={
                    'provider': self.provider_name,
                    'server_name': self.server_name,
                    'tool_used': tool_name
                }
            )

        except Exception as e:
            logger.error(f"Voice cloning failed for {self.provider_name}: {e}")
            raise APIError(f"Voice cloning failed: {str(e)}", ErrorCode.API_SERVER_ERROR)

    async def _handle_utility(self, request: AIRequest) -> AIResponse:
        """处理工具类请求"""
        try:
            tool_name = request.parameters.get('tool_name', 'list_voices')
            tool_params = request.parameters.get('tool_params', {})

            result = await mcp_manager.call_tool(self.server_name, tool_name, tool_params)

            return AIResponse(
                success=True,
                data=self._extract_content_text(result),
                metadata={
                    'provider': self.provider_name,
                    'server_name': self.server_name,
                    'tool_used': tool_name
                }
            )

        except Exception as e:
            logger.error(f"Utility request failed for {self.provider_name}: {e}")
            raise APIError(f"Utility request failed: {str(e)}", ErrorCode.API_SERVER_ERROR)

    def _find_tool_for_capability(self, capability: str) -> str:
        """查找支持特定能力的工具"""
        if not self.provider_metadata:
            return None

        for tool_name, tool_capability in self.provider_metadata.supported_tools.items():
            if tool_capability == capability:
                return tool_name

        return None

    def _prepare_speech_params(self, request: AIRequest) -> Dict[str, Any]:
        """准备语音合成参数"""
        from ..core.path_manager import path_manager

        base_params = {
            'text': request.prompt,
            'output_directory': path_manager.get_media_library_directory()
        }

        # 合并请求参数
        base_params.update(request.parameters)
        return base_params

    def _prepare_image_params(self, request: AIRequest) -> Dict[str, Any]:
        """准备图像生成参数"""
        base_params = {
            'prompt': request.prompt,
            'output_directory': get_config('storage.output_dir', './output')
        }

        base_params.update(request.parameters)
        return base_params

    def _prepare_video_params(self, request: AIRequest) -> Dict[str, Any]:
        """准备视频生成参数"""
        base_params = {
            'prompt': request.prompt,
            'output_directory': get_config('storage.output_dir', './output')
        }

        base_params.update(request.parameters)
        return base_params

    def _prepare_text_params(self, request: AIRequest) -> Dict[str, Any]:
        """准备文本生成参数"""
        logger.debug(f"🔧 Preparing text params for provider: {self.provider_name}")
        logger.debug(f"🔧 request.parameters type: {type(request.parameters)}, value: {request.parameters}")

        # 确保parameters是字典
        if not isinstance(request.parameters, dict):
            logger.warning(f"🚨 request.parameters is not a dict: {type(request.parameters)}, converting to empty dict")
            request.parameters = {}

        # 针对DeepSeek的特殊处理
        if self.provider_name == 'deepseek':
            if str(request.service_type) == 'chat':
                # 对话模式：使用multi_turn_chat工具
                base_params = {
                    'messages': request.parameters.get('messages', [
                        {"role": "user", "content": request.prompt}
                    ])
                }
            else:
                # 文本生成模式：使用chat_completion工具
                base_params = {
                    'messages': [{"role": "user", "content": request.prompt}]
                }
        else:
            # 其他提供商的通用处理
            if str(request.service_type) == 'chat':
                base_params = {
                    'message': request.prompt,
                    'messages': request.parameters.get('messages', [])
                }
            else:
                base_params = {
                    'prompt': request.prompt
                }

        # 添加其他参数
        for key, value in request.parameters.items():
            if key not in base_params:
                base_params[key] = value

        return base_params

    def _extract_content_text(self, result: Any) -> str:
        """提取MCP结果中的文本内容"""
        if not result or not hasattr(result, 'content'):
            return ""

        content_text = ""
        for content in result.content:
            if hasattr(content, 'text'):
                content_text += content.text

        return content_text

    def _convert_parsed_result_to_ai_response(self, parsed_result: Dict[str, Any], service_type: str) -> AIResponse:
        """将解析结果转换为AIResponse格式"""
        if parsed_result.get("success"):
            return AIResponse(
                success=True,
                data=parsed_result.get("data"),
                provider=self.provider_name,
                service_type=service_type,
                metadata=parsed_result.get("metadata", {}),
                processing_time=0.0
            )
        else:
            return AIResponse(
                success=False,
                error=parsed_result.get("error", "处理失败"),
                provider=self.provider_name,
                service_type=service_type,
                metadata=parsed_result.get("metadata", {}),
                processing_time=0.0
            )

    def record_request(self, success: bool):
        """记录请求统计"""
        # 简单的统计记录实现
        if not hasattr(self, '_request_stats'):
            self._request_stats = {'total': 0, 'success': 0, 'failed': 0}

        self._request_stats['total'] += 1
        if success:
            self._request_stats['success'] += 1
        else:
            self._request_stats['failed'] += 1

    def get_stats(self) -> Dict[str, Any]:
        """获取服务统计信息"""
        if not hasattr(self, '_request_stats'):
            self._request_stats = {'total': 0, 'success': 0, 'failed': 0}

        success_rate = (
            self._request_stats['success'] / self._request_stats['total']
            if self._request_stats['total'] > 0 else 0
        )

        return {
            'provider': self.provider_name,
            'server_name': self.server_name,
            'is_available': self.is_available,
            'total_requests': self._request_stats['total'],
            'successful_requests': self._request_stats['success'],
            'success_rate': success_rate,
            'error_count': self.error_count,
            'last_error_time': self.last_error_time
        }

    async def cleanup(self):
        """清理资源"""
        # MCP连接由管理器统一清理
        self._initialized = False
        self.is_available = False