"""
智能负载均衡器
实现多种负载均衡策略和熔断机制
"""

import time
import random
import logging
from typing import Dict, List, Optional, Any
from .base_types import AIServiceInterface, ServiceType

logger = logging.getLogger(__name__)

class SmartLoadBalancer:
    """智能负载均衡器"""

    def __init__(self, strategy: str = "smart"):
        self.strategy = strategy
        self._round_robin_index = {}  # 每个服务类型独立的轮询索引
        self._service_weights = {}  # 服务权重
        self._failure_counts = {}  # 失败计数
        self._last_failure_time = {}  # 最后失败时间
        self._circuit_breaker_threshold = 5  # 熔断阈值
        self._circuit_breaker_timeout = 300  # 熔断恢复时间（秒）
        self._performance_history = {}  # 性能历史记录

    def select_service(self, services: List[AIServiceInterface], service_type: ServiceType) -> Optional[AIServiceInterface]:
        """智能选择服务"""
        if not services:
            return None

        # 过滤可用服务（排除熔断的服务）
        available_services = self._filter_available_services(services)
        if not available_services:
            # 如果所有服务都熔断了，尝试恢复一个服务
            available_services = self._try_recover_services(services)

        if not available_services:
            logger.warning("No available services for service type: %s", service_type.value)
            return None

        # 根据策略选择服务
        selected_service = None

        try:
            if self.strategy == "round_robin":
                selected_service = self._round_robin_select(available_services, service_type)
            elif self.strategy == "weighted_round_robin":
                selected_service = self._weighted_round_robin_select(available_services, service_type)
            elif self.strategy == "best_performance":
                selected_service = self._best_performance_select(available_services)
            elif self.strategy == "least_connections":
                selected_service = self._least_connections_select(available_services)
            elif self.strategy == "smart":
                selected_service = self._smart_select(available_services, service_type)
            elif self.strategy == "random":
                selected_service = random.choice(available_services)
            else:
                selected_service = available_services[0]  # 默认选择第一个

            logger.debug("Selected service: %s for %s",
                        selected_service.provider_name if selected_service else "None",
                        service_type.value)

            return selected_service

        except Exception as e:
            logger.error("Error selecting service: %s", e)
            return available_services[0] if available_services else None

    def _filter_available_services(self, services: List[AIServiceInterface]) -> List[AIServiceInterface]:
        """过滤可用服务（排除熔断的服务）"""
        available = []
        current_time = time.time()

        for service in services:
            service_id = f"{service.provider_name}"

            # 检查服务是否熔断
            if service_id in self._failure_counts:
                failure_count = self._failure_counts[service_id]
                last_failure = self._last_failure_time.get(service_id, 0)

                # 如果失败次数超过阈值且在熔断时间内，跳过此服务
                if (failure_count >= self._circuit_breaker_threshold and
                    current_time - last_failure < self._circuit_breaker_timeout):
                    logger.debug("Service %s is circuit broken", service_id)
                    continue

            # 检查服务基本可用性
            if hasattr(service, 'is_available') and service.is_available:
                available.append(service)
            elif not hasattr(service, 'is_available'):
                # 如果服务没有is_available属性，默认认为可用
                available.append(service)

        return available

    def _try_recover_services(self, services: List[AIServiceInterface]) -> List[AIServiceInterface]:
        """尝试恢复熔断的服务"""
        current_time = time.time()
        recovered = []

        for service in services:
            service_id = f"{service.provider_name}"

            if service_id in self._last_failure_time:
                last_failure = self._last_failure_time[service_id]

                # 如果超过熔断恢复时间，重置失败计数
                if current_time - last_failure >= self._circuit_breaker_timeout:
                    logger.info("Recovering service %s from circuit breaker", service_id)
                    self._failure_counts[service_id] = 0
                    recovered.append(service)

        return recovered

    def _round_robin_select(self, services: List[AIServiceInterface], service_type: ServiceType) -> AIServiceInterface:
        """轮询选择"""
        type_key = service_type.value
        if type_key not in self._round_robin_index:
            self._round_robin_index[type_key] = 0

        service = services[self._round_robin_index[type_key] % len(services)]
        self._round_robin_index[type_key] += 1
        return service

    def _weighted_round_robin_select(self, services: List[AIServiceInterface], service_type: ServiceType) -> AIServiceInterface:
        """加权轮询选择"""
        # 计算服务权重
        weighted_services = []
        for service in services:
            weight = self._calculate_service_weight(service)
            weighted_services.extend([service] * max(1, int(weight * 10)))

        return self._round_robin_select(weighted_services, service_type)

    def _best_performance_select(self, services: List[AIServiceInterface]) -> AIServiceInterface:
        """基于性能选择最佳服务"""
        best_service = services[0]
        best_score = -1

        for service in services:
            score = self._calculate_performance_score(service)
            if score > best_score:
                best_score = score
                best_service = service

        return best_service

    def _least_connections_select(self, services: List[AIServiceInterface]) -> AIServiceInterface:
        """选择连接数最少的服务"""
        best_service = services[0]
        min_connections = float('inf')

        for service in services:
            stats = service.get_stats() if hasattr(service, 'get_stats') else {}
            connections = stats.get('active_connections', 0)

            if connections < min_connections:
                min_connections = connections
                best_service = service

        return best_service

    def _smart_select(self, services: List[AIServiceInterface], service_type: ServiceType) -> AIServiceInterface:
        """智能选择（综合多种因素）"""
        if len(services) == 1:
            return services[0]

        # 计算每个服务的综合评分
        service_scores = []

        for service in services:
            score = self._calculate_smart_score(service, service_type)
            service_scores.append((service, score))

        # 按评分排序，选择最高分的服务
        service_scores.sort(key=lambda x: x[1], reverse=True)

        # 在前几名中随机选择，避免总是选择同一个服务
        top_services = [s for s, score in service_scores[:min(3, len(service_scores))]]

        # 使用加权随机选择
        return self._weighted_random_select(top_services)

    def _calculate_service_weight(self, service: AIServiceInterface) -> float:
        """计算服务权重"""
        stats = service.get_stats() if hasattr(service, 'get_stats') else {}

        # 基础权重
        weight = 1.0

        # 根据成功率调整权重
        success_rate = stats.get('success_rate', 0.5)
        weight *= (0.5 + success_rate)

        # 根据响应时间调整权重（响应时间越短权重越高）
        avg_response_time = stats.get('avg_response_time', 1.0)
        weight *= (2.0 / (1.0 + avg_response_time))

        # 根据错误率调整权重
        error_rate = stats.get('error_rate', 0.1)
        weight *= (1.0 - min(error_rate, 0.9))

        return max(0.1, weight)  # 最小权重0.1

    def _calculate_performance_score(self, service: AIServiceInterface) -> float:
        """计算性能评分"""
        stats = service.get_stats() if hasattr(service, 'get_stats') else {}

        # 成功率权重：40%
        success_rate = stats.get('success_rate', 0.5)
        success_score = success_rate * 0.4

        # 响应时间权重：30%（响应时间越短分数越高）
        avg_response_time = stats.get('avg_response_time', 1.0)
        response_score = (1.0 / (1.0 + avg_response_time)) * 0.3

        # 可用性权重：20%
        availability = 1.0 if (hasattr(service, 'is_available') and service.is_available) else 0.5
        availability_score = availability * 0.2

        # 负载权重：10%（负载越低分数越高）
        active_connections = stats.get('active_connections', 0)
        load_score = (1.0 / (1.0 + active_connections * 0.1)) * 0.1

        return success_score + response_score + availability_score + load_score

    def _calculate_smart_score(self, service: AIServiceInterface, service_type: ServiceType) -> float:
        """计算智能评分"""
        base_score = self._calculate_performance_score(service)

        # 根据服务类型特性调整评分
        service_id = f"{service.provider_name}"

        # 服务特性加权
        if str(service_type) == "text_generation":
            # 文本生成偏好响应速度
            stats = service.get_stats() if hasattr(service, 'get_stats') else {}
            response_time = stats.get('avg_response_time', 1.0)
            base_score *= (2.0 / (1.0 + response_time))

        elif str(service_type) == "speech_synthesis":
            # 语音合成偏好质量稳定性
            stats = service.get_stats() if hasattr(service, 'get_stats') else {}
            success_rate = stats.get('success_rate', 0.5)
            base_score *= (1.0 + success_rate)

        elif str(service_type) == "video_generation":
            # 视频生成偏好成功率（因为耗时较长）
            stats = service.get_stats() if hasattr(service, 'get_stats') else {}
            success_rate = stats.get('success_rate', 0.5)
            base_score *= (1.5 + success_rate)

        # 考虑历史失败情况
        failure_count = self._failure_counts.get(service_id, 0)
        failure_penalty = min(failure_count * 0.1, 0.5)  # 最多扣除50%
        base_score *= (1.0 - failure_penalty)

        return max(0.1, base_score)

    def _weighted_random_select(self, services: List[AIServiceInterface]) -> AIServiceInterface:
        """加权随机选择"""
        if len(services) == 1:
            return services[0]

        weights = [self._calculate_service_weight(service) for service in services]
        total_weight = sum(weights)

        if total_weight == 0:
            return services[0]

        # 简单的加权随机选择
        r = random.random() * total_weight
        cumulative = 0

        for i, weight in enumerate(weights):
            cumulative += weight
            if r <= cumulative:
                return services[i]

        return services[-1]

    def record_success(self, service: AIServiceInterface, response_time: float = 0):
        """记录服务成功"""
        service_id = f"{service.provider_name}"

        # 成功时减少失败计数
        if service_id in self._failure_counts:
            self._failure_counts[service_id] = max(0, self._failure_counts[service_id] - 1)

        # 记录性能历史
        if service_id not in self._performance_history:
            self._performance_history[service_id] = []

        self._performance_history[service_id].append({
            'timestamp': time.time(),
            'success': True,
            'response_time': response_time
        })

        # 保持历史记录在合理范围内
        if len(self._performance_history[service_id]) > 100:
            self._performance_history[service_id] = self._performance_history[service_id][-100:]

    def record_failure(self, service: AIServiceInterface, error_type: str = "unknown"):
        """记录服务失败"""
        service_id = f"{service.provider_name}"
        self._failure_counts[service_id] = self._failure_counts.get(service_id, 0) + 1
        self._last_failure_time[service_id] = time.time()

        # 记录失败历史
        if service_id not in self._performance_history:
            self._performance_history[service_id] = []

        self._performance_history[service_id].append({
            'timestamp': time.time(),
            'success': False,
            'error_type': error_type
        })

        # 保持历史记录在合理范围内
        if len(self._performance_history[service_id]) > 100:
            self._performance_history[service_id] = self._performance_history[service_id][-100:]

        logger.warning("Service %s failed: %s (failure count: %d)",
                      service_id, error_type, self._failure_counts[service_id])

    def get_service_health(self, service: AIServiceInterface) -> Dict[str, Any]:
        """获取服务健康状态"""
        service_id = f"{service.provider_name}"
        current_time = time.time()

        failure_count = self._failure_counts.get(service_id, 0)
        last_failure = self._last_failure_time.get(service_id, 0)

        is_circuit_broken = (
            failure_count >= self._circuit_breaker_threshold and
            current_time - last_failure < self._circuit_breaker_timeout
        )

        # 计算最近的成功率
        recent_success_rate = self._calculate_recent_success_rate(service_id)

        return {
            'service_id': service_id,
            'failure_count': failure_count,
            'last_failure_time': last_failure,
            'is_circuit_broken': is_circuit_broken,
            'weight': self._calculate_service_weight(service),
            'performance_score': self._calculate_performance_score(service),
            'recent_success_rate': recent_success_rate,
            'circuit_breaker_threshold': self._circuit_breaker_threshold,
            'circuit_breaker_timeout': self._circuit_breaker_timeout
        }

    def _calculate_recent_success_rate(self, service_id: str) -> float:
        """计算最近的成功率"""
        if service_id not in self._performance_history:
            return 0.5  # 默认成功率

        history = self._performance_history[service_id]
        if not history:
            return 0.5

        # 计算最近20次请求的成功率
        recent_history = history[-20:]
        success_count = sum(1 for record in recent_history if record.get('success', False))

        return success_count / len(recent_history)

    def get_load_balancer_stats(self) -> Dict[str, Any]:
        """获取负载均衡器统计信息"""
        return {
            'strategy': self.strategy,
            'circuit_breaker_threshold': self._circuit_breaker_threshold,
            'circuit_breaker_timeout': self._circuit_breaker_timeout,
            'failure_counts': dict(self._failure_counts),
            'last_failure_times': dict(self._last_failure_time),
            'round_robin_indices': dict(self._round_robin_index),
            'performance_history_size': {
                service_id: len(history)
                for service_id, history in self._performance_history.items()
            }
        }
