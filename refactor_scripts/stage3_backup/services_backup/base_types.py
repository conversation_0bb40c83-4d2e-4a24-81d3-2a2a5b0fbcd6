"""
基础类型定义
避免循环导入的基础类型和接口
"""

import time
from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import Dict, List, Optional, Any

class ServiceType:
    """动态AI服务类型"""

    def __init__(self, value: str):
        self.value = value

    def __str__(self):
        return self.value

    def __eq__(self, other):
        if isinstance(other, ServiceType):
            return self.value == other.value
        return self.value == other

    def __hash__(self):
        return hash(self.value)

    @classmethod
    def from_string(cls, value: str):
        """从字符串创建ServiceType实例"""
        return cls(value)

    # 兼容性属性 - 保持向后兼容
    @property
    def TEXT_GENERATION(self):
        return ServiceType("text_generation")

    @property
    def TEXT_ANALYSIS(self):
        return ServiceType("text_analysis")

    @property
    def SPEECH_SYNTHESIS(self):
        return ServiceType("speech_synthesis")

    @property
    def VIDEO_GENERATION(self):
        return ServiceType("video_generation")

    @property
    def IMAGE_GENERATION(self):
        return ServiceType("image_generation")

    @property
    def UTILITY(self):
        return ServiceType("utility")

    @property
    def TRANSLATION(self):
        return ServiceType("translation")

    @property
    def CHAT(self):
        return ServiceType("chat")

    @property
    def VOICE_CLONING(self):
        return ServiceType("voice_cloning")

class ServiceProvider:
    """动态服务提供商"""

    def __init__(self, value: str):
        self.value = value

    def __str__(self):
        return self.value

    def __eq__(self, other):
        if isinstance(other, ServiceProvider):
            return self.value == other.value
        return self.value == other

    def __hash__(self):
        return hash(self.value)

    @classmethod
    def from_string(cls, value: str):
        """从字符串创建ServiceProvider实例"""
        return cls(value)

    # 兼容性属性 - 保持向后兼容
    @property
    def DEEPSEEK(self):
        return ServiceProvider("deepseek")

    @property
    def MINIMAX(self):
        return ServiceProvider("minimax")

    @property
    def VOLCANO(self):
        return ServiceProvider("volcano")

    @property
    def ELEVENLABS(self):
        return ServiceProvider("elevenlabs")

    @property
    def DOUBAO(self):
        return ServiceProvider("doubao")

    @property
    def VIDU(self):
        return ServiceProvider("vidu")

# 动态类型兼容性层
def get_dynamic_service_types():
    """获取所有可用的服务类型"""
    try:
        from .complete_dynamic_type_manager import complete_dynamic_type_manager
        return [ServiceType(st.name) for st in complete_dynamic_type_manager.get_all_service_types()]
    except ImportError:
        # 回退到默认类型
        return [
            ServiceType("text_generation"),
            ServiceType("text_analysis"),
            ServiceType("speech_synthesis"),
            ServiceType("video_generation"),
            ServiceType("image_generation"),
            ServiceType("utility"),
            ServiceType("translation"),
            ServiceType("chat"),
            ServiceType("voice_cloning")
        ]

def get_dynamic_service_providers():
    """获取所有可用的服务提供商"""
    try:
        from .complete_dynamic_type_manager import complete_dynamic_type_manager
        return [ServiceProvider(sp.name) for sp in complete_dynamic_type_manager.get_all_service_providers()]
    except ImportError:
        # 回退到默认提供商
        return [
            ServiceProvider("deepseek"),
            ServiceProvider("minimax"),
            ServiceProvider("volcano"),
            ServiceProvider("elevenlabs"),
            ServiceProvider("doubao"),
            ServiceProvider("vidu")
        ]

def is_valid_service_type(service_type: str) -> bool:
    """检查服务类型是否有效"""
    try:
        from .complete_dynamic_type_manager import complete_dynamic_type_manager
        return complete_dynamic_type_manager.get_service_type(service_type) is not None
    except ImportError:
        # 回退检查
        default_types = ["text_generation", "text_analysis", "speech_synthesis",
                        "video_generation", "image_generation", "utility",
                        "translation", "chat", "voice_cloning"]
        return service_type in default_types

def is_valid_service_provider(provider: str) -> bool:
    """检查服务提供商是否有效"""
    try:
        from .complete_dynamic_type_manager import complete_dynamic_type_manager
        return complete_dynamic_type_manager.is_valid_service_provider(provider)
    except ImportError:
        # 回退检查
        default_providers = ["deepseek", "minimax", "volcano", "elevenlabs", "doubao", "vidu"]
        return provider in default_providers

@dataclass
class AIRequest:
    """AI请求数据结构"""
    service_type: ServiceType
    provider: Optional[str] = None
    prompt: str = ""
    parameters: Dict[str, Any] = None
    metadata: Dict[str, Any] = None

    def __post_init__(self):
        if self.parameters is None:
            self.parameters = {}
        if self.metadata is None:
            self.metadata = {}

        # 确保service_type是ServiceType实例
        if isinstance(self.service_type, str):
            self.service_type = ServiceType(self.service_type)

    @property
    def service_type_str(self) -> str:
        """获取服务类型字符串值"""
        return str(self.service_type)

@dataclass
class AIResponse:
    """AI响应数据结构"""
    success: bool
    data: Any = None
    error: Optional[str] = None
    provider: Optional[str] = None
    service_type: Optional[ServiceType] = None
    metadata: Dict[str, Any] = None
    processing_time: float = 0.0

    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}

class AIServiceInterface(ABC):
    """AI服务接口抽象类"""

    def __init__(self, provider_name: str, server_name: str = None):
        self.provider_name = provider_name
        self.server_name = server_name or provider_name
        self.is_available = True
        self.last_error_time = 0
        self.error_count = 0
        self.total_requests = 0
        self.successful_requests = 0
        self._recovery_time = 0

    @abstractmethod
    async def initialize(self) -> bool:
        """初始化服务"""
        pass

    @abstractmethod
    async def health_check(self) -> bool:
        """健康检查"""
        pass

    @abstractmethod
    def get_supported_services(self) -> List[ServiceType]:
        """获取支持的服务类型"""
        pass

    @abstractmethod
    async def process_request(self, request: AIRequest) -> AIResponse:
        """处理AI请求"""
        pass

    def get_stats(self) -> Dict[str, Any]:
        """获取服务统计信息"""
        success_rate = 0.0
        if self.total_requests > 0:
            success_rate = self.successful_requests / self.total_requests

        return {
            'provider': self.provider_name,
            'server_name': self.server_name,
            'is_available': self.is_available,
            'total_requests': self.total_requests,
            'successful_requests': self.successful_requests,
            'success_rate': success_rate,
            'error_count': self.error_count,
            'last_error_time': self.last_error_time
        }

    def record_request(self, success: bool):
        """记录请求结果"""
        self.total_requests += 1
        if success:
            self.successful_requests += 1
            if self.error_count > 0:
                self.error_count = max(0, self.error_count - 1)
            if not self.is_available and self.error_count <= 2:
                self.is_available = True
        else:
            self.error_count += 1
            self.last_error_time = time.time()
            error_threshold = 10
            if self.error_count >= error_threshold:
                self.is_available = False
                self._recovery_time = time.time() + 300
