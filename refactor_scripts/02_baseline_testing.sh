#!/bin/bash

# DaVinci AI Co-pilot Pro 功能基线测试脚本
# 阶段0：功能基线测试

set -e

echo "🧪 开始功能基线测试..."

PROJECT_ROOT="/Library/Application Support/Blackmagic Design/DaVinci Resolve/Fusion/Scripts/Utility/DaVinci AI Co-pilot Pro"
REFACTOR_DIR="${PROJECT_ROOT}/refactor_scripts"
TESTING_DIR="${REFACTOR_DIR}/testing"

# 创建测试目录
mkdir -p "$TESTING_DIR"

cd "$PROJECT_ROOT"

echo "📋 记录当前功能状态..."

# 1. 创建功能测试脚本
cat > "$TESTING_DIR/test_all_functions.py" << 'EOF'
#!/usr/bin/env python3
"""
DaVinci AI Co-pilot Pro 功能基线测试
测试所有核心功能，记录当前工作状态
"""

import sys
import os
import json
import asyncio
import traceback
from datetime import datetime

# 添加项目路径
sys.path.insert(0, '/Library/Application Support/Blackmagic Design/DaVinci Resolve/Fusion/Scripts/Utility/DaVinci AI Co-pilot Pro')

class BaselineTester:
    def __init__(self):
        self.results = {
            'test_time': datetime.now().isoformat(),
            'tests': {},
            'summary': {
                'total': 0,
                'passed': 0,
                'failed': 0,
                'errors': 0
            }
        }
    
    def test_import_modules(self):
        """测试核心模块导入"""
        print("🔍 测试模块导入...")
        
        modules_to_test = [
            'src.services.direct_service_adapter',
            'src.services.simplified_ai_services', 
            'src.services.enhanced_ai_services',
            'src.services.fastmcp_server',
            'src.types.simplified_types',
            'src.api.routes'
        ]
        
        for module in modules_to_test:
            try:
                __import__(module)
                self.record_test(f"import_{module}", True, "模块导入成功")
                print(f"  ✅ {module}")
            except Exception as e:
                self.record_test(f"import_{module}", False, f"导入失败: {str(e)}")
                print(f"  ❌ {module}: {str(e)}")
    
    def test_config_loading(self):
        """测试配置加载"""
        print("⚙️  测试配置加载...")
        
        config_files = [
            'config/config.json',
            'config/mcp_enhanced.json',
            '.env'
        ]
        
        for config_file in config_files:
            try:
                if config_file.endswith('.json'):
                    with open(config_file, 'r', encoding='utf-8') as f:
                        json.load(f)
                    self.record_test(f"config_{config_file}", True, "配置文件加载成功")
                    print(f"  ✅ {config_file}")
                elif config_file == '.env':
                    if os.path.exists(config_file):
                        with open(config_file, 'r') as f:
                            content = f.read()
                        self.record_test(f"config_{config_file}", True, f"环境文件存在，{len(content.splitlines())}行")
                        print(f"  ✅ {config_file}")
                    else:
                        self.record_test(f"config_{config_file}", False, "环境文件不存在")
                        print(f"  ❌ {config_file}: 文件不存在")
            except Exception as e:
                self.record_test(f"config_{config_file}", False, f"加载失败: {str(e)}")
                print(f"  ❌ {config_file}: {str(e)}")
    
    def test_service_adapters(self):
        """测试服务适配器"""
        print("🔧 测试服务适配器...")
        
        try:
            from src.services.direct_service_adapter import DirectServiceAdapter
            adapter = DirectServiceAdapter()
            self.record_test("direct_service_adapter_init", True, "DirectServiceAdapter初始化成功")
            print("  ✅ DirectServiceAdapter 初始化")
        except Exception as e:
            self.record_test("direct_service_adapter_init", False, f"初始化失败: {str(e)}")
            print(f"  ❌ DirectServiceAdapter: {str(e)}")
        
        try:
            from src.services.simplified_ai_services import SimplifiedAIServices
            services = SimplifiedAIServices()
            self.record_test("simplified_ai_services_init", True, "SimplifiedAIServices初始化成功")
            print("  ✅ SimplifiedAIServices 初始化")
        except Exception as e:
            self.record_test("simplified_ai_services_init", False, f"初始化失败: {str(e)}")
            print(f"  ❌ SimplifiedAIServices: {str(e)}")
        
        try:
            from src.services.enhanced_ai_services import EnhancedAIServices
            enhanced = EnhancedAIServices()
            self.record_test("enhanced_ai_services_init", True, "EnhancedAIServices初始化成功")
            print("  ✅ EnhancedAIServices 初始化")
        except Exception as e:
            self.record_test("enhanced_ai_services_init", False, f"初始化失败: {str(e)}")
            print(f"  ❌ EnhancedAIServices: {str(e)}")
    
    def test_mcp_connections(self):
        """测试MCP连接"""
        print("🌐 测试MCP连接...")
        
        try:
            from src.services.fastmcp_server import SimpleMCPManager
            mcp_manager = SimpleMCPManager()
            self.record_test("mcp_manager_init", True, "MCP管理器初始化成功")
            print("  ✅ MCP管理器 初始化")
            
            # 测试MCP服务器配置
            if hasattr(mcp_manager, 'servers'):
                server_count = len(mcp_manager.servers)
                self.record_test("mcp_servers_config", True, f"配置了{server_count}个MCP服务器")
                print(f"  ✅ MCP服务器配置: {server_count}个")
            else:
                self.record_test("mcp_servers_config", False, "未找到MCP服务器配置")
                print("  ❌ MCP服务器配置: 未找到")
                
        except Exception as e:
            self.record_test("mcp_manager_init", False, f"初始化失败: {str(e)}")
            print(f"  ❌ MCP管理器: {str(e)}")
    
    def test_api_routes(self):
        """测试API路由"""
        print("🛣️  测试API路由...")
        
        try:
            from src.api.routes import app
            self.record_test("api_routes_import", True, "API路由导入成功")
            print("  ✅ API路由 导入")
            
            # 检查路由数量
            if hasattr(app, 'routes'):
                route_count = len(app.routes)
                self.record_test("api_routes_count", True, f"发现{route_count}个路由")
                print(f"  ✅ API路由数量: {route_count}")
            else:
                self.record_test("api_routes_count", False, "无法获取路由信息")
                print("  ❌ API路由数量: 无法获取")
                
        except Exception as e:
            self.record_test("api_routes_import", False, f"导入失败: {str(e)}")
            print(f"  ❌ API路由: {str(e)}")
    
    def test_type_system(self):
        """测试类型系统"""
        print("📝 测试类型系统...")
        
        try:
            from src.types.simplified_types import ServiceCapability, ServiceProvider
            
            # 测试枚举
            capabilities = list(ServiceCapability)
            providers = list(ServiceProvider)
            
            self.record_test("type_system_simplified", True, 
                           f"简化类型系统: {len(capabilities)}个能力, {len(providers)}个提供商")
            print(f"  ✅ 简化类型系统: {len(capabilities)}个能力, {len(providers)}个提供商")
            
        except Exception as e:
            self.record_test("type_system_simplified", False, f"简化类型系统失败: {str(e)}")
            print(f"  ❌ 简化类型系统: {str(e)}")
        
        try:
            from src.types.base_types import ServiceRequest
            self.record_test("type_system_base", True, "基础类型系统可用")
            print("  ✅ 基础类型系统")
        except Exception as e:
            self.record_test("type_system_base", False, f"基础类型系统失败: {str(e)}")
            print(f"  ❌ 基础类型系统: {str(e)}")
    
    def record_test(self, test_name, passed, message):
        """记录测试结果"""
        self.results['tests'][test_name] = {
            'passed': passed,
            'message': message,
            'timestamp': datetime.now().isoformat()
        }
        
        self.results['summary']['total'] += 1
        if passed:
            self.results['summary']['passed'] += 1
        else:
            self.results['summary']['failed'] += 1
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始功能基线测试...\n")
        
        try:
            self.test_import_modules()
            print()
            
            self.test_config_loading()
            print()
            
            self.test_service_adapters()
            print()
            
            self.test_mcp_connections()
            print()
            
            self.test_api_routes()
            print()
            
            self.test_type_system()
            print()
            
        except Exception as e:
            print(f"🚨 测试过程中发生错误: {str(e)}")
            traceback.print_exc()
            self.results['summary']['errors'] += 1
        
        # 保存结果
        self.save_results()
        self.print_summary()
    
    def save_results(self):
        """保存测试结果"""
        output_file = '/Library/Application Support/Blackmagic Design/DaVinci Resolve/Fusion/Scripts/Utility/DaVinci AI Co-pilot Pro/refactor_scripts/testing/baseline_results.json'
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False)
        
        print(f"💾 测试结果已保存: {output_file}")
    
    def print_summary(self):
        """打印测试摘要"""
        summary = self.results['summary']
        
        print("\n" + "="*50)
        print("📊 功能基线测试摘要")
        print("="*50)
        print(f"总测试数: {summary['total']}")
        print(f"通过: {summary['passed']} ✅")
        print(f"失败: {summary['failed']} ❌")
        print(f"错误: {summary['errors']} 🚨")
        
        success_rate = (summary['passed'] / summary['total'] * 100) if summary['total'] > 0 else 0
        print(f"成功率: {success_rate:.1f}%")
        
        if summary['failed'] > 0 or summary['errors'] > 0:
            print("\n⚠️  发现问题，建议在重构前先修复这些问题")
        else:
            print("\n🎉 所有测试通过，可以安全开始重构！")

if __name__ == "__main__":
    tester = BaselineTester()
    tester.run_all_tests()
EOF

# 2. 运行功能基线测试
echo "🧪 运行功能基线测试..."
python3 "$TESTING_DIR/test_all_functions.py"

# 3. 创建手动测试检查清单
cat > "$TESTING_DIR/manual_test_checklist.txt" << EOF
手动功能测试检查清单
==================

请在重构前手动验证以下功能：

🎬 DaVinci Resolve集成:
□ 插件能在DaVinci Resolve中正常加载
□ 插件界面能正常显示
□ 能访问DaVinci Resolve的项目数据

🤖 AI服务功能:
□ 文本生成功能正常工作
□ 翻译功能正常工作  
□ 语音合成功能正常工作
□ 图像生成功能正常工作
□ 视频生成功能正常工作

⚙️  配置和连接:
□ 所有AI服务商连接正常
□ API密钥配置正确
□ MCP服务器连接稳定

🌐 Web界面:
□ Web界面能正常访问
□ 所有按钮和功能正常响应
□ 文件上传下载功能正常

📝 记录测试结果:
测试时间: _______________
测试人员: _______________

发现的问题:
_________________________
_________________________
_________________________

总体评估:
□ 所有功能正常，可以开始重构
□ 发现问题，需要先修复再重构
EOF

# 4. 创建性能基准测试
cat > "$TESTING_DIR/performance_baseline.py" << 'EOF'
#!/usr/bin/env python3
"""
性能基准测试 - 记录重构前的性能指标
"""

import time
import psutil
import json
import os
from datetime import datetime

def measure_startup_time():
    """测量启动时间"""
    start_time = time.time()
    
    try:
        # 模拟插件启动过程
        import sys
        sys.path.insert(0, '/Library/Application Support/Blackmagic Design/DaVinci Resolve/Fusion/Scripts/Utility/DaVinci AI Co-pilot Pro')
        
        from src.services.direct_service_adapter import DirectServiceAdapter
        from src.services.simplified_ai_services import SimplifiedAIServices
        
        adapter = DirectServiceAdapter()
        services = SimplifiedAIServices()
        
        startup_time = time.time() - start_time
        return startup_time
        
    except Exception as e:
        return -1

def measure_memory_usage():
    """测量内存使用"""
    process = psutil.Process()
    memory_info = process.memory_info()
    
    return {
        'rss': memory_info.rss,  # 物理内存
        'vms': memory_info.vms,  # 虚拟内存
        'percent': process.memory_percent()
    }

def run_performance_baseline():
    """运行性能基准测试"""
    print("⚡ 开始性能基准测试...")
    
    results = {
        'timestamp': datetime.now().isoformat(),
        'startup_time': measure_startup_time(),
        'memory_usage': measure_memory_usage(),
        'cpu_count': psutil.cpu_count(),
        'system_memory': psutil.virtual_memory().total
    }
    
    # 保存结果
    output_file = '/Library/Application Support/Blackmagic Design/DaVinci Resolve/Fusion/Scripts/Utility/DaVinci AI Co-pilot Pro/refactor_scripts/testing/performance_baseline.json'
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"📊 性能基准结果:")
    print(f"  启动时间: {results['startup_time']:.2f}秒")
    print(f"  内存使用: {results['memory_usage']['rss'] / 1024 / 1024:.1f}MB")
    print(f"  CPU使用率: {results['memory_usage']['percent']:.1f}%")
    print(f"💾 结果已保存: {output_file}")

if __name__ == "__main__":
    run_performance_baseline()
EOF

# 运行性能基准测试
echo "⚡ 运行性能基准测试..."
python3 "$TESTING_DIR/performance_baseline.py"

# 5. 更新重构日志
REFACTOR_LOG="$REFACTOR_DIR/refactor_log.txt"
cat >> "$REFACTOR_LOG" << EOF

阶段0: 功能基线测试 - 完成 ✅
- 运行了完整的功能基线测试
- 记录了当前所有功能的工作状态
- 创建了手动测试检查清单
- 建立了性能基准指标
- 生成了详细的测试报告

下一步: 创建自动化测试脚本
EOF

echo ""
echo "🎉 功能基线测试完成！"
echo ""
echo "📊 生成的测试文件:"
echo "  - 自动化测试: $TESTING_DIR/test_all_functions.py"
echo "  - 测试结果: $TESTING_DIR/baseline_results.json"
echo "  - 手动检查清单: $TESTING_DIR/manual_test_checklist.txt"
echo "  - 性能基准: $TESTING_DIR/performance_baseline.json"
echo ""
echo "⚠️  请查看测试结果，确保所有关键功能正常后再继续重构"
echo ""
echo "🚀 下一步: 创建自动化测试脚本"
echo "   运行: bash refactor_scripts/03_create_automation.sh"
