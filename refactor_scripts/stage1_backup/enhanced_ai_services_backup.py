"""
增强的AI服务管理器
支持动态服务提供商管理和MCP服务的无缝集成
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any
import time

from ..core import (
    get_config,
    APIError,
    ErrorCode,
    handle_errors,
    API_RETRY_CONFIG,
    monitor_performance
)
from .dynamic_service_provider import dynamic_provider_manager, ServiceProviderInstance
from .universal_result_parser import universal_result_parser
from .base_types import ServiceType, AIRequest, AIResponse, AIServiceInterface
from .simplified_ai_services import simplified_ai_service_manager

logger = logging.getLogger(__name__)

class EnhancedServiceRegistry:
    """增强的服务注册表"""

    def __init__(self):
        self.services: Dict[str, AIServiceInterface] = {}  # key: provider_name_server_name
        self.service_capabilities: Dict[str, List[ServiceType]] = {}
        self.provider_instances: Dict[str, ServiceProviderInstance] = {}

    def register_service(self, service: AIServiceInterface, instance: ServiceProviderInstance = None):
        """注册服务 - 使用简洁的提供商名称作为服务键"""
        # 使用简洁的提供商名称作为服务键
        service_key = service.provider_name
        self.services[service_key] = service

        # 从配置文件读取支持的服务类型
        supported_services = self._get_service_capabilities_from_config(
            service.provider_name,
            service.server_name
        )
        self.service_capabilities[service_key] = supported_services

        logger.info(f"📝 Registering service: {service_key}")
        logger.info(f"   Is available: {service.is_available}")
        logger.info(f"   Supported services (from config): {supported_services}")

        if instance:
            self.provider_instances[service_key] = instance

        logger.info(f"✅ Registered service: {service_key}")

    def _get_service_capabilities_from_config(self, provider_name: str, server_name: str) -> List[ServiceType]:
        """从配置文件获取服务能力 - 适配新的统一配置格式"""
        try:
            from pathlib import Path
            import json

            config_path = Path("config/unified_services.json")
            if not config_path.exists():
                logger.warning(f"Config not found for {provider_name}/{server_name}")
                return []

            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)

            # 使用新的配置格式：services -> service_name -> capabilities
            service_config = config.get('services', {}).get(server_name, {})
            if not service_config:
                logger.warning(f"Service config not found for {server_name}")
                return []

            # 从capabilities中获取启用的服务类型
            capabilities = service_config.get('capabilities', {})
            supported_types = []

            for capability_name, capability_config in capabilities.items():
                if capability_config.get('enabled', False):
                    try:
                        service_type = ServiceType(capability_name)
                        supported_types.append(service_type)
                    except ValueError:
                        logger.warning(f"Unknown service type in config: {capability_name}")

            logger.debug(f"📋 Service {server_name} capabilities: {[str(st) for st in supported_types]}")
            return supported_types

        except Exception as e:
            logger.error(f"Failed to get service capabilities from config: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return []

    def get_service(self, provider_name: str, server_name: str = None) -> Optional[AIServiceInterface]:
        """获取指定服务 - 使用简洁的提供商名称"""
        return self.services.get(provider_name)

    def get_available_services(self, service_type: ServiceType) -> List[AIServiceInterface]:
        """获取支持指定服务类型的可用服务"""
        available_services = []
        current_time = time.time()

        for service_key, capabilities in self.service_capabilities.items():
            if service_type in capabilities:
                service = self.services.get(service_key)
                if service:
                    # 检查自动恢复
                    if not service.is_available and hasattr(service, '_recovery_time'):
                        if current_time >= service._recovery_time:
                            service.is_available = True
                            service.error_count = 0
                            logger.info(f"Service {service_key} auto-recovered")

                    if service.is_available:
                        available_services.append(service)

        return available_services

    def get_services_by_provider(self, provider_name: str) -> List[AIServiceInterface]:
        """获取指定提供商的所有服务"""
        services = []
        for key, service in self.services.items():
            if service.provider_name == provider_name:
                services.append(service)
        return services

    def list_all_providers(self) -> List[str]:
        """列出所有已注册的提供商"""
        providers = set()
        for service in self.services.values():
            providers.add(service.provider_name)
        return list(providers)

    def get_all_services(self) -> List[AIServiceInterface]:
        """获取所有服务"""
        return list(self.services.values())

class SimplifiedAIServiceManager:
    """增强的AI服务管理器"""

    def __init__(self):
        self.registry = EnhancedServiceRegistry()

        # 使用智能负载均衡器
        from .smart_load_balancer import SmartLoadBalancer
        self.load_balancer = SmartLoadBalancer(
            strategy=get_config('ai_services.load_balancer.strategy', 'smart')
        )

        # 服务发现组件
        from .service_discovery import ServiceDiscovery
        self.service_discovery = ServiceDiscovery()

        self.circuit_breaker_threshold = get_config('ai_services.circuit_breaker.threshold', 5)
        self.health_check_interval = get_config('ai_services.health_check.interval', 300)
        self._health_check_task = None

    async def initialize(self):
        """初始化服务管理器"""
        print("🔧 PRINT DEBUG: Enhanced AI Service Manager initialize() called")
        print(f"🔧 PRINT DEBUG: logger type: {type(logger)}")
        logger.info("🔧 DEBUG: Enhanced AI Service Manager initialize() called")
        logger.info("Initializing Enhanced AI Service Manager...")

        try:
            # 动态加载和初始化服务
            print("🔧 PRINT DEBUG: About to call _load_services()...")
            logger.info("🔧 About to call _load_services()...")
            await self._load_services()
            print("🔧 PRINT DEBUG: _load_services() completed successfully")
            logger.info("✅ _load_services() completed successfully")
        except Exception as e:
            print(f"🔧 PRINT DEBUG: Exception in _load_services(): {e}")
            logger.error(f"❌ Failed to load services: {e}")
            logger.exception("Service loading exception details:")

        # 启动健康检查任务
        self._health_check_task = asyncio.create_task(self._health_check_loop())

        logger.info("Enhanced AI Service Manager initialized successfully")

    async def _load_services(self):
        """动态加载AI服务"""
        logger.info("🔄 Starting service loading...")

        # 加载MCP服务（已替代传统服务）
        logger.info("📡 Loading MCP services...")
        await self._load_mcp_services()

        # 执行服务能力发现
        logger.info("🔍 Discovering service capabilities...")
        await self._discover_service_capabilities()

        logger.info("✅ Service loading completed")

    async def _load_mcp_services(self):
        """动态加载MCP服务"""
        print("🔧 PRINT DEBUG: _load_mcp_services() called")
        logger.info("🚀 Starting MCP services loading...")
        try:
            print("🔧 PRINT DEBUG: About to import MCP modules...")
            logger.info("📦 Importing MCP modules...")
            from .fastmcp_server import fastmcp_manager
            from .enhanced_mcp_service import EnhancedMCPServiceAdapter
            print("🔧 PRINT DEBUG: MCP modules imported successfully")
            logger.info("✅ MCP modules imported successfully")

            # 初始化FastMCP管理器
            print("🔧 PRINT DEBUG: About to initialize FastMCP manager...")
            logger.info("🔧 Initializing FastMCP manager...")
            mcp_initialized = await fastmcp_manager.initialize()
            print(f"🔧 PRINT DEBUG: FastMCP manager initialization result: {mcp_initialized}")
            logger.info("✅ Using FastMCP 2.0 Manager")

            if mcp_initialized:
                # 获取可用的MCP服务器
                available_servers = fastmcp_manager.get_available_servers()
                print(f"🔧 PRINT DEBUG: Available servers: {available_servers}")

                # 检查所有服务器状态
                all_servers = fastmcp_manager.server_info
                print(f"🔧 PRINT DEBUG: All servers info: {list(all_servers.keys())}")
                for name, info in all_servers.items():
                    print(f"🔧 PRINT DEBUG: Server {name}: connected={info.is_connected}, error={info.last_error}")

                logger.info(f"✅ MCP Manager initialized with servers: {available_servers}")

                # 为每个可用的MCP服务器创建服务适配器
                for server_name in available_servers:
                    try:
                        # 从配置中获取提供商信息
                        server_config = fastmcp_manager.get_server_config(server_name)
                        provider_name = server_config.get('provider', server_name)

                        # 创建或获取提供商实例
                        instance = dynamic_provider_manager.create_instance(
                            provider_name, server_name, server_config
                        )

                        if instance:
                            # 创建MCP服务适配器
                            mcp_service = EnhancedMCPServiceAdapter(provider_name, server_name)
                            if await mcp_service.initialize():
                                self.registry.register_service(mcp_service, instance)
                                logger.info(f"✅ Registered MCP service: {provider_name}/{server_name}")
                            else:
                                logger.warning(f"⚠️ Failed to initialize MCP service: {provider_name}/{server_name}")
                        else:
                            logger.warning(f"⚠️ Failed to create provider instance for: {provider_name}")

                    except Exception as e:
                        logger.error(f"❌ Failed to create MCP service adapter for '{server_name}': {e}")
            else:
                logger.warning("⚠️ MCP Manager initialization failed")

        except Exception as e:
            logger.error(f"❌ Failed to initialize MCP services: {e}")
            logger.exception("MCP services initialization exception details:")

    async def _discover_service_capabilities(self):
        """发现所有服务的能力"""
        logger.info("Discovering service capabilities...")

        all_services = self.registry.get_all_services()
        if not all_services:
            logger.warning("No services available for capability discovery")
            return

        try:
            await self.service_discovery.refresh_all_capabilities(all_services)
            stats = self.service_discovery.get_service_discovery_stats()
            logger.info(f"Service capability discovery completed: {stats['total_capabilities']} capabilities discovered")

        except Exception as e:
            logger.error(f"Service capability discovery failed: {e}")

    @handle_errors(APIError, retry_config=API_RETRY_CONFIG)
    @monitor_performance("ai_service_request")
    async def process_request(self, request: AIRequest) -> AIResponse:
        """处理AI请求 - 使用简化的3层架构"""
        request_id = f"req_{int(time.time() * 1000)}_{id(request)}"
        logger.info(f"🔧 Processing AI request {request_id}: {str(request.service_type)}")

        try:
            # 使用简化的服务管理器直接处理请求
            response = await simplified_ai_service_manager.process_request(request)

            logger.info(f"🔧 Request {request_id} completed successfully")
            return response

        except Exception as e:
            logger.error(f"🚨 Request {request_id} failed: {e}")
            logger.exception(f"🚨 Full exception:")

            # 包装异常
            raise APIError(
                f"Service request failed: {str(e)}",
                ErrorCode.API_SERVER_ERROR,
                details={'provider': request.provider, 'service_type': str(request.service_type)}
            )

    async def _health_check_loop(self):
        """健康检查循环"""
        while True:
            try:
                await asyncio.sleep(self.health_check_interval)
                await self._perform_health_checks()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in health check loop: {e}")

    async def _perform_health_checks(self):
        """执行健康检查"""
        logger.debug("Performing health checks...")

        for service in self.registry.get_all_services():
            try:
                is_healthy = await service.health_check()
                if is_healthy and not service.is_available:
                    service.is_available = True
                    service.error_count = 0
                    logger.info(f"Service {service.provider_name} is back online")
                elif not is_healthy and service.is_available:
                    logger.warning(f"Service {service.provider_name} failed health check")
            except Exception as e:
                logger.error(f"Health check failed for {service.provider_name}: {e}")

    def get_service_stats(self) -> Dict[str, Any]:
        """获取所有服务的统计信息"""
        stats = {}
        for service in self.registry.get_all_services():
            service_key = service.provider_name
            stats[service_key] = service.get_stats()
        return stats

    def get_available_capabilities(self) -> Dict[str, List[str]]:
        """获取可用的服务能力 - 基于新的统一配置文件和MCP连接状态"""
        capabilities = {}

        try:
            # 从配置文件读取服务定义
            from pathlib import Path
            import json

            config_path = Path("config/unified_services.json")
            if not config_path.exists():
                logger.warning("Unified services config not found")
                return self._get_fallback_capabilities()

            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)

            # 获取MCP服务器连接状态
            from .fastmcp_server import fastmcp_manager
            from .complete_dynamic_type_manager import complete_dynamic_type_manager

            # 初始化所有服务类型为空列表
            service_types = complete_dynamic_type_manager.get_all_service_types()
            for service_type in service_types:
                capabilities[service_type.name] = []

            # 遍历新配置格式中的服务
            for service_name, service_config in config.get('services', {}).items():
                # 检查MCP服务器是否连接 (服务名对应MCP服务器名)
                server_info = fastmcp_manager.server_info.get(service_name)
                if server_info and server_info.is_connected:
                    # 从服务配置中获取能力
                    service_capabilities = service_config.get('capabilities', {})
                    for capability_name, capability_config in service_capabilities.items():
                        # 检查能力是否启用
                        if capability_config.get('enabled', False):
                            # 将能力名称映射到ServiceType
                            if capability_name in capabilities:
                                capabilities[capability_name].append(service_name)

            logger.info(f"📊 Available capabilities: {capabilities}")
            return capabilities

        except Exception as e:
            logger.error(f"❌ Failed to get capabilities from config: {e}")
            import traceback
            logger.error(f"❌ Traceback: {traceback.format_exc()}")
            return self._get_fallback_capabilities()

    async def shutdown(self):
        """关闭服务管理器"""
        logger.info("Shutting down Enhanced AI Service Manager...")

        # 清理所有服务
        for service in self.registry.get_all_services():
            try:
                if hasattr(service, 'cleanup'):
                    await service.cleanup()
                    logger.info(f"✅ Cleaned up service: {service.provider_name}")
            except Exception as e:
                logger.error(f"❌ Failed to cleanup service {service.provider_name}: {e}")

        # 清理FastMCP管理器
        try:
            from .fastmcp_server import fastmcp_manager
            await fastmcp_manager.shutdown()
            logger.info("✅ FastMCP Manager cleaned up")
        except Exception as e:
            logger.error(f"❌ Failed to cleanup FastMCP Manager: {e}")

        if self._health_check_task:
            self._health_check_task.cancel()
            try:
                await self._health_check_task
            except asyncio.CancelledError:
                pass

        logger.info("Enhanced AI Service Manager shutdown complete")

# 全局服务管理器实例
enhanced_ai_service_manager = SimplifiedAIServiceManager()
