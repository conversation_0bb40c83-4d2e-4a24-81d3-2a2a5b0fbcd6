#!/usr/bin/env python3
"""
克隆声音本地存储管理
"""
import json
import os
import time
from pathlib import Path
from typing import List, Dict, Optional
import logging
logger = logging.getLogger(__name__)

class CloneVoiceStorage:
    """克隆声音存储管理器"""

    def __init__(self, storage_dir: str = "storage"):
        """初始化存储管理器"""
        self.storage_dir = Path(storage_dir)
        self.storage_dir.mkdir(exist_ok=True)
        self.clone_voices_file = self.storage_dir / "clone_voices.json"

    def _load_clone_voices(self) -> List[Dict]:
        """加载克隆声音列表"""
        try:
            if self.clone_voices_file.exists():
                with open(self.clone_voices_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    return data.get('voices', [])
            return []
        except Exception as e:
            logger.error(f"Failed to load clone voices: {e}")
            return []

    def _save_clone_voices(self, voices: List[Dict]) -> bool:
        """保存克隆声音列表"""
        try:
            data = {
                'voices': voices,
                'last_updated': int(time.time())
            }
            with open(self.clone_voices_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            logger.error(f"Failed to save clone voices: {e}")
            return False

    def add_clone_voice(self, voice_data: Dict) -> bool:
        """添加克隆声音"""
        try:
            voices = self._load_clone_voices()

            # 检查是否已存在相同的voice_id
            voice_id = voice_data.get('voice_id')
            if voice_id:
                # 移除已存在的相同voice_id
                voices = [v for v in voices if v.get('voice_id') != voice_id]

            # 添加时间戳和状态信息
            voice_data.update({
                'created_at': int(time.time()),
                'status': 'active',
                'usage_count': 0
            })

            voices.append(voice_data)

            # 保存到文件
            success = self._save_clone_voices(voices)
            if success:
                logger.info(f"✅ Added clone voice: {voice_id}")
            return success

        except Exception as e:
            logger.error(f"Failed to add clone voice: {e}")
            return False

    def get_clone_voices(self, provider: Optional[str] = None) -> List[Dict]:
        """获取克隆声音列表"""
        try:
            voices = self._load_clone_voices()

            # 按提供商过滤
            if provider:
                voices = [v for v in voices if v.get('provider') == provider]

            # 按创建时间排序（最新的在前）
            voices.sort(key=lambda x: x.get('created_at', 0), reverse=True)

            return voices

        except Exception as e:
            logger.error(f"Failed to get clone voices: {e}")
            return []

    def remove_clone_voice(self, voice_id: str) -> bool:
        """删除克隆声音"""
        try:
            voices = self._load_clone_voices()
            original_count = len(voices)

            # 移除指定的voice_id
            voices = [v for v in voices if v.get('voice_id') != voice_id]

            if len(voices) < original_count:
                success = self._save_clone_voices(voices)
                if success:
                    logger.info(f"✅ Removed clone voice: {voice_id}")
                return success
            else:
                logger.warning(f"Clone voice not found: {voice_id}")
                return False

        except Exception as e:
            logger.error(f"Failed to remove clone voice: {e}")
            return False

    def update_voice_usage(self, voice_id: str) -> bool:
        """更新语音使用次数"""
        try:
            voices = self._load_clone_voices()

            for voice in voices:
                if voice.get('voice_id') == voice_id:
                    voice['usage_count'] = voice.get('usage_count', 0) + 1
                    voice['last_used'] = int(time.time())
                    break

            return self._save_clone_voices(voices)

        except Exception as e:
            logger.error(f"Failed to update voice usage: {e}")
            return False

    def get_voice_info(self, voice_id: str) -> Optional[Dict]:
        """获取特定克隆声音信息"""
        try:
            voices = self._load_clone_voices()

            for voice in voices:
                if voice.get('voice_id') == voice_id:
                    return voice

            return None

        except Exception as e:
            logger.error(f"Failed to get voice info: {e}")
            return None

# 全局实例
clone_voice_storage = CloneVoiceStorage()
