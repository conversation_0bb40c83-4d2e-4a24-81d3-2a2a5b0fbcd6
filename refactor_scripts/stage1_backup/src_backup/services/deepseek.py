"""
DeepSeek AI服务模块
直接调用DeepSeek API，集成到简化架构中
"""
import asyncio
import json
import logging
import time
from typing import Dict, Any, Optional
import aiohttp

from ..core import get_config, APIError, handle_errors, monitor_performance
from .simplified_types import ServiceRequest, ServiceResponse, ServiceCapability

logger = logging.getLogger(__name__)

class DeepSeekService:
    """DeepSeek AI服务"""

    def __init__(self):
        self.api_key = None
        self.base_url = "https://api.deepseek.com/v1"
        self.session = None
        self._initialized = False

    async def initialize(self):
        """初始化DeepSeek服务"""
        if self._initialized:
            return True

        try:
            # 获取API密钥
            self.api_key = get_config("DEEPSEEK_API_KEY")
            if not self.api_key:
                logger.error("❌ DeepSeek API key not configured")
                return False

            # 创建HTTP会话
            timeout = aiohttp.ClientTimeout(total=60)
            # 创建SSL上下文，禁用证书验证以解决macOS SSL问题
            import ssl
            ssl_context = ssl.create_default_context()
            ssl_context.check_hostname = False
            ssl_context.verify_mode = ssl.CERT_NONE

            connector = aiohttp.TCPConnector(ssl=ssl_context)
            self.session = aiohttp.ClientSession(
                timeout=timeout,
                connector=connector,
                headers={
                    'Authorization': f'Bearer {self.api_key}',
                    'Content-Type': 'application/json',
                    'User-Agent': 'DaVinci-AI-Copilot/1.0'
                }
            )

            self._initialized = True
            logger.info("✅ DeepSeek service initialized")
            return True

        except Exception as e:
            logger.error(f"❌ Failed to initialize DeepSeek service: {e}")
            return False

    async def cleanup(self):
        """清理资源"""
        if self.session:
            await self.session.close()
            self.session = None
        self._initialized = False

    @handle_errors(APIError)
    @monitor_performance("deepseek_request")
    async def process_request(self, request: ServiceRequest) -> ServiceResponse:
        """处理服务请求"""
        if not self._initialized:
            success = await self.initialize()
            if not success:
                return ServiceResponse.error_response(
                    error="Failed to initialize DeepSeek service",
                    provider=request.provider,
                    capability=request.capability,
                    processing_time=0
                )

        start_time = time.time()

        try:
            if request.capability == ServiceCapability.TEXT_GENERATION:
                return await self._generate_text(request, start_time)
            elif request.capability == ServiceCapability.TRANSLATION:
                return await self._translate_text(request, start_time)
            elif request.capability == ServiceCapability.TEXT_ANALYSIS:
                return await self._analyze_text(request, start_time)
            else:
                return ServiceResponse.error_response(
                    error=f"Unsupported capability: {request.capability.value}",
                    provider=request.provider,
                    capability=request.capability,
                    processing_time=time.time() - start_time
                )

        except Exception as e:
            logger.error(f"❌ DeepSeek request failed: {e}")
            return ServiceResponse.error_response(
                error=str(e),
                provider=request.provider,
                capability=request.capability,
                processing_time=time.time() - start_time
            )

    async def _generate_text(self, request: ServiceRequest, start_time: float) -> ServiceResponse:
        """文本生成"""
        try:
            messages = [{"role": "user", "content": request.content}]

            payload = {
                "model": "deepseek-chat",
                "messages": messages,
                "max_tokens": request.parameters.get("max_tokens", 1000),
                "temperature": request.parameters.get("temperature", 0.7),
                "stream": False
            }

            async with self.session.post(f"{self.base_url}/chat/completions", json=payload) as response:
                if response.status != 200:
                    error_text = await response.text()
                    raise APIError(f"DeepSeek API error {response.status}: {error_text}")

                result = await response.json()

                if "choices" not in result or not result["choices"]:
                    raise APIError("No response from DeepSeek API")

                generated_text = result["choices"][0]["message"]["content"]

                return ServiceResponse.success_response(
                    data={"generated_text": generated_text},
                    provider=request.provider,
                    capability=request.capability,
                    processing_time=time.time() - start_time
                )

        except Exception as e:
            logger.error(f"❌ DeepSeek text generation failed: {e}")
            raise

    async def _translate_text(self, request: ServiceRequest, start_time: float) -> ServiceResponse:
        """文本翻译"""
        try:
            source_lang = request.parameters.get("source_language", "auto")
            target_lang = request.parameters.get("target_language", "zh")

            # 构建翻译提示词
            if target_lang == "zh":
                prompt = f"请将以下文本翻译成中文，只返回翻译结果，不要添加任何解释：\n\n{request.content}"
            elif target_lang == "en":
                prompt = f"Please translate the following text to English, return only the translation result without any explanation:\n\n{request.content}"
            else:
                prompt = f"Please translate the following text to {target_lang}, return only the translation result:\n\n{request.content}"

            messages = [{"role": "user", "content": prompt}]

            payload = {
                "model": "deepseek-chat",
                "messages": messages,
                "max_tokens": 1000,
                "temperature": 0.3,
                "stream": False
            }

            async with self.session.post(f"{self.base_url}/chat/completions", json=payload) as response:
                if response.status != 200:
                    error_text = await response.text()
                    raise APIError(f"DeepSeek API error {response.status}: {error_text}")

                result = await response.json()

                if "choices" not in result or not result["choices"]:
                    raise APIError("No response from DeepSeek API")

                translated_text = result["choices"][0]["message"]["content"].strip()

                return ServiceResponse.success_response(
                    data={"translated_text": translated_text},
                    provider=request.provider,
                    capability=request.capability,
                    processing_time=time.time() - start_time
                )

        except Exception as e:
            logger.error(f"❌ DeepSeek translation failed: {e}")
            raise

    async def _analyze_text(self, request: ServiceRequest, start_time: float) -> ServiceResponse:
        """文本分析"""
        try:
            task = request.parameters.get("task", "analyze")

            if task in ["prompt_enhancement", "prompt_optimization"]:
                # 提示词优化
                prompt = f"""请优化以下提示词，使其更加清晰、具体且富有表现力。直接返回优化后的提示词，不需要任何格式化标记、说明或备注：

原始提示词：{request.content}

要求：只返回优化后的提示词内容，不要包含"优化后的提示词："等标记。"""
            else:
                # 通用文本分析 - 针对视频制作的文案分析
                prompt = f"""请将以下文案分析并拆解为视频制作场景，提供详细的制作建议：

文案内容：{request.content}

请按以下格式分析：

**场景拆解：**
- 场景1：[描述具体场景]
- 场景2：[描述具体场景]
- 场景3：[描述具体场景]

**拍摄建议：**
- 镜头类型：[特写/中景/远景等]
- 拍摄角度：[平视/俯视/仰视等]
- 光线要求：[自然光/人工光等]

**后期制作：**
- 配乐建议：[音乐风格]
- 剪辑节奏：[快/慢/变化]
- 特效需求：[如有需要]

**总体建议：**
[整体制作思路和注意事项]"""

            messages = [{"role": "user", "content": prompt}]

            payload = {
                "model": "deepseek-chat",
                "messages": messages,
                "max_tokens": 1500,
                "temperature": 0.7,
                "stream": False
            }

            async with self.session.post(f"{self.base_url}/chat/completions", json=payload) as response:
                if response.status != 200:
                    error_text = await response.text()
                    raise APIError(f"DeepSeek API error {response.status}: {error_text}")

                result = await response.json()

                if "choices" not in result or not result["choices"]:
                    raise APIError("No response from DeepSeek API")

                analysis_result = result["choices"][0]["message"]["content"]

                if task in ["prompt_enhancement", "prompt_optimization"]:
                    return ServiceResponse.success_response(
                        data={"enhanced_prompt": analysis_result},
                        provider=request.provider,
                        capability=request.capability,
                        processing_time=time.time() - start_time
                    )
                else:
                    return ServiceResponse.success_response(
                        data={"analysis_result": analysis_result},
                        provider=request.provider,
                        capability=request.capability,
                        processing_time=time.time() - start_time
                    )

        except Exception as e:
            logger.error(f"❌ DeepSeek text analysis failed: {e}")
            raise

# 全局DeepSeek服务实例
deepseek_service = DeepSeekService()
