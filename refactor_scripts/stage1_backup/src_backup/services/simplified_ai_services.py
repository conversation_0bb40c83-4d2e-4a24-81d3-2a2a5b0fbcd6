"""
简化的AI服务管理器
使用新的3层架构：Frontend → SimplifiedAIService → DirectServiceAdapter → MCP
"""
import logging
import time
from typing import Dict, Any, Optional

from .simplified_types import (
    ServiceRequest, ServiceResponse, ServiceCapability, ServiceProvider,
    convert_legacy_request, convert_to_legacy_response
)
from .direct_service_adapter import direct_service_adapter
from .base_types import AIRequest, AIResponse  # 保持向后兼容

logger = logging.getLogger(__name__)

class SimplifiedAIServiceManager:
    """简化的AI服务管理器"""

    def __init__(self):
        self.adapter = direct_service_adapter

    async def initialize(self):
        """初始化服务管理器"""
        logger.info("🚀 [SIMPLIFIED_MANAGER] Initializing...")
        try:
            # 确保DirectServiceAdapter的MCP管理器已初始化
            await self.adapter._ensure_initialized()
            logger.info("✅ [SIMPLIFIED_MANAGER] Initialization complete")
        except Exception as e:
            logger.error(f"❌ [SIMPLIFIED_MANAGER] Initialization failed: {e}")
            raise

    async def shutdown(self):
        """关闭服务管理器"""
        logger.info("🛑 [SIMPLIFIED_MANAGER] Shutting down...")
        try:
            # 关闭MCP连接
            if hasattr(self.adapter, 'mcp_manager'):
                await self.adapter.mcp_manager.shutdown()
            logger.info("✅ [SIMPLIFIED_MANAGER] Shutdown complete")
        except Exception as e:
            logger.error(f"❌ [SIMPLIFIED_MANAGER] Shutdown error: {e}")

    def get_service_stats(self):
        """获取服务统计信息"""
        return {
            "total_services": 5,
            "active_services": 5,
            "providers": ["deepseek", "minimax", "elevenlabs", "doubao", "vidu"],
            "capabilities": ["text_generation", "translation", "text_analysis", "speech_synthesis", "image_generation", "video_generation"]
        }

    def get_available_capabilities(self):
        """获取可用能力"""
        return [
            "text_generation",
            "translation",
            "text_analysis",
            "speech_synthesis",
            "image_generation",
            "video_generation"
        ]

    async def process_request(self, request: AIRequest) -> AIResponse:
        """处理AI请求 - 简化的处理流程"""
        start_time = time.time()

        try:
            # 1. 转换为新的统一请求格式
            service_request = convert_legacy_request(request)
            logger.info(f"🔄 [SIMPLIFIED_SERVICE] Processing {service_request.capability.value} request for {service_request.provider.value}")

            # 2. 使用直接适配器处理请求
            service_response = await self.adapter.execute_request(service_request)

            # 3. 转换回旧格式以保持兼容性
            legacy_response = convert_to_legacy_response(service_response)

            processing_time = time.time() - start_time
            logger.info(f"✅ [SIMPLIFIED_SERVICE] Request completed in {processing_time:.2f}s")

            return legacy_response

        except Exception as e:
            logger.error(f"❌ [SIMPLIFIED_SERVICE] Error processing request: {e}")
            return AIResponse(
                success=False,
                data={},
                error=str(e),
                metadata={
                    'processing_time': time.time() - start_time,
                    'error_type': type(e).__name__
                }
            )

    async def process_unified_request(self, request: ServiceRequest) -> ServiceResponse:
        """直接处理统一请求格式（新接口）"""
        logger.info(f"🔄 [SIMPLIFIED_SERVICE] Processing unified {request.capability.value} request for {request.provider.value}")
        return await self.adapter.execute_request(request)

    def get_supported_capabilities(self, provider: str) -> list[str]:
        """获取服务商支持的能力列表"""
        try:
            provider_enum = ServiceProvider(provider)
            from .simplified_types import get_supported_capabilities
            capabilities = get_supported_capabilities(provider_enum)
            return [cap.value for cap in capabilities]
        except ValueError:
            logger.warning(f"Unknown provider: {provider}")
            return []

    def is_capability_supported(self, provider: str, capability: str) -> bool:
        """检查服务商是否支持指定能力"""
        try:
            provider_enum = ServiceProvider(provider)
            capability_enum = ServiceCapability(capability)
            from .simplified_types import is_capability_supported
            return is_capability_supported(provider_enum, capability_enum)
        except ValueError:
            return False

    def get_all_providers(self) -> list[str]:
        """获取所有可用的服务商"""
        return [provider.value for provider in ServiceProvider]

    def get_all_capabilities(self) -> list[str]:
        """获取所有可用的能力"""
        return [capability.value for capability in ServiceCapability]

# 全局实例
simplified_ai_service_manager = SimplifiedAIServiceManager()

# 兼容性包装器 - 保持与现有代码的兼容性
class EnhancedAIServiceManagerCompat:
    """兼容性包装器，保持与现有enhanced_ai_services.py的接口兼容"""

    def __init__(self):
        self.simplified_manager = simplified_ai_service_manager

    async def process_ai_request(self, request: AIRequest) -> AIResponse:
        """兼容旧的process_ai_request接口"""
        return await self.simplified_manager.process_request(request)

    async def execute_ai_request(self, request: AIRequest) -> AIResponse:
        """兼容旧的execute_ai_request接口"""
        return await self.simplified_manager.process_request(request)

# 创建兼容性实例
enhanced_ai_service_manager_compat = EnhancedAIServiceManagerCompat()

# 便捷函数
async def create_and_execute_request(
    capability: str,
    provider: str,
    content: str,
    parameters: Optional[Dict[str, Any]] = None,
    metadata: Optional[Dict[str, Any]] = None
) -> ServiceResponse:
    """便捷函数：创建并执行服务请求"""
    request = ServiceRequest(
        capability=ServiceCapability(capability),
        provider=ServiceProvider(provider),
        content=content,
        parameters=parameters or {},
        metadata=metadata or {}
    )
    return await simplified_ai_service_manager.process_unified_request(request)

# 常用能力的便捷函数
async def generate_text(provider: str, prompt: str, **kwargs) -> ServiceResponse:
    """文本生成便捷函数"""
    return await create_and_execute_request(
        capability="text_generation",
        provider=provider,
        content=prompt,
        parameters=kwargs
    )

async def translate_text(provider: str, text: str, target_language: str = "en",
                        source_language: str = "auto", **kwargs) -> ServiceResponse:
    """翻译便捷函数"""
    parameters = {
        "target_language": target_language,
        "source_language": source_language,
        **kwargs
    }
    return await create_and_execute_request(
        capability="translation",
        provider=provider,
        content=text,
        parameters=parameters
    )

async def enhance_prompt(provider: str, prompt: str, target_type: str = "general", **kwargs) -> ServiceResponse:
    """提示词优化便捷函数"""
    parameters = {
        "task": "prompt_enhancement",
        "target_type": target_type,
        **kwargs
    }
    return await create_and_execute_request(
        capability="text_analysis",
        provider=provider,
        content=prompt,
        parameters=parameters
    )

async def synthesize_speech(provider: str, text: str, voice_id: str = "default", **kwargs) -> ServiceResponse:
    """语音合成便捷函数"""
    parameters = {
        "voice_id": voice_id,
        **kwargs
    }
    return await create_and_execute_request(
        capability="speech_synthesis",
        provider=provider,
        content=text,
        parameters=parameters
    )

async def generate_image(provider: str, prompt: str, **kwargs) -> ServiceResponse:
    """图像生成便捷函数"""
    return await create_and_execute_request(
        capability="image_generation",
        provider=provider,
        content=prompt,
        parameters=kwargs
    )

async def generate_video(provider: str, prompt: str, **kwargs) -> ServiceResponse:
    """视频生成便捷函数"""
    return await create_and_execute_request(
        capability="video_generation",
        provider=provider,
        content=prompt,
        parameters=kwargs
    )
