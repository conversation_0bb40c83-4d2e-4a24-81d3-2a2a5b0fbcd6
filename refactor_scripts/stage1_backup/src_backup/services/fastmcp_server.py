"""
FastMCP 2.0 简化客户端实现
使用官方推荐的FastMCP Client方式，提供标准的MCP服务调用
"""

import json
import logging
import os
from pathlib import Path
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

try:
    from fastmcp import Client
    FASTMCP_AVAILABLE = True
except ImportError:
    FASTMCP_AVAILABLE = False
    logging.warning("FastMCP 2.0 not available. Please install: pip install fastmcp>=2.10.0")

# 修复导入问题 - 使用绝对导入或者硬编码配置
# from ..core.config import get_config

def get_config(key: str, default=None):
    """临时配置获取函数"""
    import os
    if key == 'mcp.servers':
        return {
            "minimax": {
                "command": "npx",
                "args": ["@modelcontextprotocol/server-minimax"],
                "env": {"MINIMAX_API_KEY": os.getenv("MINIMAX_API_KEY", "")}
            },
            "elevenlabs": {
                "command": "npx",
                "args": ["@modelcontextprotocol/server-elevenlabs"],
                "env": {"ELEVENLABS_API_KEY": os.getenv("ELEVENLABS_API_KEY", "")}
            },
            "doubao": {
                "command": "npx",
                "args": ["@modelcontextprotocol/server-doubao"],
                "env": {"DOUBAO_API_KEY": os.getenv("DOUBAO_API_KEY", "")}
            },
            "deepseek": {
                "command": "npx",
                "args": ["@modelcontextprotocol/server-deepseek"],
                "env": {"DEEPSEEK_API_KEY": os.getenv("DEEPSEEK_API_KEY", "")}
            },
            "vidu": {
                "command": "npx",
                "args": ["@modelcontextprotocol/server-vidu"],
                "env": {"VIDU_API_KEY": os.getenv("VIDU_API_KEY", "")}
            }
        }
    return default

logger = logging.getLogger(__name__)

@dataclass
class MCPServerInfo:
    """MCP服务器信息"""
    name: str
    client: Optional[Any] = None
    is_connected: bool = False
    available_tools: List[str] = None
    capabilities: List[str] = None
    last_error: Optional[str] = None

class SimpleMCPManager:
    """基于官方FastMCP Client的简化MCP管理器"""

    def __init__(self):
        self.clients: Dict[str, Client] = {}
        self.server_info: Dict[str, MCPServerInfo] = {}
        # 根据当前文件位置确定配置文件路径
        current_dir = Path(__file__).parent
        project_root = current_dir.parent.parent  # 从 src/services 回到项目根目录
        self.config_path = project_root / "config" / "mcp_enhanced.json"
        self._initialized = False

    async def initialize(self) -> bool:
            """初始化MCP管理器"""
            if not FASTMCP_AVAILABLE:
                logger.error("FastMCP 2.0 not available. Please install: pip install fastmcp>=2.10.0")
                return False

            if self._initialized:
                return True

            try:
                # 🔑 确保环境变量已加载
                from dotenv import load_dotenv
                from pathlib import Path
                project_root = Path(__file__).parent.parent.parent
                env_file = project_root / ".env"
                if env_file.exists():
                    load_dotenv(env_file)
                    logger.debug(f"🔧 Loaded environment variables from {env_file}")

                # 加载配置
                config_data = self._load_config()
                if not config_data:
                    logger.error("Failed to load MCP configuration")
                    return False

                # 初始化服务器连接
                servers_config = config_data.get('mcpServers', {})
                success_count = 0

                for server_name, server_config in servers_config.items():
                    if not server_config.get('enabled', True):
                        logger.info(f"Skipping disabled MCP server: {server_name}")
                        continue

                    try:
                        await self._initialize_client(server_name, server_config)
                        success_count += 1
                    except Exception as e:
                        logger.error(f"Failed to initialize MCP client {server_name}: {e}")

                self._initialized = success_count > 0
                logger.info(f"SimpleMCP Manager initialized with {success_count} clients")
                return self._initialized

            except Exception as e:
                logger.error(f"Failed to initialize SimpleMCP Manager: {e}")
                return False

    def _load_config(self) -> Optional[Dict]:
            """加载MCP配置文件"""
            try:
                if not self.config_path.exists():
                    logger.error(f"MCP config file not found: {self.config_path}")
                    return None

                with open(self.config_path, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)

                # 处理环境变量替换
                config_data = self._resolve_environment_variables(config_data)
                return config_data

            except Exception as e:
                logger.error(f"Failed to load MCP config: {e}")
                return None

    def _resolve_environment_variables(self, config_data: Dict) -> Dict:
            """解析环境变量占位符"""
            try:
                # 获取环境变量映射
                global_settings = config_data.get('global_settings', {})
                env_mapping = global_settings.get('environment_variables', {})

                # 递归处理配置中的环境变量
                def resolve_value(value):
                    if isinstance(value, str) and value.startswith('${') and value.endswith('}'):
                        env_var = value[2:-1]  # 移除 ${ 和 }

                        # 首先尝试从系统环境变量获取
                        env_value = os.getenv(env_var)
                        if env_value:
                            return env_value

                        # 然后尝试从配置映射获取
                        if env_var in env_mapping:
                            config_path = env_mapping[env_var]
                            return get_config(config_path, '')

                        logger.warning(f"Environment variable not found: {env_var}")
                        return value
                    elif isinstance(value, dict):
                        return {k: resolve_value(v) for k, v in value.items()}
                    elif isinstance(value, list):
                        return [resolve_value(item) for item in value]
                    else:
                        return value

                return resolve_value(config_data)

            except Exception as e:
                logger.error(f"Failed to resolve environment variables: {e}")
                return config_data

    async def _initialize_client(self, server_name: str, server_config: Dict):
            """初始化单个MCP客户端 - 使用官方推荐方式"""
            logger.info(f"🔧 INIT DEBUG: _initialize_client called for {server_name}")
            try:
                # 根据官方文档，直接使用命令和参数创建客户端
                logger.info(f"🔧 CONFIG DEBUG: Initializing {server_name}")
                logger.debug(f"🔧 CONFIG DEBUG: server_config = {server_config}")
                command = server_config.get("command")
                args = server_config.get("args", [])
                env = server_config.get("env", {})
                logger.info(f"🔧 CONFIG DEBUG: {server_name} env = {env}")

                if not command:
                    raise ValueError(f"No command specified for server {server_name}")

                # 解析环境变量
                logger.info(f"🔧 ENV PARSE: Starting for {server_name}, env keys: {list(env.keys())}")
                resolved_env = {}
                for key, value in env.items():
                    logger.debug(f"🔧 ENV PARSE: Processing {key} = {value}")
                    if isinstance(value, str) and value.startswith('${') and value.endswith('}'):
                        env_var = value[2:-1]
                        # 从系统环境变量获取值
                        resolved_value = os.getenv(env_var)
                        logger.info(f"🔧 ENV DEBUG: Resolving {env_var} for {server_name}: {'✅ Found' if resolved_value else '❌ Not Found'}")
                        if resolved_value:
                            logger.debug(f"🔧 ENV DEBUG: {env_var} value: {resolved_value[:10]}...{resolved_value[-4:] if len(resolved_value) > 14 else resolved_value}")

                        # 如果系统环境变量没有，尝试从配置文件获取
                        if not resolved_value:
                            # 使用全局设置中的映射
                            config_data = self._load_config()
                            if config_data:
                                global_settings = config_data.get('global_settings', {})
                                env_mapping = global_settings.get('environment_variables', {})

                                if env_var in env_mapping:
                                    config_path = env_mapping[env_var]
                                    resolved_value = get_config(config_path, "")

                                    # 特殊处理OUTPUT_DIR，确保使用绝对路径
                                    if env_var == 'OUTPUT_DIR' and resolved_value:
                                        if not os.path.isabs(resolved_value):
                                            # 相对于项目根目录
                                            project_root = Path(__file__).parent.parent.parent
                                            resolved_value = str(project_root / resolved_value)
                                        # 确保目录存在
                                        os.makedirs(resolved_value, exist_ok=True)

                                    logger.debug(f"🔍 Resolved {env_var} from config: {config_path} = {resolved_value}")
                                else:
                                    logger.warning(f"No mapping found for environment variable {env_var}")
                            else:
                                logger.warning(f"Could not load config for environment variable resolution")

                        if resolved_value:
                            resolved_env[key] = resolved_value
                            logger.info(f"✅ ENV RESOLVED: {key} = {resolved_value[:10]}...{resolved_value[-4:] if len(str(resolved_value)) > 14 else resolved_value}")
                        else:
                            logger.error(f"❌ ENV MISSING: Environment variable {env_var} not found for {server_name}")
                            logger.error(f"🔧 ENV DEBUG: Available env vars: {list(os.environ.keys())[:10]}...")
                    else:
                        resolved_env[key] = value

                # 创建客户端 - 使用官方推荐的配置方式
                if server_config.get("transport") == "stdio":
                    # 根据官方文档，使用配置字典的方式
                    # 确保环境变量正确传递
                    logger.info(f"🔧 MCP CONFIG: Setting up {server_name} with env vars: {list(resolved_env.keys())}")
                    for key, value in resolved_env.items():
                        logger.debug(f"🔧 MCP ENV: {key} = {value[:10]}...{value[-4:] if len(value) > 14 else value}")

                    mcp_config = {
                        "mcpServers": {
                            server_name: {
                                "command": command,
                                "args": args,
                                "env": resolved_env
                            }
                        }
                    }
                    client = Client(mcp_config)
                else:
                    # 对于其他传输方式
                    url = server_config.get("url")
                    if url:
                        client = Client(url)
                    else:
                        raise ValueError(f"No URL specified for non-stdio transport in {server_name}")

                # 存储客户端和服务器信息
                self.clients[server_name] = client

                # 🔑 立即建立连接并获取工具列表
                try:
                    async with client:
                        # 获取可用工具列表
                        tools = await client.list_tools()
                        logger.debug(f"🔧 DEBUG: tools对象类型: {type(tools)}")
                        logger.debug(f"🔧 DEBUG: tools对象内容: {tools}")
                        logger.debug(f"🔧 DEBUG: tools对象属性: {dir(tools)}")

                        available_tools = []
                        if hasattr(tools, 'tools'):
                            logger.debug(f"🔧 DEBUG: tools.tools类型: {type(tools.tools)}")
                            logger.debug(f"🔧 DEBUG: tools.tools内容: {tools.tools}")
                            available_tools = [tool.name for tool in tools.tools]
                        elif hasattr(tools, '__iter__'):
                            logger.debug(f"🔧 DEBUG: tools是可迭代对象")
                            available_tools = [tool.name if hasattr(tool, 'name') else str(tool) for tool in tools]
                        else:
                            logger.debug(f"🔧 DEBUG: tools对象结构未知")

                        # 创建服务器信息对象
                        server_info = MCPServerInfo(
                            name=server_name,
                            client=client,
                            is_connected=True,  # 连接成功
                            available_tools=available_tools,
                            capabilities=server_config.get("capabilities", [])
                        )

                        self.server_info[server_name] = server_info
                        logger.info(f"✅ MCP client '{server_name}' connected with {len(available_tools)} tools: {available_tools}")

                except Exception as e:
                    logger.error(f"❌ Failed to connect MCP client '{server_name}': {e}")
                    # 创建未连接的服务器信息
                    server_info = MCPServerInfo(
                        name=server_name,
                        client=client,
                        is_connected=False,
                        available_tools=[],
                        capabilities=server_config.get("capabilities", []),
                        last_error=str(e)
                    )
                    self.server_info[server_name] = server_info
                    raise

            except Exception as e:
                logger.error(f"❌ Failed to initialize MCP client '{server_name}': {e}")
                # 创建失败的服务器信息
                server_info = MCPServerInfo(
                    name=server_name,
                    is_connected=False,
                    last_error=str(e)
                )
                self.server_info[server_name] = server_info
                raise

    async def call_tool(self, server_name: str, tool_name: str, parameters: Dict[str, Any], timeout: int = 60) -> Any:
        """调用指定服务器的工具 - 修复死锁问题"""
        if server_name not in self.clients:
            raise ValueError(f"MCP client '{server_name}' not found")

        client = self.clients[server_name]
        server_info = self.server_info.get(server_name)

        if not server_info or not server_info.is_connected:
            raise ValueError(f"MCP client '{server_name}' is not connected")

        try:
            import asyncio

            logger.info(f"🔧 Calling tool '{tool_name}' on '{server_name}' with timeout {timeout}s")
            logger.debug(f"🔧 Parameters: {parameters}")

            # 🔑 关键修复：正确使用async with client连接
            try:
                logger.debug(f"🔌 建立客户端连接并调用工具")

                # 🔑 修复：正确使用async with建立连接
                async with client:
                    logger.debug(f"🔌 客户端连接成功，调用工具")
                    result = await asyncio.wait_for(
                        client.call_tool(tool_name, parameters),
                        timeout=timeout
                    )

                    logger.info(f"✅ Tool '{tool_name}' completed successfully")
                    logger.debug(f"🔧 Tool result: {str(result)[:200]}...")
                    return result

            except asyncio.TimeoutError:
                # 超时错误单独处理
                raise
            except Exception as tool_error:
                # 工具调用错误
                logger.error(f"🚨 工具调用错误: {tool_error}")
                raise ValueError(f"Tool call failed: {tool_error}")

        except asyncio.TimeoutError:
            error_msg = f"Tool '{tool_name}' on '{server_name}' timed out after {timeout}s"
            logger.error(f"⏰ {error_msg}")
            if server_info:
                server_info.last_error = error_msg
            raise ValueError(error_msg)

        except Exception as e:
            error_msg = str(e)
            logger.error(f"❌ Failed to call tool '{tool_name}' on client '{server_name}': {error_msg}")

            # 🔑 修复：添加特定错误类型的处理
            if 'connection' in error_msg.lower():
                server_info.is_connected = False
                logger.warning(f"🔌 Marking server '{server_name}' as disconnected")

                if server_info:
                    server_info.last_error = error_msg
                raise

    def get_available_servers(self) -> List[str]:
        """获取可用的服务器列表"""
        return [name for name, info in self.server_info.items() if info.is_connected]

    def get_server_tools(self, server_name: str) -> List[str]:
        """获取指定服务器的工具列表"""
        if server_name in self.server_info:
            return self.server_info[server_name].available_tools or []
        return []

    def get_server_capabilities(self, server_name: str) -> List[str]:
        """获取指定服务器的能力列表"""
        if server_name in self.server_info:
            return self.server_info[server_name].capabilities or []
        return []

    def get_server_config(self, server_name: str) -> Dict[str, Any]:
        """获取指定服务器的配置信息"""
        try:
            config_data = self._load_config()
            if not config_data:
                return {}

            servers_config = config_data.get('mcpServers', {})
            server_config = servers_config.get(server_name, {})

            # 添加服务器信息
            if server_name in self.server_info:
                server_info = self.server_info[server_name]
                server_config.update({
                    'is_connected': server_info.is_connected,
                    'available_tools': server_info.available_tools or [],
                    'last_error': server_info.last_error
                })

            return server_config
        except Exception as e:
            logger.error(f"Failed to get server config for '{server_name}': {e}")
            return {}

    async def check_connection_health(self, server_name: str) -> bool:
        """检查MCP服务器连接健康状态"""
        if server_name not in self.clients:
            logger.warning(f"🔍 服务器'{server_name}'不存在")
            return False

        try:
            import asyncio
            client = self.clients[server_name]
            server_info = self.server_info.get(server_name)

            # 🔑 修复：直接ping，避免async with冲突
            await asyncio.wait_for(client.ping(), timeout=5)

            if server_info:
                server_info.is_connected = True
                server_info.last_error = None

            logger.info(f"✅ 服务器'{server_name}'连接健康")
            return True

        except Exception as e:
            logger.error(f"❌ 服务器'{server_name}'连接检查失败: {e}")
            if server_info:
                server_info.is_connected = False
                server_info.last_error = str(e)
            return False

    async def get_tools_for_server(self, server_name: str) -> List[str]:
            """动态获取服务器的工具列表"""
            if server_name not in self.clients:
                return []

            try:
                client = self.clients[server_name]
                # 🔑 修复：直接调用list_tools，避免async with冲突
                tools = await client.list_tools()
                tool_names = []
                if hasattr(tools, '__iter__'):
                    for tool in tools:
                        if hasattr(tool, 'name'):
                            tool_names.append(tool.name)
                        else:
                            tool_names.append(str(tool))

                # 更新缓存
                if server_name in self.server_info:
                    self.server_info[server_name].available_tools = tool_names

                return tool_names
            except Exception as e:
                logger.error(f"Failed to get tools for server '{server_name}': {e}")
                return []

    async def shutdown(self):
        """关闭所有MCP连接"""
        for server_name, client in self.clients.items():
            try:
                # FastMCP Client会自动处理连接关闭
                logger.info(f"✅ Closed connection to MCP client '{server_name}'")
            except Exception as e:
                logger.error(f"❌ Error closing connection to MCP client '{server_name}': {e}")

        self.clients.clear()
        self.server_info.clear()
        self._initialized = False

# 全局实例 - 使用新的简化管理器
fastmcp_manager = SimpleMCPManager()