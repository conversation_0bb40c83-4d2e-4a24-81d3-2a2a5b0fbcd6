"""
动态配置API路由
为前端提供动态配置信息
"""

import logging
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import Dict, Any, List, Optional

from ..services.complete_dynamic_type_manager import complete_dynamic_type_manager
from ..services.unified_service_adapter import unified_service_manager

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/api/config", tags=["dynamic-config"])

# 请求模型
class ParameterValidationRequest(BaseModel):
    provider: str
    capability: str
    parameters: Dict[str, Any]

@router.get("/service-types")
async def get_service_types():
    """获取所有服务类型"""
    try:
        service_types = complete_dynamic_type_manager.get_all_service_types()

        result = []
        for service_type in service_types:
            result.append({
                'name': service_type.name,
                'display_name': service_type.display_name,
                'description': service_type.description,
                'category': service_type.category,
                'providers': service_type.providers
            })

        return {"success": True, "data": result, "message": "服务类型获取成功"}

    except Exception as e:
        logger.error(f"❌ 获取服务类型失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取服务类型失败: {str(e)}")

@router.get("/service-providers")
async def get_service_providers():
    """获取所有服务提供商"""
    try:
        providers = complete_dynamic_type_manager.get_all_service_providers()

        result = []
        for provider in providers:
            result.append({
                'name': provider.name,
                'display_name': provider.display_name,
                'description': provider.description,
                'type': provider.type,
                'capabilities': provider.capabilities,
                'status': provider.status
            })

        return {"success": True, "data": result, "message": "服务提供商获取成功"}

    except Exception as e:
        logger.error(f"❌ 获取服务提供商失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取服务提供商失败: {str(e)}")

@router.get("/selector-mappings")
async def get_selector_mappings():
    """获取选择器映射"""
    try:
        mappings = complete_dynamic_type_manager.get_selector_mappings()
        return {"success": True, "data": mappings, "message": "选择器映射获取成功"}

    except Exception as e:
        logger.error(f"❌ 获取选择器映射失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取选择器映射失败: {str(e)}")

@router.get("/capability-providers")
async def get_capability_providers(capability: Optional[str] = None):
    """获取能力-提供商映射"""
    try:
        if capability:
            # 获取特定能力的提供商
            providers = complete_dynamic_type_manager.get_providers_for_capability(capability)
            result = {capability: providers}
        else:
            # 获取所有能力的提供商映射
            result = {}
            service_types = complete_dynamic_type_manager.get_all_service_types()

            for service_type in service_types:
                providers = complete_dynamic_type_manager.get_providers_for_capability(service_type.name)
                result[service_type.name] = providers

        return {"success": True, "data": result, "message": "能力提供商映射获取成功"}

    except Exception as e:
        logger.error(f"❌ 获取能力提供商映射失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取能力提供商映射失败: {str(e)}")

@router.get("/smart-parameters")
async def get_smart_parameters(provider: str, capability: str):
    """获取智能参数信息"""
    try:
        if not provider or not capability:
            raise HTTPException(status_code=400, detail="缺少必需参数: provider 和 capability")

        # 获取智能参数信息
        parameters = unified_service_manager.get_smart_parameters(provider, capability)

        result = []
        for param in parameters:
            param_info = {
                'name': param.name,
                'type': param.type.value,
                'description': param.description,
                'required': param.required,
                'current_constraints': param.current_constraints,
                'suggested_value': param.suggested_value,
                'validation_rules': param.validation_rules or []
            }

            if param.available_choices:
                param_info['available_choices'] = param.available_choices

            result.append(param_info)

        return {"success": True, "data": result, "message": "智能参数信息获取成功"}

    except Exception as e:
        logger.error(f"❌ 获取智能参数信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取智能参数信息失败: {str(e)}")

@router.post("/validate-parameters")
async def validate_parameters(request: ParameterValidationRequest):
    """验证参数"""
    try:
        if not request.provider or not request.capability:
            raise HTTPException(status_code=400, detail="缺少必需参数: provider 和 capability")

        # 验证参数
        validation_result = unified_service_manager.validate_parameters(
            request.provider, request.capability, request.parameters
        )

        return {"success": True, "data": validation_result, "message": "参数验证完成"}

    except Exception as e:
        logger.error(f"❌ 参数验证失败: {e}")
        raise HTTPException(status_code=500, detail=f"参数验证失败: {str(e)}")

@router.get("/service-categories")
async def get_service_categories():
    """获取服务类别"""
    try:
        service_types = complete_dynamic_type_manager.get_all_service_types()
        categories = {}

        for service_type in service_types:
            category = service_type.category
            if category not in categories:
                categories[category] = []

            categories[category].append({
                'name': service_type.name,
                'display_name': service_type.display_name,
                'description': service_type.description,
                'providers': service_type.providers
            })

        return {"success": True, "data": categories, "message": "服务类别获取成功"}

    except Exception as e:
        logger.error(f"❌ 获取服务类别失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取服务类别失败: {str(e)}")

@router.get("/provider-capabilities")
async def get_provider_capabilities(provider: Optional[str] = None):
    """获取提供商能力"""
    try:
        if provider:
            # 获取特定提供商的能力
            capabilities = complete_dynamic_type_manager.get_capabilities_for_provider(provider)
            result = {provider: capabilities}
        else:
            # 获取所有提供商的能力
            result = {}
            providers = complete_dynamic_type_manager.get_all_service_providers()

            for provider_obj in providers:
                result[provider_obj.name] = provider_obj.capabilities

        return {"success": True, "data": result, "message": "提供商能力获取成功"}

    except Exception as e:
        logger.error(f"❌ 获取提供商能力失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取提供商能力失败: {str(e)}")

@router.get("/configuration-summary")
async def get_configuration_summary():
    """获取配置摘要"""
    try:
        summary = complete_dynamic_type_manager.get_configuration_summary()

        # 添加统一服务管理器信息
        unified_capabilities = unified_service_manager.get_available_capabilities()
        summary['unified_capabilities'] = len(unified_capabilities)
        summary['total_adapters'] = len(unified_service_manager.adapters)

        return {"success": True, "data": summary, "message": "配置摘要获取成功"}

    except Exception as e:
        logger.error(f"❌ 获取配置摘要失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取配置摘要失败: {str(e)}")

@router.post("/reload")
async def reload_configuration():
    """重新加载配置"""
    try:
        # 重新加载动态类型管理器
        complete_dynamic_type_manager.reload_configuration()

        # 重新加载统一服务管理器
        unified_service_manager.reload_adapters()

        # 获取更新后的摘要
        summary = complete_dynamic_type_manager.get_configuration_summary()

        return {"success": True, "data": summary, "message": "配置重新加载成功"}

    except Exception as e:
        logger.error(f"❌ 配置重新加载失败: {e}")
        raise HTTPException(status_code=500, detail=f"配置重新加载失败: {str(e)}")

@router.get("/health")
async def health_check():
    """健康检查"""
    try:
        summary = complete_dynamic_type_manager.get_configuration_summary()

        health_status = {
            'status': 'healthy',
            'dynamic_types_loaded': summary['service_types'] > 0,
            'providers_loaded': summary['service_providers'] > 0,
            'adapters_loaded': len(unified_service_manager.adapters) > 0,
            'configuration_summary': summary
        }

        return {"success": True, "data": health_status, "message": "系统健康状态正常"}

    except Exception as e:
        logger.error(f"❌ 健康检查失败: {e}")
        raise HTTPException(status_code=500, detail=f"健康检查失败: {str(e)}")
