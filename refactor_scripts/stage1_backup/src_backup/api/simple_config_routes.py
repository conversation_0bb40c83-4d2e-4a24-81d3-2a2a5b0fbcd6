"""
简化配置API路由
为前端提供简化的服务配置信息，替代复杂的动态配置系统
"""

import logging
from fastapi import APIRouter
from typing import Dict, List, Any

from ..services.simplified_types import ServiceCapability, ServiceProvider

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/config", tags=["config"])

@router.get("/services")
async def get_services_config():
    """获取所有可用的服务配置"""
    try:
        # 硬编码的服务配置，基于简化架构
        services_config = {
            "text_generation": {
                "name": "文本生成",
                "description": "生成各种类型的文本内容",
                "providers": ["deepseek"],
                "parameters": {
                    "max_tokens": {"type": "number", "default": 1000, "min": 1, "max": 4000},
                    "temperature": {"type": "number", "default": 0.7, "min": 0.0, "max": 2.0}
                }
            },
            "translation": {
                "name": "翻译",
                "description": "文本翻译服务",
                "providers": ["deepseek"],
                "parameters": {
                    "target_language": {"type": "string", "default": "en", "choices": ["en", "zh", "ja", "ko", "fr", "de", "es"]},
                    "source_language": {"type": "string", "default": "auto"}
                }
            },
            "text_analysis": {
                "name": "文本分析",
                "description": "分析文本内容，提供洞察和建议",
                "providers": ["deepseek"],
                "parameters": {
                    "analysis_type": {"type": "string", "default": "general", "choices": ["general", "sentiment", "keywords", "summary"]}
                }
            },
            "speech_synthesis": {
                "name": "语音合成",
                "description": "将文本转换为语音",
                "providers": ["elevenlabs", "minimax"],
                "parameters": {
                    "voice_id": {"type": "string", "default": "default"},
                    "speed": {"type": "number", "default": 1.0, "min": 0.5, "max": 2.0}
                }
            },
            "image_generation": {
                "name": "图像生成",
                "description": "根据文本描述生成图像",
                "providers": ["doubao"],
                "parameters": {
                    "width": {"type": "number", "default": 1024, "choices": [512, 768, 1024]},
                    "height": {"type": "number", "default": 1024, "choices": [512, 768, 1024]},
                    "steps": {"type": "number", "default": 20, "min": 10, "max": 50}
                }
            },
            "video_generation": {
                "name": "视频生成",
                "description": "根据文本或图像生成视频",
                "providers": ["vidu"],
                "parameters": {
                    "duration": {"type": "number", "default": 4, "choices": [4, 8, 16]},
                    "fps": {"type": "number", "default": 24, "choices": [24, 30]}
                }
            }
        }

        return {
            "success": True,
            "data": services_config
        }

    except Exception as e:
        logger.error(f"Error getting services config: {e}")
        return {
            "success": False,
            "error": "Failed to get services configuration",
            "metadata": {"error_code": "CONFIG_ERROR"}
        }

@router.get("/providers")
async def get_providers_config():
    """获取所有可用的服务提供商配置"""
    try:
        providers_config = {
            "deepseek": {
                "name": "DeepSeek",
                "description": "DeepSeek AI服务",
                "capabilities": ["text_generation", "translation", "text_analysis"],
                "status": "active"
            },
            "elevenlabs": {
                "name": "ElevenLabs",
                "description": "高质量语音合成服务",
                "capabilities": ["speech_synthesis", "voice_cloning"],
                "status": "active"
            },
            "minimax": {
                "name": "MiniMax",
                "description": "MiniMax AI服务",
                "capabilities": ["speech_synthesis", "image_generation"],
                "status": "active"
            },
            "doubao": {
                "name": "豆包",
                "description": "字节跳动豆包AI服务",
                "capabilities": ["image_generation", "video_generation"],
                "status": "active"
            },
            "vidu": {
                "name": "Vidu",
                "description": "Vidu视频生成服务",
                "capabilities": ["video_generation"],
                "status": "active"
            }
        }

        return {
            "success": True,
            "data": providers_config
        }

    except Exception as e:
        logger.error(f"Error getting providers config: {e}")
        return {
            "success": False,
            "error": "Failed to get providers configuration",
            "metadata": {"error_code": "CONFIG_ERROR"}
        }

@router.get("/capabilities")
async def get_capabilities_config():
    """获取所有可用的服务能力配置"""
    try:
        capabilities_config = {}

        for capability in ServiceCapability:
            capabilities_config[capability.value] = {
                "name": capability.value.replace("_", " ").title(),
                "value": capability.value,
                "providers": []
            }

        # 添加提供商信息
        provider_capabilities = {
            ServiceProvider.DEEPSEEK: [ServiceCapability.TEXT_GENERATION, ServiceCapability.TRANSLATION, ServiceCapability.TEXT_ANALYSIS],
            ServiceProvider.ELEVENLABS: [ServiceCapability.SPEECH_SYNTHESIS],
            ServiceProvider.MINIMAX: [ServiceCapability.SPEECH_SYNTHESIS],
            ServiceProvider.DOUBAO: [ServiceCapability.IMAGE_GENERATION],
            ServiceProvider.VIDU: [ServiceCapability.VIDEO_GENERATION]
        }

        for provider, capabilities in provider_capabilities.items():
            for capability in capabilities:
                capabilities_config[capability.value]["providers"].append(provider.value)

        return {
            "success": True,
            "data": capabilities_config
        }

    except Exception as e:
        logger.error(f"Error getting capabilities config: {e}")
        return {
            "success": False,
            "error": "Failed to get capabilities configuration",
            "metadata": {"error_code": "CONFIG_ERROR"}
        }

@router.get("/unified_services.json")
async def get_unified_services_json():
    """兼容性端点：返回统一服务配置JSON格式"""
    try:
        # 为了兼容前端的配置加载，提供简化的配置格式
        unified_config = {
            "services": {
                "text_generation": {
                    "providers": ["deepseek"],
                    "default_provider": "deepseek"
                },
                "translation": {
                    "providers": ["deepseek"],
                    "default_provider": "deepseek"
                },
                "text_analysis": {
                    "providers": ["deepseek"],
                    "default_provider": "deepseek"
                },
                "speech_synthesis": {
                    "providers": ["elevenlabs", "minimax"],
                    "default_provider": "elevenlabs"
                },
                "image_generation": {
                    "providers": ["doubao"],
                    "default_provider": "doubao"
                },
                "video_generation": {
                    "providers": ["vidu"],
                    "default_provider": "vidu"
                }
            },
            "providers": {
                "deepseek": {"name": "DeepSeek", "status": "active"},
                "elevenlabs": {"name": "ElevenLabs", "status": "active"},
                "minimax": {"name": "MiniMax", "status": "active"},
                "doubao": {"name": "豆包", "status": "active"},
                "vidu": {"name": "Vidu", "status": "active"}
            }
        }

        return unified_config

    except Exception as e:
        logger.error(f"Error getting unified services config: {e}")
        return {"services": {}, "providers": {}}
