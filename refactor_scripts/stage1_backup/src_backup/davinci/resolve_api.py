"""
DaVinci Resolve API封装 - 完全重构版本
专为AI Co-pilot Pro优化，提供简洁高效的DaVinci Resolve集成
"""

import logging
import os
import time
import json
from typing import Dict, List, Optional, Any
from pathlib import Path
from dataclasses import dataclass
from enum import Enum

# DaVinci Resolve API导入
try:
    import DaVinciResolveScript as dvr_script
    RESOLVE_AVAILABLE = True
except ImportError:
    dvr_script = None
    RESOLVE_AVAILABLE = False

from ..core import get_config, APIError, ErrorCode

logger = logging.getLogger(__name__)

class ConnectionState(Enum):
    """连接状态枚举"""
    DISCONNECTED = "disconnected"
    CONNECTING = "connecting"
    CONNECTED = "connected"
    ERROR = "error"

class APIVersion(Enum):
    """支持的API版本"""
    V18 = "18"
    V19 = "19"
    UNKNOWN = "unknown"

@dataclass
class ProjectInfo:
    """项目信息数据类"""
    name: str
    fps: str
    width: int
    height: int
    timeline_count: int
    current_timeline: Optional[str] = None

@dataclass
class MediaClip:
    """媒体片段信息"""
    name: str
    duration: str
    file_path: Optional[str] = None
    clip_type: Optional[str] = None

@dataclass
class TimelineInfo:
    """时间线信息"""
    name: str
    duration: int
    start_frame: int
    end_frame: int
    video_tracks: int
    audio_tracks: int

@dataclass
class RenderJob:
    """渲染任务信息"""
    job_id: str
    status: str
    progress: float
    output_path: str
    estimated_time: Optional[int] = None

@dataclass
class FrameInfo:
    """静帧信息"""
    frame_number: int
    timecode: str
    file_path: str
    width: int
    height: int
    format: str = "PNG"

@dataclass
class PlayheadInfo:
    """播放头信息"""
    current_frame: int
    current_timecode: str
    timeline_duration: int
    timeline_start: int
    timeline_end: int

@dataclass
class SubtitleInfo:
    """字幕信息"""
    text: str
    start_time: float
    end_time: float
    track_index: int
    style: Optional[Dict[str, Any]] = None
    language: str = "zh-CN"

@dataclass
class AudioTrackInfo:
    """音频轨道信息"""
    track_index: int
    track_name: str
    duration: float
    sample_rate: int
    channels: int
    file_path: Optional[str] = None

@dataclass
class SmartMarker:
    """智能标记信息"""
    frame_number: int
    marker_type: str  # person, object, scene_change, quality_issue, content
    confidence: float
    description: str
    color: str
    metadata: Dict[str, Any]

class ResolveAPIError(APIError):
    """DaVinci Resolve API专用错误"""
    def __init__(self, message: str, error_code: ErrorCode, details: Dict[str, Any] = None):
        super().__init__(message, error_code, details)
        self.api_version = None
        self.connection_state = None

class DaVinciResolveAPI:
    """DaVinci Resolve API封装类 - 完全重构版本

    专为AI Co-pilot Pro优化，提供：
    - 简化的API调用机制
    - 更好的错误处理
    - AI功能集成支持
    - 版本兼容性检查
    - 专门的AI内容管理
    """

    def __init__(self):
        # 核心对象
        self.resolve = None
        self.project_manager = None
        self.current_project = None
        self.media_pool = None
        self.timeline = None

        # 状态管理
        self._connection_state = ConnectionState.DISCONNECTED
        self._api_version = APIVersion.UNKNOWN
        self._last_error = None

        # 配置
        self._connection_timeout = get_config('davinci.connection_timeout', 30)
        self._auto_save = get_config('davinci.auto_save', True)
        self._backup_projects = get_config('davinci.backup_projects', True)

        # AI集成相关
        self._ai_generated_folder = "AI Generated Content"
        self._ai_metadata_prefix = "AI_"

        # 静帧导出相关
        self._frame_export_folder = "Frame Exports"
        self._frame_export_format = "PNG"
        self._frame_export_quality = "Best"

        # 深度集成相关
        self._subtitle_track_name = "AI Generated Subtitles"
        self._audio_export_folder = "Audio Exports"
        self._smart_markers_enabled = True

        logger.info("DaVinci Resolve API initialized (New Version)")

    # ==================== 连接管理 ====================

    async def initialize(self) -> bool:
        """初始化DaVinci Resolve连接"""
        if not RESOLVE_AVAILABLE:
            self._connection_state = ConnectionState.ERROR
            self._last_error = "DaVinci Resolve Python API not available"
            logger.error(self._last_error)
            return False

        try:
            self._connection_state = ConnectionState.CONNECTING
            logger.info("Connecting to DaVinci Resolve...")

            # 获取Resolve实例
            self.resolve = dvr_script.scriptapp("Resolve")
            if not self.resolve:
                raise ResolveAPIError(
                    "Failed to connect to DaVinci Resolve. Please ensure DaVinci Resolve is running.",
                    ErrorCode.API_CONNECTION_ERROR
                )

            # 检测API版本
            self._api_version = self._detect_api_version()
            logger.info(f"Detected DaVinci Resolve API version: {self._api_version.value}")

            # 获取项目管理器
            self.project_manager = self.resolve.GetProjectManager()
            if not self.project_manager:
                raise ResolveAPIError(
                    "Failed to get Project Manager",
                    ErrorCode.API_CONNECTION_ERROR
                )

            # 尝试获取当前项目（可选）
            await self._load_current_project()

            # 设置连接成功状态
            self._connection_state = ConnectionState.CONNECTED
            self._last_error = None

            logger.info("✅ Successfully connected to DaVinci Resolve")
            return True

        except Exception as e:
            self._connection_state = ConnectionState.ERROR
            self._last_error = str(e)
            logger.error(f"Failed to initialize DaVinci Resolve API: {e}")
            return False

    async def _load_current_project(self) -> None:
        """加载当前项目信息"""
        try:
            self.current_project = self.project_manager.GetCurrentProject()
            if self.current_project:
                # 获取媒体池
                self.media_pool = self.current_project.GetMediaPool()

                # 获取当前时间线
                self.timeline = self.current_project.GetCurrentTimeline()

                # 确保AI生成内容文件夹存在
                await self._ensure_ai_folder()

                project_name = self.current_project.GetName()
                logger.info(f"Loaded project: {project_name}")
            else:
                logger.info("No current project loaded")
        except Exception as e:
            logger.warning(f"Failed to load current project: {e}")

    def _detect_api_version(self) -> APIVersion:
        """检测API版本"""
        if not self.resolve:
            return APIVersion.UNKNOWN

        try:
            version = self.resolve.GetVersion()
            if version:
                major_version = version.split('.')[0]
                if major_version == "18":
                    return APIVersion.V18
                elif major_version == "19":
                    return APIVersion.V19
            return APIVersion.UNKNOWN
        except Exception:
            return APIVersion.UNKNOWN

    def _validate_connection(self) -> bool:
        """验证连接状态"""
        return (self._connection_state == ConnectionState.CONNECTED and
                self.resolve is not None)

    def _validate_project(self) -> bool:
        """验证项目状态"""
        return (self._validate_connection() and
                self.current_project is not None)

    # ==================== 状态查询 ====================

    def is_connected(self) -> bool:
        """检查是否连接到DaVinci Resolve"""
        return self._validate_connection()

    def is_project_loaded(self) -> bool:
        """检查是否有项目加载"""
        return self._validate_project()

    def get_connection_state(self) -> ConnectionState:
        """获取连接状态"""
        return self._connection_state

    def get_api_version(self) -> APIVersion:
        """获取API版本"""
        return self._api_version

    def get_last_error(self) -> Optional[str]:
        """获取最后一次错误信息"""
        return self._last_error

    # ==================== 系统信息 ====================

    async def get_system_info(self) -> Dict[str, Any]:
        """获取DaVinci Resolve系统信息"""
        if not self._validate_connection():
            raise ResolveAPIError(
                "Not connected to DaVinci Resolve",
                ErrorCode.API_CONNECTION_ERROR
            )

        try:
            info = {
                'version': self.resolve.GetVersion() if self.resolve else "Unknown",
                'product_name': self.resolve.GetProductName() if self.resolve else "Unknown",
                'api_version': self._api_version.value,
                'connection_state': self._connection_state.value,
                'current_project': None,
                'project_count': 0,
                'timeline_count': 0,
                'media_pool_clips': 0,
                'ai_folder_exists': False
            }

            # 项目信息
            if self.current_project:
                info['current_project'] = self.current_project.GetName()
                info['timeline_count'] = self.current_project.GetTimelineCount()

                # 媒体池信息
                if self.media_pool:
                    root_folder = self.media_pool.GetRootFolder()
                    if root_folder:
                        clips = root_folder.GetClipList()
                        info['media_pool_clips'] = len(clips) if clips else 0

                        # 检查AI文件夹是否存在
                        subfolders = root_folder.GetSubFolderList()
                        info['ai_folder_exists'] = any(
                            folder.GetName() == self._ai_generated_folder
                            for folder in subfolders
                        )

            # 项目管理器信息
            if self.project_manager:
                try:
                    projects = self.project_manager.GetProjectListInCurrentFolder()
                    info['project_count'] = len(projects) if projects else 0
                except Exception:
                    info['project_count'] = 0

            return info

        except Exception as e:
            raise ResolveAPIError(
                f"Failed to get system info: {e}",
                ErrorCode.API_SERVER_ERROR
            )

    # ==================== 项目管理 ====================

    async def get_project_info(self) -> ProjectInfo:
        """获取当前项目信息"""
        if not self._validate_project():
            raise ResolveAPIError(
                "No project loaded",
                ErrorCode.INVALID_REQUEST
            )

        try:
            # 基本项目信息
            name = self.current_project.GetName()
            fps = self.current_project.GetSetting('timelineFrameRate') or "25"
            width = int(self.current_project.GetSetting('timelineResolutionWidth') or 1920)
            height = int(self.current_project.GetSetting('timelineResolutionHeight') or 1080)
            timeline_count = self.current_project.GetTimelineCount()

            # 当前时间线信息
            current_timeline = None
            if self.timeline:
                current_timeline = self.timeline.GetName()

            return ProjectInfo(
                name=name,
                fps=fps,
                width=width,
                height=height,
                timeline_count=timeline_count,
                current_timeline=current_timeline
            )

        except Exception as e:
            raise ResolveAPIError(
                f"Failed to get project info: {e}",
                ErrorCode.API_SERVER_ERROR
            )

    async def get_timeline_info(self) -> Optional[TimelineInfo]:
        """获取当前时间线详细信息"""
        if not self.timeline:
            return None

        try:
            return TimelineInfo(
                name=self.timeline.GetName(),
                duration=self.timeline.GetDuration(),
                start_frame=self.timeline.GetStartFrame(),
                end_frame=self.timeline.GetEndFrame(),
                video_tracks=self.timeline.GetTrackCount('video'),
                audio_tracks=self.timeline.GetTrackCount('audio')
            )
        except Exception as e:
            logger.warning(f"Failed to get timeline info: {e}")
            return None

    # ==================== AI集成功能 ====================

    async def _ensure_ai_folder(self) -> None:
        """确保AI生成内容文件夹存在"""
        if not self.media_pool:
            return

        try:
            root_folder = self.media_pool.GetRootFolder()
            subfolders = root_folder.GetSubFolderList()

            # 检查是否已存在AI文件夹
            ai_folder_exists = any(
                folder.GetName() == self._ai_generated_folder
                for folder in subfolders
            )

            if not ai_folder_exists:
                self.media_pool.AddSubFolder(root_folder, self._ai_generated_folder)
                logger.info(f"Created AI content folder: {self._ai_generated_folder}")
        except Exception as e:
            logger.warning(f"Failed to create AI folder: {e}")

    async def import_ai_generated_media(self, file_paths: List[str], content_type: str = "general") -> Dict[str, Any]:
        """导入AI生成的媒体文件到专用文件夹"""
        if not self._validate_project():
            raise ResolveAPIError(
                "No project loaded",
                ErrorCode.INVALID_REQUEST
            )

        try:
            # 确保AI文件夹存在
            await self._ensure_ai_folder()

            # 获取AI文件夹
            root_folder = self.media_pool.GetRootFolder()
            ai_folder = None
            for folder in root_folder.GetSubFolderList():
                if folder.GetName() == self._ai_generated_folder:
                    ai_folder = folder
                    break

            if not ai_folder:
                raise ResolveAPIError(
                    "AI content folder not found",
                    ErrorCode.API_SERVER_ERROR
                )

            # 导入文件
            imported_clips = []
            failed_imports = []

            for file_path in file_paths:
                if not os.path.exists(file_path):
                    failed_imports.append({
                        'file': file_path,
                        'error': 'File not found'
                    })
                    continue

                try:
                    clips = self.media_pool.ImportMedia([file_path])
                    if clips:
                        for clip in clips:
                            # 添加AI元数据
                            clip.SetClipProperty(f"{self._ai_metadata_prefix}ContentType", content_type)
                            clip.SetClipProperty(f"{self._ai_metadata_prefix}ImportTime", str(int(time.time())))

                            imported_clips.append({
                                'name': clip.GetName(),
                                'duration': clip.GetClipProperty('Duration'),
                                'file_path': file_path,
                                'content_type': content_type
                            })

                        logger.info(f"✅ Imported AI media: {file_path}")
                    else:
                        failed_imports.append({
                            'file': file_path,
                            'error': 'Import failed'
                        })
                except Exception as e:
                    failed_imports.append({
                        'file': file_path,
                        'error': str(e)
                    })

            return {
                'imported_count': len(imported_clips),
                'failed_count': len(failed_imports),
                'imported_clips': imported_clips,
                'failed_imports': failed_imports,
                'ai_folder': self._ai_generated_folder
            }

        except Exception as e:
            raise ResolveAPIError(
                f"Failed to import AI media: {e}",
                ErrorCode.API_SERVER_ERROR
            )

    async def create_ai_timeline(self, timeline_name: str, ai_scenes: List[Dict[str, Any]]) -> bool:
        """基于AI分析结果创建时间线"""
        if not self._validate_project():
            raise ResolveAPIError(
                "No project loaded",
                ErrorCode.INVALID_REQUEST
            )

        try:
            # 创建新时间线
            timeline = self.media_pool.CreateEmptyTimeline(timeline_name)
            if not timeline:
                raise ResolveAPIError(
                    f"Failed to create timeline: {timeline_name}",
                    ErrorCode.API_SERVER_ERROR
                )

            # 设置为当前时间线
            self.current_project.SetCurrentTimeline(timeline)
            self.timeline = timeline

            # 添加AI元数据到时间线
            timeline.SetMarker(
                0,
                "Blue",
                "AI Generated Timeline",
                f"Created from {len(ai_scenes)} AI-analyzed scenes"
            )

            logger.info(f"✅ Created AI timeline: {timeline_name}")
            return True

        except Exception as e:
            raise ResolveAPIError(
                f"Failed to create AI timeline: {e}",
                ErrorCode.API_SERVER_ERROR
            )

    # ==================== 静帧联动功能 ====================

    async def get_playhead_info(self) -> Optional[PlayheadInfo]:
        """获取当前播放头信息 - 符合官方API"""
        if not self.timeline:
            return None

        try:
            # ✅ 使用官方API获取当前时间码
            current_timecode = self.timeline.GetCurrentTimecode()

            # ✅ 从时间码计算帧数（需要项目帧率）
            fps = float(self.current_project.GetSetting('timelineFrameRate') or 24)
            current_frame = self._timecode_to_frame(current_timecode, fps)

            # ✅ 获取时间线范围 - 使用官方API
            start_frame = self.timeline.GetStartFrame()
            end_frame = self.timeline.GetEndFrame()

            return PlayheadInfo(
                current_frame=current_frame,
                current_timecode=current_timecode,
                timeline_duration=end_frame - start_frame,
                timeline_start=start_frame,
                timeline_end=end_frame
            )

        except Exception as e:
            logger.warning(f"Failed to get playhead info: {e}")
            return None

    def _timecode_to_frame(self, timecode: str, fps: float) -> int:
        """将时间码转换为帧数"""
        try:
            # 解析时间码格式 "HH:MM:SS:FF"
            parts = timecode.split(':')
            if len(parts) == 4:
                hours, minutes, seconds, frames = map(int, parts)
                total_frames = (hours * 3600 + minutes * 60 + seconds) * fps + frames
                return int(total_frames)
            return 0
        except (ValueError, IndexError):
            logger.warning(f"Invalid timecode format: {timecode}")
            return 0

    async def export_current_frame(self, output_dir: str = None, filename: str = None) -> Optional[FrameInfo]:
        """导出当前播放头位置的静帧 - 符合官方API"""
        if not self._validate_project():
            raise ResolveAPIError("No project loaded", ErrorCode.INVALID_REQUEST)

        try:
            # 设置输出路径
            if not output_dir:
                project_info = await self.get_project_info()
                output_dir = str(Path.cwd() / "output" / "frames" / project_info.name)

            Path(output_dir).mkdir(parents=True, exist_ok=True)

            if not filename:
                timestamp = int(time.time())
                filename = f"frame_{timestamp}.png"  # 官方API只支持PNG

            # 确保文件扩展名正确
            if not filename.lower().endswith(('.png', '.jpg', '.jpeg', '.tiff', '.bmp')):
                filename += '.png'

            output_path = str(Path(output_dir) / filename)

            # ✅ 使用官方API导出当前帧
            success = self.current_project.ExportCurrentFrameAsStill(output_path)

            if success and Path(output_path).exists():
                playhead_info = await self.get_playhead_info()
                project_info = await self.get_project_info()

                frame_info = FrameInfo(
                    frame_number=playhead_info.current_frame if playhead_info else 0,
                    timecode=playhead_info.current_timecode if playhead_info else "00:00:00:00",
                    file_path=output_path,
                    width=project_info.width,
                    height=project_info.height,
                    format=Path(filename).suffix.upper().lstrip('.')
                )

                logger.info(f"✅ Exported frame: {output_path}")
                return frame_info

            raise ResolveAPIError("Failed to export frame", ErrorCode.API_SERVER_ERROR)

        except Exception as e:
            raise ResolveAPIError(f"Failed to export current frame: {e}", ErrorCode.API_SERVER_ERROR)

    async def export_frame_for_ai(self, ai_service_type: str = "image_generation") -> Optional[str]:
        """导出当前帧用于AI服务，返回文件路径"""
        try:
            # 创建AI专用的帧导出目录
            ai_frames_dir = Path.cwd() / "output" / "ai_frames"
            ai_frames_dir.mkdir(parents=True, exist_ok=True)

            # 导出当前帧
            frame_info = await self.export_current_frame(
                str(ai_frames_dir),
                f"ai_reference_{ai_service_type}_{int(time.time())}.png"
            )

            if frame_info:
                # 添加AI元数据标记
                metadata = {
                    'ai_service_type': ai_service_type,
                    'export_time': time.time(),
                    'frame_number': frame_info.frame_number,
                    'timecode': frame_info.timecode
                }

                # 可以将元数据保存到同名的JSON文件
                metadata_path = Path(frame_info.file_path).with_suffix('.json')
                with open(metadata_path, 'w') as f:
                    import json
                    json.dump(metadata, f, indent=2)

                logger.info(f"✅ Exported frame for AI ({ai_service_type}): {frame_info.file_path}")
                return frame_info.file_path

            return None

        except Exception as e:
            logger.error(f"Failed to export frame for AI: {e}")
            return None

    # ==================== 渲染功能 ====================

    async def export_timeline(self, export_path: str, filename: str, render_settings: Dict[str, Any] = None) -> Dict[str, Any]:
        """导出时间线"""
        if not self.timeline:
            raise ResolveAPIError(
                "No timeline loaded",
                ErrorCode.INVALID_REQUEST
            )

        try:
            # 确保导出目录存在
            Path(export_path).mkdir(parents=True, exist_ok=True)

            # 设置默认渲染设置
            default_settings = {
                'TargetDir': export_path,
                'CustomName': filename,
                'ExportVideo': True,
                'ExportAudio': True,
                'FormatWidth': 1920,
                'FormatHeight': 1080,
                'FrameRate': 24.0
            }

            if render_settings:
                default_settings.update(render_settings)

            # 创建渲染任务
            job_id = self.current_project.AddRenderJob()
            if not job_id:
                raise ResolveAPIError(
                    "Failed to create render job",
                    ErrorCode.API_SERVER_ERROR
                )

            # 设置渲染设置
            self.current_project.SetRenderSettings(default_settings)

            # 开始渲染
            success = self.current_project.StartRendering(job_id)
            if not success:
                raise ResolveAPIError(
                    "Failed to start rendering",
                    ErrorCode.API_SERVER_ERROR
                )

            logger.info(f"✅ Started rendering job: {job_id}")

            return {
                'job_id': job_id,
                'export_path': export_path,
                'filename': filename,
                'status': 'started',
                'settings': default_settings
            }

        except Exception as e:
            raise ResolveAPIError(
                f"Failed to export timeline: {e}",
                ErrorCode.API_SERVER_ERROR
            )

    async def get_render_status(self, job_id: Optional[str] = None) -> Dict[str, Any]:
        """获取渲染状态"""
        if not self._validate_project():
            raise ResolveAPIError(
                "No project loaded",
                ErrorCode.INVALID_REQUEST
            )

        try:
            if job_id:
                # 获取特定任务状态
                status = self.current_project.GetRenderJobStatus(job_id)
                return {
                    'job_id': job_id,
                    'status': status.get('JobStatus', 'Unknown'),
                    'progress': status.get('CompletionPercentage', 0),
                    'estimated_time': status.get('EstimatedTimeRemainingInMs', 0)
                }
            else:
                # 获取所有渲染任务状态
                jobs = []
                render_jobs = self.current_project.GetRenderJobList()

                for job in render_jobs:
                    job_status = self.current_project.GetRenderJobStatus(job['JobId'])
                    jobs.append({
                        'job_id': job['JobId'],
                        'status': job_status.get('JobStatus', 'Unknown'),
                        'progress': job_status.get('CompletionPercentage', 0),
                        'estimated_time': job_status.get('EstimatedTimeRemainingInMs', 0)
                    })

                return {
                    'jobs': jobs,
                    'total_jobs': len(jobs)
                }

        except Exception as e:
            raise ResolveAPIError(
                f"Failed to get render status: {e}",
                ErrorCode.API_SERVER_ERROR
            )

    # ==================== 深度集成功能 ====================

    async def extract_audio_for_ai(self, track_index: int = 1) -> Optional[str]:
        """提取音频轨道用于AI分析 - 改进版本"""
        if not self._validate_project():
            raise ResolveAPIError("No project loaded", ErrorCode.INVALID_REQUEST)

        try:
            # 创建音频导出目录
            audio_export_dir = Path.cwd() / "output" / "audio_exports"
            audio_export_dir.mkdir(parents=True, exist_ok=True)

            # 生成音频文件名
            project_info = await self.get_project_info()
            timestamp = int(time.time())
            audio_filename = f"{project_info.name}_track{track_index}_{timestamp}.wav"
            audio_path = str(audio_export_dir / audio_filename)

            logger.info(f"🎵 Starting audio extraction to: {audio_path}")

            # 跳过获取当前渲染设置（方法不存在）
            logger.debug("Using simplified render settings")

            # ✅ 第一步：设置渲染格式和编码器（必须先设置）
            logger.info("🔧 Setting render format and codec first...")
            format_success = self.current_project.SetCurrentRenderFormatAndCodec('Wave', 'LinearPCM')
            if format_success:
                logger.info("✅ Set render format: Wave, codec: LinearPCM")
            else:
                logger.warning("⚠️ Failed to set Wave/LinearPCM format, trying alternatives...")
                # 尝试其他可能的组合
                alternatives = [
                    ('Wave', 'PCM'),
                ]
                for fmt, codec in alternatives:
                    try:
                        success = self.current_project.SetCurrentRenderFormatAndCodec(fmt, codec)
                        if success:
                            logger.info(f"✅ Alternative format set: {fmt}, codec: {codec}")
                            format_success = True
                            break
                    except Exception as e:
                        logger.debug(f"Failed {fmt}/{codec}: {e}")
                        continue

            # ✅ 第二步：设置渲染参数（不包含AudioCodec，因为已通过格式设置）
            audio_export_settings = {
                'TargetDir': str(audio_export_dir),
                'CustomName': Path(audio_filename).stem,
                'ExportVideo': False,
                'ExportAudio': True,
                'AudioSampleRate': 48000,
                'AudioBitDepth': 24,
                'SelectAllFrames': True,  # 导出整个时间线
            }

            logger.info(f"🔧 Audio export settings: {audio_export_settings}")

            # 设置渲染设置
            logger.info("🔧 Setting render settings for audio export...")
            success = self.current_project.SetRenderSettings(audio_export_settings)
            if not success:
                logger.error("❌ Failed to set render settings")
                raise ResolveAPIError("Failed to set render settings", ErrorCode.API_SERVER_ERROR)

            # 设置完成，准备渲染
            logger.debug("✅ Audio export settings configured")

            # ✅ 使用正确的渲染流程：先设置，再添加任务，最后开始渲染
            logger.info("📋 Adding render job to queue...")
            job_id = self.current_project.AddRenderJob()
            if not job_id:
                logger.error("❌ Failed to create render job")
                raise ResolveAPIError("Failed to create render job", ErrorCode.API_SERVER_ERROR)

            logger.info(f"✅ Render job created with ID: {job_id}")

            # ✅ 开始渲染（尝试不同的方法）
            logger.info(f"🚀 Starting render process for job: {job_id}")

            # 方法1: 尝试启动指定任务
            try:
                success = self.current_project.StartRendering(job_id)
                if success:
                    logger.info("✅ Started rendering with job ID")
                else:
                    raise Exception("StartRendering(job_id) returned False")
            except Exception as e1:
                logger.warning(f"Method 1 failed: {e1}")

                # 方法2: 尝试启动所有任务（但只有我们的任务在队列中）
                try:
                    success = self.current_project.StartRendering()
                    if success:
                        logger.info("✅ Started rendering with StartRendering()")
                    else:
                        raise Exception("StartRendering() returned False")
                except Exception as e2:
                    logger.error(f"Method 2 also failed: {e2}")

                    # 清理失败的任务
                    try:
                        self.current_project.DeleteRenderJob(job_id)
                    except:
                        pass
                    raise ResolveAPIError("Failed to start rendering", ErrorCode.API_SERVER_ERROR)

            logger.info("⏳ Waiting for render completion...")

            # 等待渲染完成
            audio_path = await self._wait_for_audio_render_completion(audio_path, job_id)

            # 跳过恢复原始渲染设置（不需要）
            logger.debug("✅ Audio extraction completed, no settings to restore")

            if audio_path:
                logger.info(f"✅ Audio extraction completed: {audio_path}")
                return audio_path

            raise ResolveAPIError("Audio rendering failed or timed out", ErrorCode.API_SERVER_ERROR)

        except Exception as e:
            logger.error(f"❌ Audio extraction failed: {e}")
            raise ResolveAPIError(f"Failed to extract audio for AI: {e}", ErrorCode.API_SERVER_ERROR)

    async def _wait_for_audio_render_completion(self, audio_path: str, job_id: str, timeout: int = 300) -> Optional[str]:
        """等待音频渲染完成 - 改进版本"""
        import asyncio

        start_time = time.time()
        check_interval = 3  # 每3秒检查一次
        last_progress = -1

        logger.info(f"⏳ Waiting for audio render completion: {audio_path}")
        logger.info(f"📋 Job ID: {job_id}")

        while time.time() - start_time < timeout:
            try:
                elapsed = time.time() - start_time

                # 检查渲染状态
                try:
                    render_status = self.current_project.GetRenderJobStatus(job_id)
                    if render_status:
                        job_status = render_status.get('JobStatus', 'Unknown')
                        progress = render_status.get('CompletionPercentage', 0)

                        # 显示进度更新
                        if progress != last_progress:
                            logger.info(f"🎬 Render progress: {progress}% - Status: {job_status} ({elapsed:.1f}s)")
                            last_progress = progress

                        if job_status == 'Complete':
                            logger.info("✅ Render job completed successfully")
                            # 等待文件写入完成
                            await asyncio.sleep(2)
                            if Path(audio_path).exists():
                                file_size = Path(audio_path).stat().st_size
                                logger.info(f"✅ Audio file created: {audio_path} ({file_size} bytes)")
                                return audio_path
                            else:
                                logger.warning("⚠️ Render completed but file not found")

                        elif job_status == 'Failed':
                            error_msg = render_status.get('Error', 'Unknown error')
                            logger.error(f"❌ Render job failed: {error_msg}")
                            logger.error(f"Full status: {render_status}")
                            break

                        elif job_status == 'Rendering':
                            # 正在渲染，继续等待
                            pass
                        elif job_status == 'Queued':
                            logger.info("📋 Render job is queued, waiting to start...")
                        else:
                            logger.debug(f"🔄 Render status: {job_status} ({progress}%)")

                except Exception as status_error:
                    logger.warning(f"⚠️ Error getting render status: {status_error}")

                # 检查文件是否已经存在（有时状态更新延迟）
                if Path(audio_path).exists():
                    file_size = Path(audio_path).stat().st_size
                    if file_size > 0:
                        # 等待文件大小稳定
                        await asyncio.sleep(2)
                        if Path(audio_path).exists():
                            new_size = Path(audio_path).stat().st_size
                            if new_size == file_size:
                                logger.info(f"✅ Audio file detected and stable: {audio_path} ({file_size} bytes)")
                                return audio_path

                await asyncio.sleep(check_interval)

            except Exception as e:
                logger.warning(f"⚠️ Error during render wait: {e}")
                await asyncio.sleep(check_interval)

        logger.error(f"❌ Audio render timeout after {timeout} seconds")

        # 尝试清理失败的任务
        try:
            self.current_project.DeleteRenderJob(job_id)
            logger.info(f"🗑️ Cleaned up failed render job: {job_id}")
        except Exception as cleanup_error:
            logger.warning(f"Failed to cleanup render job: {cleanup_error}")

        return None

    async def add_ai_subtitles(self, subtitles: List[SubtitleInfo]) -> bool:
        """添加AI生成的字幕到时间线"""
        if not self.timeline:
            raise ResolveAPIError(
                "No timeline loaded",
                ErrorCode.INVALID_REQUEST
            )

        try:
            # 创建字幕文件
            subtitle_dir = Path.cwd() / "output" / "subtitles"
            subtitle_dir.mkdir(parents=True, exist_ok=True)

            timestamp = int(time.time())
            srt_filename = f"ai_subtitles_{timestamp}.srt"
            srt_path = subtitle_dir / srt_filename

            # 生成SRT格式字幕
            with open(srt_path, 'w', encoding='utf-8') as f:
                for i, subtitle in enumerate(subtitles, 1):
                    start_time = self._seconds_to_srt_time(subtitle.start_time)
                    end_time = self._seconds_to_srt_time(subtitle.end_time)

                    f.write(f"{i}\n")
                    f.write(f"{start_time} --> {end_time}\n")
                    f.write(f"{subtitle.text}\n\n")

            # 导入字幕文件到媒体池
            subtitle_clips = self.media_pool.ImportMedia([str(srt_path)])
            if subtitle_clips:
                # 添加字幕到时间线
                # 注意：具体的字幕添加方法可能因DaVinci版本而异
                logger.info(f"✅ Added AI subtitles: {len(subtitles)} entries")
                return True

            return False

        except Exception as e:
            raise ResolveAPIError(
                f"Failed to add AI subtitles: {e}",
                ErrorCode.API_SERVER_ERROR
            )

    def _seconds_to_srt_time(self, seconds: float) -> str:
        """将秒数转换为SRT时间格式"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        milliseconds = int((seconds % 1) * 1000)
        return f"{hours:02d}:{minutes:02d}:{secs:02d},{milliseconds:03d}"

    async def add_smart_markers(self, markers: List[SmartMarker]) -> bool:
        """添加AI生成的智能标记 - 符合官方API"""
        if not self.timeline:
            raise ResolveAPIError("No timeline loaded", ErrorCode.INVALID_REQUEST)

        try:
            added_count = 0

            for marker in markers:
                try:
                    # ✅ 使用官方API签名 - AddMarker而不是SetMarker
                    success = self.timeline.AddMarker(
                        marker.frame_number,      # frameId
                        marker.color,             # color
                        f"[AI] {marker.marker_type}",  # name
                        f"{marker.description} (置信度: {marker.confidence:.2f})",  # note
                        1.0,                      # duration
                        json.dumps(marker.metadata) if marker.metadata else ""  # customData
                    )

                    if success:
                        added_count += 1

                except Exception as e:
                    logger.warning(f"Failed to add marker at frame {marker.frame_number}: {e}")

            logger.info(f"✅ Added {added_count} smart markers")
            return added_count > 0

        except Exception as e:
            raise ResolveAPIError(f"Failed to add smart markers: {e}", ErrorCode.API_SERVER_ERROR)

    async def analyze_timeline_content(self) -> Dict[str, Any]:
        """分析时间线内容，生成智能标记"""
        if not self._validate_project():
            raise ResolveAPIError(
                "No project loaded",
                ErrorCode.INVALID_REQUEST
            )

        try:
            timeline_info = await self.get_timeline_info()
            if not timeline_info:
                return {}

            # 基础内容分析
            analysis = {
                'timeline_name': timeline_info.name,
                'duration': timeline_info.duration,
                'video_tracks': timeline_info.video_tracks,
                'audio_tracks': timeline_info.audio_tracks,
                'suggested_markers': [],
                'content_summary': {
                    'total_frames': timeline_info.duration,
                    'estimated_scenes': max(1, timeline_info.duration // 300),  # 假设每5秒一个场景
                    'has_audio': timeline_info.audio_tracks > 0,
                    'complexity': 'medium'
                }
            }

            # 生成一些示例智能标记
            if timeline_info.duration > 0:
                # 开始标记
                analysis['suggested_markers'].append(SmartMarker(
                    frame_number=0,
                    marker_type="content",
                    confidence=1.0,
                    description="视频开始",
                    color="Green",
                    metadata={"type": "start", "auto_generated": True}
                ))

                # 中点标记
                if timeline_info.duration > 600:  # 超过10秒
                    mid_point = timeline_info.duration // 2
                    analysis['suggested_markers'].append(SmartMarker(
                        frame_number=mid_point,
                        marker_type="scene_change",
                        confidence=0.8,
                        description="可能的场景转换点",
                        color="Blue",
                        metadata={"type": "scene_change", "auto_generated": True}
                    ))

                # 结束标记
                analysis['suggested_markers'].append(SmartMarker(
                    frame_number=timeline_info.duration - 1,
                    marker_type="content",
                    confidence=1.0,
                    description="视频结束",
                    color="Red",
                    metadata={"type": "end", "auto_generated": True}
                ))

            return analysis

        except Exception as e:
            raise ResolveAPIError(
                f"Failed to analyze timeline content: {e}",
                ErrorCode.API_SERVER_ERROR
            )

    # ==================== 清理和关闭 ====================

    async def close(self):
        """关闭DaVinci Resolve连接"""
        try:
            if self._auto_save and self.current_project:
                try:
                    self.current_project.SaveProject()
                    logger.info("Project auto-saved before closing")
                except Exception as e:
                    logger.warning(f"Failed to auto-save project: {e}")

            # 清理引用
            self.resolve = None
            self.project_manager = None
            self.current_project = None
            self.media_pool = None
            self.timeline = None

            # 更新状态
            self._connection_state = ConnectionState.DISCONNECTED
            self._last_error = None

            logger.info("✅ DaVinci Resolve connection closed")

        except Exception as e:
            logger.error(f"Error closing DaVinci Resolve connection: {e}")

# 全局DaVinci Resolve API实例
resolve_api = DaVinciResolveAPI()
