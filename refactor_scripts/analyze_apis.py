#!/usr/bin/env python3
"""
分析现有API结构和路由
"""

import os
import ast
from pathlib import Path

def analyze_api_files():
    """分析API文件"""
    project_root = Path('/Library/Application Support/Blackmagic Design/DaVinci Resolve/Fusion/Scripts/Utility/DaVinci AI Co-pilot Pro')
    api_dir = project_root / 'src' / 'api'

    if not api_dir.exists():
        print("❌ API目录不存在")
        return

    print("📊 API文件分析结果:")
    print("=" * 50)

    routes = []
    endpoints = []

    for api_file in api_dir.glob('*.py'):
        if api_file.name == '__init__.py':
            continue

        try:
            with open(api_file, 'r', encoding='utf-8') as f:
                content = f.read()

            # 简单的路由检测
            if '@router.' in content or '@app.' in content:
                print(f"✅ {api_file.name}: 包含路由定义")

                # 提取路由信息
                lines = content.split('\n')
                for i, line in enumerate(lines):
                    if '@router.' in line or '@app.' in line:
                        # 提取HTTP方法和路径
                        if 'post' in line.lower():
                            method = 'POST'
                        elif 'get' in line.lower():
                            method = 'GET'
                        elif 'put' in line.lower():
                            method = 'PUT'
                        elif 'delete' in line.lower():
                            method = 'DELETE'
                        else:
                            method = 'UNKNOWN'

                        # 尝试提取路径
                        if '"' in line:
                            path = line.split('"')[1] if '"' in line else 'unknown'
                        elif "'" in line:
                            path = line.split("'")[1] if "'" in line else 'unknown'
                        else:
                            path = 'unknown'

                        routes.append({
                            'file': api_file.name,
                            'method': method,
                            'path': path,
                            'line': i + 1
                        })
            else:
                print(f"⚠️  {api_file.name}: 未发现路由定义")

        except Exception as e:
            print(f"❌ {api_file.name}: 分析失败 - {e}")

    print(f"\n🛣️  发现的路由:")
    print("-" * 30)

    if routes:
        for route in routes:
            print(f"  {route['method']} {route['path']} ({route['file']}:{route['line']})")
    else:
        print("  未发现任何路由")

    print(f"\n📈 统计信息:")
    print(f"  - API文件数: {len(list(api_dir.glob('*.py'))) - 1}")  # 排除__init__.py
    print(f"  - 路由总数: {len(routes)}")

    # 生成统一建议
    print(f"\n💡 统一建议:")
    print("  📁 建议创建统一API端点:")
    print("    - POST /api/ai/process - 统一AI处理端点")
    print("    - GET /api/ai/capabilities - 获取AI能力列表")
    print("    - GET /api/ai/status - 获取服务状态")
    print("    - POST /api/ai/batch - 批量处理请求")

    return routes

if __name__ == "__main__":
    analyze_api_files()
