#!/usr/bin/env python3
"""
迁移enhanced_ai_services的使用到simplified_ai_services
"""

import os
import re
from pathlib import Path

def migrate_file(file_path):
    """迁移单个文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        original_content = content

        # 替换导入语句
        content = re.sub(
            r'from\s+\.\.?services\.enhanced_ai_services\s+import\s+.*',
            'from ..services.simplified_ai_services import SimplifiedAIServiceManager',
            content
        )

        content = re.sub(
            r'import\s+.*enhanced_ai_services.*',
            'from ..services.simplified_ai_services import SimplifiedAIServiceManager',
            content
        )

        # 替换类使用
        content = re.sub(r'Enhanced\w*AIService\w*', 'SimplifiedAIServiceManager', content)

        # 如果内容有变化，写回文件
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"  ✅ 迁移完成: {file_path}")
            return True
        else:
            print(f"  ⏭️  无需迁移: {file_path}")
            return False

    except Exception as e:
        print(f"  ❌ 迁移失败: {file_path} - {e}")
        return False

def main():
    project_root = Path('/Library/Application Support/Blackmagic Design/DaVinci Resolve/Fusion/Scripts/Utility/DaVinci AI Co-pilot Pro')

    # 查找所有使用enhanced_ai_services的文件
    python_files = list(project_root.glob('src/**/*.py'))

    migrated_count = 0
    for file_path in python_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            if 'enhanced_ai_services' in content or 'Enhanced' in content:
                if migrate_file(file_path):
                    migrated_count += 1
        except Exception as e:
            print(f"  ⚠️  检查文件失败: {file_path} - {e}")

    print(f"\n📊 迁移完成: {migrated_count} 个文件")

if __name__ == "__main__":
    main()
