#!/usr/bin/env python3
"""
分析前端冗余代码
"""

import os
import re
from pathlib import Path
from collections import defaultdict

def analyze_frontend_redundancy():
    project_root = Path('/Library/Application Support/Blackmagic Design/DaVinci Resolve/Fusion/Scripts/Utility/DaVinci AI Co-pilot Pro')

    print('🔍 分析前端冗余代码...')
    print('=' * 50)

    # 查找所有前端文件
    js_files = list(project_root.rglob('*.js'))
    js_files = [f for f in js_files if not str(f).endswith('.min.js') and 'node_modules' not in str(f)]

    print(f'📊 发现 {len(js_files)} 个JavaScript文件')

    # 分析文件大小和复杂度
    file_stats = []
    for js_file in js_files:
        try:
            with open(js_file, 'r', encoding='utf-8') as f:
                content = f.read()

            lines = len(content.split('\n'))
            functions = len(re.findall(r'function\s+\w+|\w+\s*=\s*function|\w+\s*=>|class\s+\w+', content))
            api_calls = len(re.findall(r'fetch\(|axios\.|\.ajax', content))

            file_stats.append({
                'file': js_file.relative_to(project_root),
                'lines': lines,
                'functions': functions,
                'api_calls': api_calls,
                'size_kb': js_file.stat().st_size / 1024
            })
        except Exception as e:
            continue

    # 按行数排序
    file_stats.sort(key=lambda x: x['lines'], reverse=True)

    print('\n📋 文件复杂度分析（按行数排序）:')
    print('-' * 60)
    for i, stat in enumerate(file_stats[:15]):  # 显示前15个最大的文件
        print(f'{i+1:2d}. {stat["file"]} ({stat["lines"]}行, {stat["functions"]}函数, {stat["api_calls"]}API调用, {stat["size_kb"]:.1f}KB)')

    # 查找可能的冗余文件
    print('\n🔍 查找可能的冗余文件:')
    print('-' * 40)

    redundant_candidates = []

    # 1. 查找相似命名的文件
    file_names = [f.name for f in js_files]
    name_groups = defaultdict(list)

    for js_file in js_files:
        base_name = js_file.stem.lower()
        # 移除版本号、后缀等
        clean_name = re.sub(r'[-_](v\d+|\d+|old|new|backup|copy)', '', base_name)
        name_groups[clean_name].append(js_file)

    for name, files in name_groups.items():
        if len(files) > 1:
            redundant_candidates.extend(files[1:])  # 保留第一个，其他标记为冗余候选
            print(f'  ⚠️  相似命名文件组 "{name}": {len(files)} 个文件')
            for f in files:
                print(f'    - {f.relative_to(project_root)}')

    # 2. 查找空文件或几乎空的文件
    empty_files = [stat for stat in file_stats if stat['lines'] < 10]
    if empty_files:
        print(f'\n  📝 发现 {len(empty_files)} 个几乎空的文件（<10行）:')
        for stat in empty_files:
            print(f'    - {stat["file"]} ({stat["lines"]}行)')

    # 3. 查找未使用的函数（简单检测）
    print('\n🔍 查找可能未使用的大文件:')
    print('-' * 30)

    large_files = [stat for stat in file_stats if stat['lines'] > 500]
    for stat in large_files:
        print(f'  📄 {stat["file"]} - {stat["lines"]}行，建议检查是否可以拆分或删除')

    # 4. 分析API调用模式
    print('\n🔍 API调用模式分析:')
    print('-' * 25)

    api_patterns = defaultdict(list)
    for js_file in js_files:
        try:
            with open(js_file, 'r', encoding='utf-8') as f:
                content = f.read()

            # 查找API调用
            fetch_calls = re.findall(r'fetch\(["\']([^"\']*)["\']', content)
            for call in fetch_calls:
                api_patterns[call].append(js_file.relative_to(project_root))
        except:
            continue

    # 显示API调用统计
    for api, files in api_patterns.items():
        if len(files) > 1:
            print(f'  📡 API "{api}" 被 {len(files)} 个文件调用')

    # 生成清理建议
    print('\n💡 清理建议:')
    print('-' * 15)

    total_redundant = len(redundant_candidates) + len(empty_files)
    if total_redundant > 0:
        print(f'  🗑️  建议删除 {total_redundant} 个冗余文件')
        redundant_size = sum(stat["size_kb"] for stat in file_stats
                           if any(str(c.relative_to(project_root)) == str(stat["file"]) for c in redundant_candidates))
        empty_size = sum(stat["size_kb"] for stat in empty_files)
        print(f'  📦 预计可减少 {redundant_size + empty_size:.1f}KB')
    else:
        print('  ✅ 未发现明显的冗余文件')

    if large_files:
        print(f'  📋 建议重构 {len(large_files)} 个大文件')

    return {
        'redundant_candidates': redundant_candidates,
        'empty_files': empty_files,
        'large_files': large_files,
        'file_stats': file_stats
    }

if __name__ == "__main__":
    result = analyze_frontend_redundancy()
    print(f'\n✅ 前端冗余分析完成！')
