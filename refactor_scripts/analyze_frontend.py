#!/usr/bin/env python3
"""
分析前端代码结构和组件
"""

import os
import re
from pathlib import Path

def analyze_frontend_files():
    """分析前端文件"""
    project_root = Path('/Library/Application Support/Blackmagic Design/DaVinci Resolve/Fusion/Scripts/Utility/DaVinci AI Co-pilot Pro')

    print("📊 前端文件分析结果:")
    print("=" * 50)

    # 查找前端相关文件
    frontend_patterns = ['*.js', '*.html', '*.css', '*.vue', '*.jsx', '*.tsx']
    frontend_files = []

    for pattern in frontend_patterns:
        frontend_files.extend(project_root.rglob(pattern))

    # 按类型分类
    js_files = [f for f in frontend_files if f.suffix in ['.js', '.jsx', '.ts', '.tsx']]
    html_files = [f for f in frontend_files if f.suffix == '.html']
    css_files = [f for f in frontend_files if f.suffix == '.css']

    print(f"✅ JavaScript文件: {len(js_files)}")
    for js_file in js_files[:10]:  # 只显示前10个
        rel_path = js_file.relative_to(project_root)
        print(f"    - {rel_path}")

    print(f"✅ HTML文件: {len(html_files)}")
    for html_file in html_files[:10]:
        rel_path = html_file.relative_to(project_root)
        print(f"    - {rel_path}")

    print(f"✅ CSS文件: {len(css_files)}")
    for css_file in css_files[:10]:
        rel_path = css_file.relative_to(project_root)
        print(f"    - {rel_path}")

    # 分析JavaScript文件中的API调用
    print(f"\n🔍 API调用分析:")
    print("-" * 30)

    api_calls = []
    for js_file in js_files:
        try:
            with open(js_file, 'r', encoding='utf-8') as f:
                content = f.read()

            # 查找API调用模式
            fetch_calls = re.findall(r'fetch\([\'"]([^\'"]+)[\'"]', content)
            axios_calls = re.findall(r'axios\.[get|post|put|delete]+\([\'"]([^\'"]+)[\'"]', content)

            for call in fetch_calls + axios_calls:
                api_calls.append({
                    'file': js_file.relative_to(project_root),
                    'url': call,
                    'type': 'fetch' if call in fetch_calls else 'axios'
                })

        except Exception as e:
            continue

    if api_calls:
        for call in api_calls[:10]:  # 只显示前10个
            print(f"  📡 {call['type']}: {call['url']} ({call['file']})")
    else:
        print("  未发现API调用")

    print(f"\n📈 统计信息:")
    print(f"  - 前端文件总数: {len(frontend_files)}")
    print(f"  - JavaScript文件: {len(js_files)}")
    print(f"  - HTML文件: {len(html_files)}")
    print(f"  - CSS文件: {len(css_files)}")
    print(f"  - API调用数: {len(api_calls)}")

    # 生成优化建议
    print(f"\n💡 优化建议:")
    print("  📁 建议创建统一前端结构:")
    print("    - unified-ai-client.js - 统一AI服务客户端")
    print("    - ui-components.js - 通用UI组件")
    print("    - config-manager.js - 前端配置管理")
    print("    - main.js - 统一入口文件")

    return {
        'js_files': js_files,
        'html_files': html_files,
        'css_files': css_files,
        'api_calls': api_calls
    }

if __name__ == "__main__":
    analyze_frontend_files()
