文件删除安全性检查清单
====================

⚠️  高风险文件（删除前需特别小心）:
- src/services/enhanced_ai_services.py
- src/services/unified_service_adapter.py  
- src/types/base_types.py
- config/legacy_config.json
- config/service_config.json

🔍 删除前必须检查的依赖关系:
1. 检查是否有其他文件导入这些模块
2. 检查配置文件是否引用这些服务
3. 检查前端JavaScript是否调用相关API
4. 检查是否有测试文件依赖这些模块

✅ 安全删除步骤:
1. 搜索所有对目标文件的引用
2. 逐一替换或删除引用
3. 运行测试确保功能正常
4. 提交Git更改作为检查点
5. 删除目标文件
6. 再次运行测试验证

🚨 紧急回滚指令:
如果删除后出现问题，立即运行:
bash refactor_scripts/quick_rollback.sh
