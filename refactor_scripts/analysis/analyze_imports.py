#!/usr/bin/env python3
import os
import re
import json
from pathlib import Path

def analyze_imports(project_root):
    """分析Python文件的导入关系"""
    dependencies = {}
    all_files = []

    # 收集所有Python文件
    for root, dirs, files in os.walk(project_root):
        if 'refactor_scripts' in root:
            continue
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                rel_path = os.path.relpath(file_path, project_root)
                all_files.append(rel_path)

    print(f"📁 分析 {len(all_files)} 个Python文件...")

    # 分析每个文件的导入
    for file_path in all_files:
        full_path = os.path.join(project_root, file_path)
        dependencies[file_path] = {
            'imports': [],
            'from_imports': [],
            'local_imports': [],
            'external_imports': []
        }

        try:
            with open(full_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # 匹配import语句
            import_pattern = r'^import\s+([^\s#]+)'
            from_pattern = r'^from\s+([^\s#]+)\s+import'

            for line in content.split('\n'):
                line = line.strip()

                # 普通import
                import_match = re.match(import_pattern, line)
                if import_match:
                    module = import_match.group(1)
                    dependencies[file_path]['imports'].append(module)

                    # 判断是否为本地导入
                    if module.startswith('.') or any(module.startswith(f) for f in ['src', 'config', 'web']):
                        dependencies[file_path]['local_imports'].append(module)
                    else:
                        dependencies[file_path]['external_imports'].append(module)

                # from import
                from_match = re.match(from_pattern, line)
                if from_match:
                    module = from_match.group(1)
                    dependencies[file_path]['from_imports'].append(module)

                    # 判断是否为本地导入
                    if module.startswith('.') or any(module.startswith(f) for f in ['src', 'config', 'web']):
                        dependencies[file_path]['local_imports'].append(module)
                    else:
                        dependencies[file_path]['external_imports'].append(module)

        except Exception as e:
            print(f"⚠️  分析文件失败 {file_path}: {e}")

    return dependencies, all_files

def find_circular_dependencies(dependencies):
    """查找循环依赖"""
    circular_deps = []

    def has_path(start, end, visited=None):
        if visited is None:
            visited = set()
        if start in visited:
            return False
        if start == end:
            return True

        visited.add(start)
        for dep in dependencies.get(start, {}).get('local_imports', []):
            # 转换模块名为文件路径
            dep_file = convert_module_to_file(dep)
            if dep_file and has_path(dep_file, end, visited.copy()):
                return True
        return False

    def convert_module_to_file(module):
        """将模块名转换为文件路径"""
        if module.startswith('.'):
            return None  # 相对导入需要更复杂的处理

        # 简单转换
        parts = module.split('.')
        for file_path in dependencies.keys():
            if any(part in file_path for part in parts):
                return file_path
        return None

    # 检查每对文件是否存在循环依赖
    files = list(dependencies.keys())
    for i, file1 in enumerate(files):
        for file2 in files[i+1:]:
            if has_path(file1, file2) and has_path(file2, file1):
                circular_deps.append((file1, file2))

    return circular_deps

def generate_dependency_report(dependencies, all_files, output_dir):
    """生成依赖关系报告"""

    # 1. 基本统计
    stats = {
        'total_files': len(all_files),
        'files_with_local_imports': 0,
        'files_with_external_imports': 0,
        'total_local_imports': 0,
        'total_external_imports': 0
    }

    for file_path, deps in dependencies.items():
        if deps['local_imports']:
            stats['files_with_local_imports'] += 1
            stats['total_local_imports'] += len(deps['local_imports'])
        if deps['external_imports']:
            stats['files_with_external_imports'] += 1
            stats['total_external_imports'] += len(deps['external_imports'])

    # 2. 生成报告
    report_path = os.path.join(output_dir, 'dependency_report.txt')
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write("DaVinci AI Co-pilot Pro 依赖关系分析报告\n")
        f.write("=" * 50 + "\n\n")

        f.write("📊 基本统计:\n")
        f.write(f"  总文件数: {stats['total_files']}\n")
        f.write(f"  有本地导入的文件: {stats['files_with_local_imports']}\n")
        f.write(f"  有外部导入的文件: {stats['files_with_external_imports']}\n")
        f.write(f"  本地导入总数: {stats['total_local_imports']}\n")
        f.write(f"  外部导入总数: {stats['total_external_imports']}\n\n")

        f.write("📁 文件详细依赖:\n")
        f.write("-" * 30 + "\n")

        for file_path, deps in dependencies.items():
            f.write(f"\n📄 {file_path}:\n")
            if deps['local_imports']:
                f.write(f"  本地导入: {', '.join(deps['local_imports'])}\n")
            if deps['external_imports']:
                f.write(f"  外部导入: {', '.join(set(deps['external_imports']))}\n")

    # 3. 保存JSON格式的详细数据
    json_path = os.path.join(output_dir, 'dependencies.json')
    with open(json_path, 'w', encoding='utf-8') as f:
        json.dump(dependencies, f, indent=2, ensure_ascii=False)

    print(f"✅ 依赖关系报告已生成: {report_path}")
    print(f"✅ 详细数据已保存: {json_path}")

    return stats

if __name__ == "__main__":
    project_root = "/Library/Application Support/Blackmagic Design/DaVinci Resolve/Fusion/Scripts/Utility/DaVinci AI Co-pilot Pro"
    output_dir = os.path.join(project_root, "refactor_scripts", "analysis")

    print("🔍 开始分析依赖关系...")
    dependencies, all_files = analyze_imports(project_root)

    print("📊 生成依赖关系报告...")
    stats = generate_dependency_report(dependencies, all_files, output_dir)

    print("🔄 查找循环依赖...")
    circular_deps = find_circular_dependencies(dependencies)

    if circular_deps:
        print(f"⚠️  发现 {len(circular_deps)} 个循环依赖:")
        for dep in circular_deps:
            print(f"  {dep[0]} ↔ {dep[1]}")
    else:
        print("✅ 未发现循环依赖")

    print("\n🎉 依赖关系分析完成!")
